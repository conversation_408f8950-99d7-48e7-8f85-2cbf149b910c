# samples:
# helmfile sync -f helmfile_ai.yaml.gotmpl
---
environments:
  default:
    values:
      #- domain: "dh.artemis-ia.fr"
      - domain: "kosmos.athea"
      #- domain: "kosmos.wip"
---
releases:
  # - name: translate
  #   namespace: kosmos-ai-restricted
  #   createNamespace: true
  #   wait: true
  #   chart: ../../libretranslate/libretranslate
  #   labels:
  #     app: libretranslate
  #   values:
  #     - ingress:
  #         enabled: true
  #         host: translate.{{.StateValues.domain }}
  #         annotations:
  #           cert-manager.io/cluster-issuer: letsencrypt-prod
  #         tls:
  #           - hosts:
  #               - translate.{{.StateValues.domain }}
  #             secretName: translate-tls-secret

  - name: open-webui-secrets
    namespace: kosmos-ai
    chart: ../../open-webui/open-webui-secrets
    values:
      - trustedCA: "ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/tls.crt"
    labels:
      app: open-webui

  - name: keycloakimporter-open-webui
    namespace: kosmos-iam
    labels:
      app: open-webui
    needs:
      #- kosmos-iam/keycloakimporter-init-realm-kosmos
      - kosmos-ai/open-webui-secrets
    chart: ../../keycloakimporter/keycloakimporter
    values:
      - values_templates/open-webui/import-client-open-webui.yaml.gotmpl

  - name: open-webui
    namespace: kosmos-ai
    createNamespace: true
    wait: true
    chart: ../../open-webui/open-webui
    skipDeps: true
    needs:
      - kosmos-ai/open-webui-secrets
    labels:
      app: open-webui
    values:
      - ../../open-webui/values/values.yaml
      # do not set when using kserve
      #- ../../open-webui/values/values-ollama.yaml
      - ingress:
          host: "open-webui.{{.StateValues.domain }}"
        # -> Only with letsencrypt (will rm custo certs related)
        #   annotations:
        #     cert-manager.io/cluster-issuer: letsencrypt-prod
        #     kosmos-studio.athea.tech/description: AI interface
        #     kosmos-studio.athea.tech/name: OI
        # volumes: []
        # volumeMounts:
        #   initContainer: []
        #   container: []
        # extraEnvVars: []
        # <- Only with letsencrypt
        sso:
          oidc:
            clientId: "ref+k8s://v1/Secret/kosmos-ai/open-webui-oidc-secret/clientId"
            providerUrl: https://auth.{{.StateValues.domain }}/realms/kosmos/.well-known/openid-configuration
          roleManagement:
            rolesClaim: "resource_access.ref+k8s://v1/Secret/kosmos-ai/open-webui-oidc-secret/clientId+.roles"
    hooks:
      # label for current namespace
      - events: ["presync"]
        command: "kubectl"
        args:
          - "apply"
          - "-f"
          - "namespaces/kosmos-ai.yaml"

  - name: kserve-crd
    namespace: kosmos-ai-restricted
    chart: ../../model-serving/kserve-crd
    labels:
      app: model-serving

  - name: kserve
    namespace: kosmos-ai-restricted
    chart: ../../model-serving/kserve
    needs: [kosmos-ai-restricted/kserve-crd]
    labels:
      app: model-serving
    values:
      - ../../model-serving/values/values.yaml

  # basic sa for test (use yourn own fine grained one)
  - name: kserve-sa
    namespace: kosmos-ai-restricted
    chart: ../../model-serving/kserve-sa
    needs: [kosmos-ai-restricted/kserve-crd]
    labels:
      app: model-serving
    values:
      - accessKeyId: "ref+k8s://v1/Secret/kosmos-s3/minio-secrets/accessKey"
        secretAccessKey: "ref+k8s://v1/Secret/kosmos-s3/minio-secrets/secretKey"

  # # will create a lakefs user and a lakefs db in pg, generate a password
  # # and store infos as kubernetes secret
  # - name: lakefs-initdb
  #   namespace: kosmos-sql
  #   wait: true
  #   waitForJobs: true
  #   chart: ../../init-datastore/initpg
  #   labels:
  #     app: lakefs
  #   values:
  #     - pgSecretName: pgcluster-superuser
  #       appDbName: lakefs
  #       appDbUser: lakefs

  # - name: lakefs-inits3
  #   namespace: kosmos-s3
  #   wait: true
  #   waitForJobs: true
  #   chart: ../../init-datastore/inits3
  #   labels:
  #     app: lakefs
  #   values:
  #     - appBucketUser: lakefs
  #       appBucketName: lakefs

  # - name: lakefs-secrets
  #   namespace: kosmos-data
  #   wait: true
  #   waitForJobs: true
  #   chart: ../../lakefs/lakefs-secrets
  #   needs: [kosmos-s3/lakefs-inits3]
  #   labels:
  #     app: lakefs
  #   values:
  #     - s3AccessKey: ref+k8s://v1/Secret/kosmos-s3/lakefs-inits3-secret/app_bucket_user
  #       s3SecretKey: ref+k8s://v1/Secret/kosmos-s3/lakefs-inits3-secret/app_bucket_password

  # - name: lakefs
  #   namespace: kosmos-data
  #   chart: ../../lakefs/lakefs
  #   labels:
  #     app: lakefs
  #   needs: [kosmos-sql/lakefs-initdb, kosmos-s3/lakefs-inits3]
  #   values:
  #     - ../../lakefs/values/values.yaml
  #     - ingress:
  #         hosts:
  #           - host: lakefs.{{ .StateValues.domain }}
  #             paths:
  #               - /
  #         tls:
  #           - secretName: lakefs-secret-tls
  #             hosts:
  #               - lakefs.{{ .StateValues.domain }}
  #       secrets:
  #         databaseConnectionString: postgres://lakefs:ref+k8s://v1/Secret/kosmos-sql/lakefs-initdb-secret/<EMAIL>-sql:5432/lakefs

  # # Gen argo-workflows secrets
  # - name: argo-workflows-secrets
  #   namespace: kosmos-data
  #   chart: ../../argoworkflows/argo-workflows-secrets
  #   labels:
  #     app: argoworkflows

  # # Create argo-workflows client in iam
  # - name: keycloakimporter-argo-workflows
  #   namespace: kosmos-data
  #   chart: ../../keycloakimporter/keycloakimporter
  #   needs: [kosmos-data/argo-workflows-secrets]
  #   wait: true
  #   waitForJobs: true
  #   labels:
  #     app: argoworkflows
  #   values:
  #     - ./values_templates/argoworkflows/import-argoworkflows.yaml.gotmpl

  # # Argo workflows
  # - name: argo-workflows
  #   namespace: kosmos-data
  #   chart: ../../argoworkflows/argo-workflows
  #   needs: [kosmos-data/keycloakimporter-argo-workflows]
  #   wait: true
  #   waitForJobs: true
  #   labels:
  #     app: argoworkflows
  #   values:
  #     - ../../argoworkflows/values/values.yaml
  #     - server:
  #         sso:
  #           issuer: https://auth.{{ .StateValues.domain }}/realms/kosmos
  #           redirectUrl: https:///argo-workflows.{{ .StateValues.domain }}/oauth2/callback
  #         ingress:
  #           hosts:
  #             - argo-workflows.{{ .StateValues.domain }}
  #           tls:
  #             - secretName: argo-workflows-secret-tls
  #               hosts:
  #                 - argo-workflows.{{ .StateValues.domain }}

  # ### MLFLOW
  # - name: mlflow-inits3
  #   namespace: kosmos-s3
  #   labels:
  #     app: mlflow
  #   chart: ../../init-datastore/inits3
  #   values:
  #     - appBucketName: mlflow
  #     - appBucketUserPrefix: mlflow

  # - name: mlflow-initpg
  #   namespace: kosmos-sql
  #   labels:
  #     app: mlflow
  #   chart: ../../init-datastore/initpg
  #   values:
  #     - appDbName: mlflow mlflow_auth
  #       appDbUserPrefix: mlflow
  #       pgSecretName: pgcluster-superuser

  # - name: mlflow-secrets
  #   namespace: kosmos-data
  #   labels:
  #     app: mlflow
  #   needs:
  #     - kosmos-sql/mlflow-initpg
  #     - kosmos-s3/mlflow-inits3
  #   chart: ../../mlflow/mlflow-secrets
  #   values:
  #     - postgres:
  #         username: ref+k8s://v1/Secret/kosmos-sql/mlflow-initpg-secret/app_db_user
  #         password: ref+k8s://v1/Secret/kosmos-sql/mlflow-initpg-secret/app_db_password
  #     - minio:
  #         accessKey: ref+k8s://v1/Secret/kosmos-s3/mlflow-inits3-secret/app_bucket_user
  #         secretKey: ref+k8s://v1/Secret/kosmos-s3/mlflow-inits3-secret/app_bucket_password

  # - name: keycloakimporter-mlflow
  #   namespace: kosmos-data
  #   labels:
  #     app: mlflow
  #   needs:
  #     - kosmos-data/mlflow-secrets
  #   chart: ../../keycloakimporter/keycloakimporter
  #   values:
  #     - values_templates/mlflow/oidc-values.yaml.gotmpl

  # - name: mlflow
  #   namespace: kosmos-data
  #   labels:
  #     app: mlflow
  #   needs:
  #     - kosmos-data/keycloakimporter-mlflow
  #   chart: ../../mlflow/mlflow
  #   values:
  #     - values_templates/mlflow/default-values.yaml.gotmpl
  #     - ingress:
  #         annotations:
  #           #cert-manager.io/cluster-issuer: letsencrypt-prod
  #           cert-manager.io/cluster-issuer: kosmos-ca-issuer
  #           kosmos-studio.athea.tech/description: MLFlow
  #           kosmos-studio.athea.tech/name: MLFlow
