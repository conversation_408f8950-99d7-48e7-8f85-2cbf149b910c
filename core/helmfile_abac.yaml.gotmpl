# samples:
# helmfile sync -f helmfile_abac.yaml.gotmpl
---
environments:
  default:
    values:
      - domain: "kosmos.athea"
---
releases:
  - name: iad-s3
    namespace: kosmos-abac
    createNamespace: true
    wait: true
    chart: ../../dcs/helm_charts/iad
    labels:
      app: iad-s3
    values:
      - admin: adminsysteme,adminfra
      - s3:
          scheme: http
          host: s3-cluster-hl.kosmos-s3.svc.cluster.local
          port: 9000
          accessKey: "ref+k8s://v1/Secret/kosmos-s3/minio-secrets/accessKey"
          secretKey: "ref+k8s://v1/Secret/kosmos-s3/minio-secrets/secretKey"
      - ingress:
          enabled: true
          host: "iad-s3.{{ .StateValues.domain }}"
          className: ""
          annotations:
            cert-manager.io/cluster-issuer: kosmos-ca-issuer
            kosmos-studio.athea.tech/ignore: "true"
          tls:
            - hosts:
                - "iad-s3.{{ .StateValues.domain }}"
              secretName: iad-s3-tls

  - name: dcs
    namespace: kosmos-abac
    createNamespace: true
    chart: ../../dcs/helm_charts/dcs
    labels:
      app: dcs
    values:
      - resources:
          limits: {}
          requests:
            cpu: 500m
            memory: 512Mi
      - env:
          LOG_LEVEL: INFO
          S3_ENDPOINT: http://s3-cluster-hl.kosmos-s3.svc.cluster.local:9000
          S3_ACCESS_KEY: "ref+k8s://v1/Secret/kosmos-s3/minio-secrets/accessKey"
          S3_SECRET_KEY: "ref+k8s://v1/Secret/kosmos-s3/minio-secrets/secretKey"

  - name: keycloakimporter-kosmos-studio
    namespace: kosmos-iam
    labels:
      app: kosmos-studio
    needs:
      #- kosmos-iam/keycloakimporter-init-realm-kosmos
    chart: ../../keycloakimporter/keycloakimporter
    values:
      - values_templates/kosmos-studio/import-client-kosmos-studio.yaml.gotmpl
