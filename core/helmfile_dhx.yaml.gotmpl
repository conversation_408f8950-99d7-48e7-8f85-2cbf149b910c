# samples:
# helmfile sync -f helmfile_dhx.yaml.gotmpl -l core=true
# helmfile sync -f helmfile_dhx.yaml.gotmpl -l app=postgresql
---
environments:
  default:
    values:
      #- domain: "dh.artemis-ia.fr"
      - domain: "kosmos.athea"
      #- domain: "kosmos.wip"
---
releases:
  - name: monitoringcrds
    namespace: kosmos-monitoring
    chart: ../../monitoring/kube-prometheus-stack/charts/crds
    wait: true
    waitForJobs: true
    skipDeps: true

  # pg operator only
  - name: cnpg
    namespace: kosmos-sql
    createNamespace: true
    chart: ../../postgresql/cloudnative-pg
    needs: [ kosmos-monitoring/monitoringcrds ]
    skipDeps: true
    wait: true
    waitForJobs: true
    labels:
      app: postgresql
    values:
      - ../../postgresql/values/values-operator.yaml

  # will use default storage class
  - name: pgcluster
    namespace: kosmos-sql
    createNamespace: true
    wait: true
    waitForJobs: true
    chart: ../../postgresql/cluster
    needs: [ kosmos-sql/cnpg ]
    labels:
      app: postgresql
    values:
      - ../../postgresql/values/values-cluster-fat.yaml
      - cluster:
          instances: 1
          storage:
            size: 48Gi
            #size: 10Gi
          resources:
            limits:
              cpu: 4000m
              memory: 8Gi
            requests:
              cpu: 4000m
              memory: 8Gi

  # iam
  - name: keycloak-operator
    namespace: kosmos-iam
    createNamespace: true
    wait: true
    chart: ../../keycloak/keycloak-operator
    labels:
      app: keycloak

  - name: keycloak-cluster
    namespace: kosmos-iam
    needs:
      - kosmos-iam/keycloak-operator
      - kosmos-sql/pgcluster
    chart: ../../keycloak/keycloak-cluster
    values:
      - domain: {{ .StateValues.domain }}
        db:
          host: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/host+.kosmos-sql.svc.cluster.local"
          name: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/dbname"
          username: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/username"
          password: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/password"
        # only for letsencrypt
        # do not rewrite auth request on coredns with this
        # ingress:
        #   annotations:
        #     cert-manager.io/cluster-issuer: letsencrypt-prod
    wait: true
    waitForJobs: true
    labels:
      app: keycloak

  - name: keycloak-secrets
    needs:
      - kosmos-iam/keycloak-cluster
    namespace: kosmos-iam
    chart: ../../keycloak/keycloak-secrets
    labels:
      app: keycloak

  - name: keycloakimporter-init-import-admin
    needs:
      - kosmos-iam/keycloak-secrets
    namespace: kosmos-iam
    chart: ../../keycloakimporter/keycloakimporter
    labels:
      app: keycloak
    values:
      - ./values_templates/keycloak/import-user-import-admin.yaml.gotmpl

  - name: keycloakimporter-init-realm-kosmos
    needs:
      - kosmos-iam/keycloak-secrets
    namespace: kosmos-iam
    chart: ../../keycloakimporter/keycloakimporter
    wait: true
    waitForJobs: true
    labels:
      app: keycloak
    values:
      - ./values_templates/keycloak/import-realm-kosmos.yaml.gotmpl

  - name: rancher-secrets
    namespace: cattle-system
    chart: ../../rancher/rancher-secrets
    labels:
      app: rancher
    values:
      - trustedCA: "ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/tls.crt"

  - name: rancher
    namespace: cattle-system
    chart: ../../rancher/rancher
    wait: true
    waitForJobs: true
    needs: [ cattle-system/rancher-secrets ]
    skipDeps: true
    labels:
      app: rancher
    values:
      - ../../rancher/values/values.yaml
      - hostname: rancher.{{ .StateValues.domain }}

  # Create rancher client in iam
  - name: keycloakimporter-rancher
    namespace: cattle-system
    needs:
      - kosmos-iam/keycloakimporter-init-realm-kosmos
      - cattle-system/rancher-secrets
    chart: ../../keycloakimporter/keycloakimporter
    labels:
      app: rancher
    values:
      - ./values_templates/rancher/import-client-rancher.yaml.gotmpl

  # Generate admin and oidc secret
  - name: pgadmin4-secrets
    namespace: kosmos-sql
    createNamespace: true
    chart: ../../pgadmin/pgadmin4-secrets
    needs: [ kosmos-sql/cnpg ]
    labels:
      app: pgadmin

  # No oidc config
  - name: pgadmin4
    namespace: kosmos-sql
    createNamespace: true
    chart: ../../pgadmin/pgadmin4
    needs: [ kosmos-sql/pgadmin4-secrets, kosmos-sql/pgcluster ]
    labels:
      app: pgadmin
    values:
      - ../../pgadmin/values/values.yaml
      - domain: {{ .StateValues.domain }}

  - name: keycloakimporter-pgadmin4-kosmos
    namespace: kosmos-iam
    labels:
      app: pgadmin
    needs:
      - kosmos-iam/keycloakimporter-init-realm-kosmos
      - kosmos-sql/pgadmin4-secrets
      - kosmos-sql/pgadmin4
    chart: ../../keycloakimporter/keycloakimporter
    values:
      - values_templates/pgadmin/oidc-values.yaml.gotmpl

  - name: grafana-secrets
    namespace: kosmos-monitoring
    chart: ../../monitoring/grafana-secrets
    wait: true
    waitForJobs: true
    skipDeps: true
    labels:
      app: monitoring

  - name: keycloakimporter-import-grafana
    needs:
      - kosmos-iam/keycloakimporter-init-realm-kosmos
      - kosmos-monitoring/grafana-secrets
    namespace: kosmos-monitoring
    chart: ../../keycloakimporter/keycloakimporter
    wait: true
    waitForJobs: true
    labels:
      app: monitoring
    values:
      - ./values_templates/grafana/import-grafana.yaml.gotmpl

  - name: monitoring-stack
    namespace: kosmos-monitoring
    chart: ../../monitoring/kube-prometheus-stack
    needs: [kosmos-monitoring/grafana-secrets, kosmos-monitoring/monitoringcrds]
    wait: true
    waitForJobs: true
    skipDeps: true
    labels:
      app: monitoring
    values:
      - ../../monitoring/values/values.yaml
      - domain: {{ .StateValues.domain }}
        grafana:
          domain: {{ .StateValues.domain }}

  - name: s3-operator
    namespace: kosmos-s3
    chart: ../../s3/operator
    wait: true
    waitForJobs: true
    labels:
      app: s3
    values:
      # No HA (replica 1)
      - ../../s3/values/values-operator.yaml

  - name: s3-secrets
    namespace: kosmos-s3
    chart: ../../s3/minio-secrets
    needs: [kosmos-iam/keycloak-cluster, kosmos-s3/s3-operator]
    labels:
      app: s3
    values:
      - oidcConfig:
          domain: {{ .StateValues.domain }}
        trustedCA: ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/ca.crt

  - name: keycloakimporter-import-s3
    needs:
      - kosmos-s3/s3-secrets
      - kosmos-iam/keycloakimporter-init-realm-kosmos
    namespace: kosmos-iam
    chart: ../../keycloakimporter/keycloakimporter
    labels:
      app: s3
    values:
      - ./values_templates/s3/import-client-s3.yaml.gotmpl

  - name: s3-cluster
    namespace: kosmos-s3
    needs: [ kosmos-s3/s3-operator, kosmos-s3/s3-secrets, kosmos-iam/keycloakimporter-import-s3 ]
    chart: ../../s3/tenant
    wait: true
    waitForJobs: true
    labels:
      app: s3
    values:
      - ../../s3/values/values-tenant.yaml
      #- ../../s3/values/prod-singlenode-overlay.yaml
      - domain: {{ .StateValues.domain }}
      - ingress:
          console:
            enabled: true
            # # annotation only for letsencrypt
            # annotations:
            #   nginx.ingress.kubernetes.io/proxy-body-size: 102400m
            #   cert-manager.io/cluster-issuer: letsencrypt-prod
            #   kosmos-studio.athea.tech/description: S3 Object Store
            #   kosmos-studio.athea.tech/name: S3
          api:
            enabled: true
            # annotation only for letsencrypt
            # annotations:
            #   nginx.ingress.kubernetes.io/proxy-body-size: 102400m
            #   cert-manager.io/cluster-issuer: letsencrypt-prod
            #   kosmos-studio.athea.tech/description: S3 API Object Store
            #   kosmos-studio.athea.tech/name: S3 API
            #   kosmos-studio.athea.tech/ignore: "true"

  - name: gitea-secrets
    namespace: kosmos-dev-restricted
    chart: ../../gitea/gitea-secrets
    skipDeps: true
    values:
      - trustedCA: "ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/tls.crt"
    labels:
      app: gitea

  - name: keycloakimporter-import-gitea
    needs:
      - kosmos-iam/keycloakimporter-init-realm-kosmos
      - kosmos-dev-restricted/gitea-secrets
    namespace: kosmos-dev-restricted
    chart: ../../keycloakimporter/keycloakimporter
    wait: true
    waitForJobs: true
    labels:
      app: gitea
    values:
      - ./values_templates/gitea/import-client-gitea.yaml.gotmpl

  - name: gitea
    namespace: kosmos-dev-restricted
    needs: [kosmos-dev-restricted/gitea-secrets, kosmos-dev-restricted/keycloakimporter-import-gitea]
    chart: ../../gitea/gitea
    wait: true
    waitForJobs: true
    skipDeps: true
    labels:
      app: gitea
    values:
      - ../../gitea/values/values.yaml
      - ingress:
          hosts:
            - host: gitea.{{ .StateValues.domain }}
              paths:
                - path: /
                  pathType: Prefix
          tls:
            - hosts:
                - gitea.{{ .StateValues.domain }}
        persistence:
          size: 1Gi
      - gitea:
          oauth:
            - name: "OIDC"
              provider: "openidConnect"
              autoDiscoverUrl: "https://auth.{{ .StateValues.domain }}/realms/kosmos/.well-known/openid-configuration"
              existingSecret: gitea-oidc-secret
              # fine grain SSO config
              groupClaimName: "giteam"
              requiredClaimName: "giteam"
              adminGroup: "admin"
              groupTeamMap: '{"admin" : {"athea" : ["Owners"]}}'

  # remember, with self certs use:
  # git config --global http.sslVerify false
  - name: jupyterhub-secrets
    namespace: kosmos-data
    chart: ../../jupyterhub4/jupyterhub-secrets
    wait: true
    labels:
      app: jupyterhub4

  - name: keycloakimporter-jupyterhub
    namespace: kosmos-iam
    labels:
      app: jupyterhub4
    needs:
      - kosmos-iam/keycloakimporter-init-realm-kosmos
      - kosmos-data/jupyterhub-secrets
    chart: ../../keycloakimporter/keycloakimporter
    values:
      - values_templates/jupyterhub/import-client-jupyterhub.yaml.gotmpl

  - name: jupyterhub
    namespace: kosmos-data
    chart: ../../jupyterhub4/jupyterhub
    wait: true
    skipDeps: true
    labels:
      app: jupyterhub4
    needs:
      - kosmos-iam/keycloakimporter-jupyterhub
    values:
      - ../../jupyterhub4/values/values.yaml
      - ingress:
          hosts:
            - jupyterhub.{{ .StateValues.domain }}
          tls:
            - hosts:
                - jupyterhub.{{ .StateValues.domain }}
              secretName: jupyterhub-cert
      - hub:
          cookieSecret: ref+k8s://v1/Secret/kosmos-data/jupyterhub-oidc-secret/cookieSecret
          config:
            GenericOAuthenticator:
              client_secret: ref+k8s://v1/Secret/kosmos-data/jupyterhub-oidc-secret/clientSecret
              oauth_callback_url: https://jupyterhub.{{ .StateValues.domain }}/hub/oauth_callback
              authorize_url: https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/auth
              token_url: https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/token
              userdata_url: https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/userinfo
              allowed_groups:
                - dataing
              admin_groups:
                - adminsysteme
                - adminsecurite
                - admininfra

  # - name: tileserver
  #   namespace: kosmos-data
  #   createNamespace: true
  #   wait: true
  #   chart: ../../tileserver/tileserver
  #   skipDeps: true
  #   labels:
  #     app: tileserver
  #   values:
  #     - ../../tileserver/values.yaml
  #     - config:
  #         styles:
  #           satellite:
  #             style: "http://minio.kosmos-s3/tileserver-public/styles/cog_style.json"
  #         tiles:
  #           satellite:
  #             pmtiles: "http://minio.kosmos-s3/tileserver-public/data/satellite.pmtiles"
  #     - ingress:
  #         hosts:
  #           - host: tileserver.{{ .StateValues.domain }}
  #             paths:
  #               - path: /
  #                 pathType: Prefix
  #         tls:
  #           - hosts:
  #               - tileserver.{{ .StateValues.domain }}
  #             secretName: tileserver-tls

  - name: redis
    namespace: kosmos-data
    createNamespace: true
    wait: true
    chart: ../../redis/redis
    skipDeps: true
    labels:
      app: redis
    values:
      - ../../redis/values/values.yaml

  - name: superset-secrets
    namespace: kosmos-data
    createNamespace: true
    wait: true
    chart: ../../superset/superset-secrets
    skipDeps: true
    values:
      - caCrt: "ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/ca.crt"
    labels:
      app: superset

  # will create a superset user and a superset db in pg and generate a password
  - name: superset-initdb
    namespace: kosmos-sql
    wait: true
    needs: [ kosmos-sql/pgcluster ]
    chart: ../../init-datastore/initpg
    labels:
      app: superset
    values:
      - pgSecretName: pgcluster-superuser
        appDbName: superset
        appDbUser: superset

  - name: keycloakimporter-superset
    namespace: kosmos-data
    labels:
      app: superset
    needs:
      - kosmos-iam/keycloakimporter-init-realm-kosmos
      - kosmos-data/superset-secrets
    chart: ../../keycloakimporter/keycloakimporter
    values:
      - values_templates/superset/import-client-superset.yaml.gotmpl

  - name: superset
    namespace: kosmos-data
    createNamespace: true
    wait: true
    chart: ../../superset/superset
    skipDeps: true
    needs: [kosmos-data/superset-secrets, kosmos-data/redis, kosmos-sql/superset-initdb, kosmos-data/keycloakimporter-superset]
    labels:
      app: superset
    values:
      - ../../superset/values.yaml
      - ./values_templates/superset/sso-override.yaml.gotmpl
      - supersetNode:
          connections:
            redis_password: ref+k8s://v1/Secret/kosmos-data/redis/redis-password
            db_user: ref+k8s://v1/Secret/kosmos-sql/superset-initdb-secret/app_db_user
            db_pass: ref+k8s://v1/Secret/kosmos-sql/superset-initdb-secret/app_db_password
        init:
          adminUser:
            username: ref+k8s://v1/Secret/kosmos-data/superset-admin-secret/username
            firstname: Superset
            lastname: Admin
            email: <EMAIL>
            password: ref+k8s://v1/Secret/kosmos-data/superset-admin-secret/password
        ingress:
          hosts:
            - superset.{{.StateValues.domain }}
          tls:
            - secretName: superset-cert
              hosts:
                - superset.{{.StateValues.domain }}

  - name: keycloakimporter-kosmos-studio
    namespace: kosmos-iam
    labels:
      app: kosmos-studio
    needs:
      - kosmos-iam/keycloakimporter-init-realm-kosmos
    chart: ../../keycloakimporter/keycloakimporter
    values:
      - values_templates/kosmos-studio/import-client-kosmos-studio.yaml.gotmpl

  - name: kosmos-studio
    namespace: kosmos-data
    labels:
      app: kosmos-studio
    createNamespace: true
    wait: true
    chart: ../../kosmos-studio/charts/kosmos-studio
    skipDeps: true
    needs: [ kosmos-s3/s3-cluster ]
    values:
      - ../../kosmos-studio/values/values.yaml
      - ingress:
          host: kosmos-studio.{{.StateValues.domain }}
        server:
          OIDC_AUTHORITY: https://auth.{{.StateValues.domain }}/realms/kosmos
          OIDC_REDIRECT_URI: https://kosmos-studio.{{.StateValues.domain }}
          UI_BLACKLIST_PAGES: buckets,applications,models
        initS3Admin:
          enabled: false
          ############
          # DIRECT   #
          ############
          # OBJECT_STORAGE_SECURITY_MODE: "direct"
          # OBJECT_STORAGE_URL: "https://s3-api.{{.StateValues.domain }}"
          # OBJECT_STORAGE_FORCE_LANDING_BUCKET: ""
          ############
          # S3Guard  #
          ############
          # OBJECT_STORAGE_SECURITY_MODE: "s3guard"
          # OBJECT_STORAGE_URL: "https://iad-s3.{{.StateValues.domain }}"
          # OBJECT_STORAGE_FORCE_LANDING_BUCKET: ""
          ############
          # DCS      #
          ############
          #OBJECT_STORAGE_SECURITY_MODE: "dcs"
          #OBJECT_STORAGE_URL: "https://iad-s3.{{.StateValues.domain }}"
          #OBJECT_STORAGE_FORCE_LANDING_BUCKET: "notify"

  # - name: coder-initpg
  #   namespace: kosmos-sql
  #   labels:
  #     app: coder
  #   needs:
  #     - kosmos-sql/pgcluster
  #   chart: ../../init-datastore/initpg
  #   values:
  #     - appDbName: coder
  #     - appDbUserPrefix: coder

  # - name: coder-secrets
  #   namespace: kosmos-dev-restricted
  #   labels:
  #     app: coder
  #   needs:
  #     - kosmos-sql/coder-initpg
  #   chart: ../../coder/coder-secrets
  #   values:
  #     - dbUrl: pgcluster-rw.kosmos-sql.svc.cluster.local:5432
  #     - dbUsername: ref+k8s://v1/Secret/kosmos-sql/coder-initpg-secret/app_db_user
  #     - dbPassword: ref+k8s://v1/Secret/kosmos-sql/coder-initpg-secret/app_db_password

  # - name: keycloakimporter-coder
  #   namespace: kosmos-dev-restricted
  #   labels:
  #     app: coder
  #   needs:
  #     - kosmos-iam/keycloakimporter-init-realm-kosmos
  #     - kosmos-dev-restricted/coder-secrets
  #   chart: ../../keycloakimporter/keycloakimporter
  #   values:
  #     - values_templates/coder/import-client-coder.yaml.gotmpl

  # - name: coder
  #   namespace: kosmos-dev-restricted
  #   createNamespace: true
  #   chart: ../../coder/coder
  #   skipDeps: true
  #   needs:
  #     - kosmos-dev-restricted/coder-secrets
  #   labels:
  #     app: coder
  #   values:
  #     - ../../coder/values/values.yaml
  #     - domain: {{ .StateValues.domain }}

  - name: clickhouse-secrets
    namespace: kosmos-system-restricted
    createNamespace: true
    chart: ../../clickhouse/clickhouse-secrets
    wait: true
    waitForJobs: true
    labels:
      app: clickhouse

  - name: clickhouse-operator
    namespace: kosmos-system-restricted
    createNamespace: true
    chart: ../../clickhouse/altinity-clickhouse-operator
    needs: [kosmos-system-restricted/clickhouse-secrets]
    wait: true
    labels:
      app: clickhouse
    values:
      - ../../clickhouse/values/values-operator.yaml

  # # no need for single node clickhouse
  # - name: clickhouse-keeper
  #   namespace: kosmos-logs
  #   createNamespace: true
  #   chart: ../../clickhouse/keeper
  #   needs: [kosmos-system-restricted/clickhouse-operator]
  #   wait: true
  #   waitForJobs: true
  #   labels:
  #     app: clickhouse
  #   values:
  #     - volumeClaim:
  #         accessModes:
  #           - ReadWriteOnce
  #         resources:
  #           requests:
  #             storage: 10Gi

  - name: clickhouse-cluster
    namespace: kosmos-logs
    createNamespace: true
    chart: ../../clickhouse/clickhouse
    needs: [kosmos-system-restricted/clickhouse-operator]
    wait: true
    waitForJobs: true
    labels:
      app: clickhouse
    values:
      - ../../clickhouse/values/values-clickhouse.yaml
      - volumeClaim:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 32Gi

  # - name: kube-vip
  #   namespace: kosmos-system
  #   createNamespace: true
  #   chart: ../../kubelb/kube-vip
  #   wait: true
  #   labels:
  #     app: kubelb
  #   values:
  #     - ../../kubelb/values/values-kube-vip.yaml

  # - name: kube-vip-cloud-provider
  #   namespace: kosmos-system
  #   createNamespace: true
  #   chart: ../../kubelb/kube-vip-cloud-provider
  #   needs: [kosmos-system/kube-vip]
  #   wait: true
  #   labels:
  #     app: kubelb
  #   values:
  #     - ../../kubelb/values/values-kube-vip-cloud-provider.yaml
  #     # ip range for service of type 'loadbalancer'
  #     - cm:
  #         data:
  #           range-global: ***************-***************

  - name: vector
    namespace: kosmos-logs
    createNamespace: true
    chart: ../../vector/vector
    needs: [kosmos-logs/clickhouse-cluster]
    wait: true
    labels:
      app: vector
    values:
      - ../../vector/values/values.yaml
      - ../../vector/values/values-audit-logs-transformers.yaml
      - ../../vector/values/values-clickhouse-sink.yaml

  # Need clickhouse generated credentials
  - name: grafana-resources
    namespace: kosmos-monitoring
    chart: ../../monitoring/grafana-resources
    needs: [kosmos-monitoring/monitoring-stack, kosmos-logs/vector, kosmos-logs/clickhouse-cluster]
    wait: true
    waitForJobs: true
    skipDeps: true
    values:
      - ../../monitoring/values/grafana-resources-kosmos.yaml
