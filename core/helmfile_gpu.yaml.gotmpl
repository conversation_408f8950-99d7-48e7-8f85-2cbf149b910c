# samples:
# helmfile sync -f helmfile_gpu.yaml.gotmpl
---
environments:
  default:
    values:
      #- domain: "dh.artemis-ia.fr"
      - domain: "kosmos.athea"
      #- domain: "kosmos.wip"
---
releases:
  - name: monitoringcrds
    namespace: kosmos-monitoring
    chart: ../../monitoring/kube-prometheus-stack/charts/crds
    wait: true
    waitForJobs: true
    skipDeps: true

  # Only for gpu usage
  - name: gpu-operator
    namespace: kosmos-system
    chart: ../../gpu-operator/gpu-operator
    needs: [ kosmos-monitoring/monitoringcrds ]
    wait: true
    waitForJobs: true
    skipDeps: true
    labels:
      app: gpu-operator
    values:
      - ../../gpu-operator/values/values.yaml
      # - ../../gpu-operator/values/values-timeslice-overlay.yaml
