# samples:
# helmfile sync -f helmfile_scaleway.yaml.gotmpl -l core=true
# helmfile sync -f helmfile_scaleway.yaml.gotmpl -l app=gpu-operator
# adminprs pass:
---
environments:
  default:
    values:
      - domain: "dh.artemis-ia.fr"
---
releases:
  - name: monitoringcrds
    namespace: kosmos-monitoring
    chart: ../../monitoring/kube-prometheus-stack/charts/crds
    labels:
      core: true
    skipDeps: true

  # Only for gpu usage
  - name: gpu-operator
    namespace: kosmos-system
    chart: ../../gpu-operator/gpu-operator
    needs: [kosmos-monitoring/monitoringcrds]
    wait: true
    waitForJobs: true
    skipDeps: true
    labels:
      app: gpu-operator
    values:
      - ../../gpu-operator/values.yaml
      # - ../../gpu-operator/values-timeslice-overlay.yaml

  - name: rancher-secrets
    namespace: cattle-system
    chart: ../../rancher/rancher-secrets
    labels:
      app: rancher
    values:
      - trustedCA: "ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/tls.crt"

  - name: rancher
    namespace: cattle-system
    chart: ../../rancher/rancher
    wait: true
    waitForJobs: true
    needs: [cattle-system/rancher-secrets]
    skipDeps: true
    labels:
      app: rancher
    values:
      - ../../rancher/values.yaml
      - hostname: rancher.{{ .StateValues.domain }}
      # no need to anotate ingress, rancher will handle letsencrypt
      - ingress:
          tls:
            source: letsEncrypt
      - letsEncrypt:
          email: <EMAIL>
          environment: production
          ingress:
            class: nginx
      - replicas: 1

  # pg operator only
  - name: cnpg
    namespace: kosmos-sql
    createNamespace: true
    chart: ../../postgresql/cloudnative-pg
    needs: [kosmos-monitoring/monitoringcrds]
    skipDeps: true
    wait: true
    waitForJobs: true
    labels:
      app: postgresql
    values:
      - ../../postgresql/values/values-operator.yaml

  - name: pgcluster
    namespace: kosmos-sql
    createNamespace: true
    wait: true
    waitForJobs: true
    chart: ../../postgresql/cluster
    needs: [kosmos-sql/cnpg]
    labels:
      app: postgresql
    values:
      - ../../postgresql/values/values-cluster-fat.yaml
      - cluster:
          instances: 1
          imageName: "kosmos-registry.{{.StateValues.domain }}:30005/athea/cloudnative-pg/postgresql-fat:16-3.5"
          storage:
            size: 48Gi
          resources:
            limits:
              cpu: 4000m
              memory: 8Gi
            requests:
              cpu: 4000m
              memory: 8Gi

  - name: s3-operator
    namespace: kosmos-s3
    chart: ../../s3/operator
    wait: true
    waitForJobs: true
    labels:
      app: s3
    values:
      # No HA (replica 1)
      - ../../s3/values/values-operator.yaml
    hooks:
    - events: ["postsync"]
      showlogs: true
      command: "kubectl"
      args: ["wait", "--for=condition=Established", "crd/tenants.minio.min.io", "--timeout=60s"]

  - name: s3-secrets
    namespace: kosmos-s3
    chart: ../../s3/minio-secrets
    labels:
      app: s3
    values:
      - oidcConfig:
          domain: {{ .StateValues.domain }}
        trustedCA: ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/ca.crt

  - name: s3-cluster
    namespace: kosmos-s3
    needs: [kosmos-s3/s3-operator, kosmos-s3/s3-secrets]
    chart: ../../s3/tenant
    labels:
      app: s3
    values:
      - ../../s3/values/values-tenant.yaml
      - ../../s3/values/prod-singlenode-overlay.yaml
      - domain: {{ .StateValues.domain }}

  # Generate admin and oidc secret
  - name: pgadmin4-secrets
    namespace: kosmos-sql
    createNamespace: true
    chart: ../../pgadmin/pgadmin4-secrets
    needs: [kosmos-sql/cnpg]
    labels:
      app: pgadmin
    values:
      - pgadminOidc:
          hostname: auth.{{.StateValues.domain }}

  # No oidc config
  - name: pgadmin4
    namespace: kosmos-sql
    createNamespace: true
    chart: ../../pgadmin/pgadmin4
    needs: [kosmos-sql/pgadmin4-secrets, kosmos-sql/pgcluster]
    labels:
      app: pgadmin
    values:
      - ../../pgadmin/values/values.yaml
      - image:
          registry: kosmos-registry.{{.StateValues.domain }}:30005/athea
      - extraConfigmapMounts: []
      - domain: "{{.StateValues.domain }}"
        ingress:
          annotations:
            cert-manager.io/cluster-issuer: letsencrypt-prod

  # iam
  - name: keycloak-operator
    namespace: kosmos-iam
    createNamespace: true
    wait: true
    chart: ../../keycloak/keycloak-operator
    labels:
      app: keycloak

  - name: keycloak-cluster
    namespace: kosmos-iam
    needs:
      - kosmos-iam/keycloak-operator
      - kosmos-sql/pgcluster
    chart: ../../keycloak/keycloak-cluster
    labels:
      app: keycloak
    values:
      - hostname: iam.{{ .StateValues.domain }}
        db:
          host: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/host+.kosmos-sql.svc.cluster.local"
          name: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/dbname"
          username: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/username"
          password: "ref+k8s://v1/Secret/kosmos-sql/pgcluster-app/password"
        ingress:
          annotations:
            cert-manager.io/cluster-issuer: letsencrypt-prod
            #cert-manager.io/cluster-issuer: letsencrypt-staging
            #cert-manager.io/cluster-issuer: kosmos-ca-issuer
    wait: true
    waitForJobs: true

  - name: tileserver
    namespace: kosmos-data
    createNamespace: true
    wait: true
    chart: ../../tileserver/tileserver
    skipDeps: true
    labels:
      app: tileserver
    values:
      - ../../tileserver/values.yaml
      - image:
          repository: kosmos-registry.{{.StateValues.domain }}:30005/athea/maptiler/tileserver-gl
        ingress:
          annotations:
            cert-manager.io/cluster-issuer: letsencrypt-prod
          hosts:
            - host: tileserver.{{.StateValues.domain }}
              paths:
              - path: /
                pathType: Prefix
          tls:
            - secretName: tileserver-tls
              hosts:
                - tileserver.{{.StateValues.domain }}
        # NOTE: manual requirements to create PV/PVC, if you want to use custom maps. see documentation.
        volumeMounts:
          - name: tileserver-gl
            mountPath: /tileserver-gl
            subPath: /mnt/pv/
            persistentVolumeClaim: tileserver-maps-9


  - name: keycloakimporter-kosmos-studio
    namespace: kosmos-iam
    labels:
      app: kosmos-studio
    needs:
      - kosmos-iam/keycloak-cluster
    chart: ../../keycloakimporter/keycloakimporter
    values:
      - values_templates/kosmos-studio/import-client-kosmos-studio.yaml.gotmpl

  - name: kosmos-studio-secrets
    namespace: kosmos-data
    chart: ../../kosmos-studio/charts/kosmos-studio-secrets
    labels:
      app: kosmos-studio

  - name: kosmos-studio
    namespace: kosmos-data
    createNamespace: true
    wait: true
    chart: ../../kosmos-studio/charts/kosmos-studio
    skipDeps: true
    needs: [ kosmos-data/kosmos-studio-secrets ]
    values:
      - image:
          repository: kosmos-registry.{{.StateValues.domain }}:30005/athea
        ingress:
          host: kosmos-studio.{{.StateValues.domain }}
          annotations:
            cert-manager.io/cluster-issuer: letsencrypt-prod
        server:
          LOG_LEVEL: debug
          OIDC_AUTHORITY: https://iam.{{.StateValues.domain }}/realms/kosmos
          OIDC_REDIRECT_URI: https://kosmos-studio.{{.StateValues.domain }}
          OCI_REGISTRY_URL: oci://kosmos-registry.{{.StateValues.domain }}
          OCI_REGISTRY_USERNAME: "ref+k8s://v1/Secret/kosmos-dev-restricted/zot-push-secret/username"
          OCI_REGISTRY_PASSWORD: "ref+k8s://v1/Secret/kosmos-dev-restricted/zot-push-secret/password"
    labels:
      app: kosmos-studio

  - name: kosmos-doc
    namespace: kosmos-doc
    createNamespace: true
    wait: true
    chart: ../../doc/helm_charts/kosmos-doc
    skipDeps: true
    labels:
      app: kosmos-doc
    values:
      - ../../doc/values.yaml
      - image:
          repository: kosmos-registry.{{.StateValues.domain }}:30005/athea
          tag: dev
        ingress:
          host: kosmos-doc.{{.StateValues.domain }}
          annotations:
            cert-manager.io/cluster-issuer: letsencrypt-prod
          tls:
            - secretName: kosmos-doc-tls
              hosts:
                - kosmos-doc.{{.StateValues.domain }}
