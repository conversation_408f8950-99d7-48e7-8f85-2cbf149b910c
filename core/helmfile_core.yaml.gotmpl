# samples:
# helmfile sync -f helmfile_core.yaml.gotmpl
---
environments:
  default:
    values:
      #- domain: "dh.artemis-ia.fr"
      - domain: "kosmos.athea"
      #- domain: "kosmos.wip"
---
releases:
  - name: cert-manager
    namespace: kosmos-system-restricted
    createNamespace: true
    wait: true
    waitForJobs: true
    labels:
      core: true
      app: cert-manager
    chart: ../../cert-manager/cert-manager
    values:
      - ../../cert-manager/values/values.yaml

  - name: cert-manager-init
    namespace: kosmos-system-restricted
    createNamespace: true
    labels:
      core: true
      app: cert-manager
    needs: [kosmos-system-restricted/cert-manager]
    chart: ../../cert-manager/cert-manager-init
    wait: true
    # values:
    #   # This will activate letsencrypt acme exchange
    #   - letsEncrypt:
    #       enabled: true
    #       # Email that will receive LetsEncrypt notifications
    #       letsEncryptEmail: eric.hoff<PERSON>@athea.tech
    #       http01:
    #         enabled: true

  - name: kosmos-namespaces
    namespace: kube-system
    createNamespace: false
    labels:
      core: true
      app: namespaces
    chart: ./namespaces-chart
    wait: true
    waitForJobs: true

  - name: lvm-csi
    namespace: kosmos-system
    createNamespace: false
    labels:
      core: true
      app: lvm-csi
    chart: ../../lvm-csi/topolvm
    needs: [kosmos-system-restricted/cert-manager-init, kube-system/kosmos-namespaces]
    skipDeps: true
    wait: true
    waitForJobs: true
    values:
      - ../../lvm-csi/values/values.yaml
      - controller:
          replicaCount: 1

  - name: kosmos-registry-secrets
    namespace: kosmos-dev-restricted
    createNamespace: true
    labels:
      core: true
      app: zot
    chart: ../../zot/zot-secrets

  - name: kosmos-registry
    namespace: kosmos-dev-restricted
    createNamespace: true
    labels:
      core: true
      app: zot
    needs: [kosmos-system/lvm-csi, kosmos-dev-restricted/kosmos-registry-secrets]
    chart: ../../zot/zot
    values:
      - ../../zot/values/values.yaml
      - ingress:
          # annotations:
          #   cert-manager.io/cluster-issuer: letsencrypt-prod
          #   nginx.ingress.kubernetes.io/proxy-body-size: "0"
          #   nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
          #   nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
          hosts:
            - host: kosmos-registry.{{.StateValues.domain }}
              paths:
                - path: /
          tls:
            - secretName: kosmos-registry-tls
              hosts:
                - kosmos-registry.{{.StateValues.domain }}
        pvc:
          storage: 64Gi
