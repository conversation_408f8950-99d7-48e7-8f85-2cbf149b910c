# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
    keycloak: |
        clientId: admin-cli
        username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
        password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
        grantType: password
        baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
        skipCertCheck: false
    import: |
        {
            "realm": "kosmos",
            "groups": [],
            "roles": {
                "realm": [
                    {
                        "name": "adminsysteme",
                        "composite": true,
                        "composites": {
                          "client": {
                            "pgadmin": [
                              "admin"
                            ]
                          }
                        },
                        "clientRole": false
                    },
                    {
                        "name": "adminsecurite",
                        "composite": true,
                        "composites": {
                          "client": {
                            "pgadmin": [
                              "admin"
                            ]
                          }
                        },
                        "clientRole": false
                    },
                    {
                        "name": "admininfra",
                        "composite": true,
                        "composites": {
                          "client": {
                            "pgadmin": [
                              "admin"
                            ]
                          }
                        },
                        "clientRole": false
                    }
                ],
                "client": {
                    "pgadmin": [
                        {
                            "name": "admin",
                            "description": "",
                            "composite": false,
                            "clientRole": true,
                            "attributes": {}
                        }
                    ]
                }
            },
            "clients": [
                {
                    "clientId": "ref+k8s://v1/Secret/kosmos-sql/pgadmin4-oidc-secret/OAUTH2_CLIENT_ID+",
                    "name": "pgadmin Client",
                    "description": "",
                    "rootUrl": "https://pgadmin.{{ .StateValues.domain }}",
                    "adminUrl": "https://pgadmin.{{ .StateValues.domain }}",
                    "baseUrl": "/applications",
                    "surrogateAuthRequired": false,
                    "enabled": true,
                    "alwaysDisplayInConsole": false,
                    "clientAuthenticatorType": "client-secret",
                    "secret": "ref+k8s://v1/Secret/kosmos-sql/pgadmin4-oidc-secret/OAUTH2_CLIENT_SECRET+",
                    "redirectUris": [
                        "https://pgadmin.{{ .StateValues.domain }}/oauth2/authorize"
                    ],
                    "webOrigins": [
                        "https://pgadmin.{{ .StateValues.domain }}"
                    ],
                    "notBefore": 0,
                    "bearerOnly": false,
                    "consentRequired": false,
                    "standardFlowEnabled": true,
                    "implicitFlowEnabled": false,
                    "directAccessGrantsEnabled": true,
                    "serviceAccountsEnabled": false,
                    "publicClient": false,
                    "frontchannelLogout": true,
                    "protocol": "openid-connect",
                    "attributes": {
                        "request.object.signature.alg": "none",
                        "client.introspection.response.allow.jwt.claim.enabled": "false",
                        "post.logout.redirect.uris": "https://pgadmin.{{ .StateValues.domain }}",
                        "oauth2.device.authorization.grant.enabled": "false",
                        "use.jwks.url": "false",
                        "backchannel.logout.revoke.offline.tokens": "false",
                        "use.refresh.tokens": "true",
                        "oidc.ciba.grant.enabled": "false",
                        "client.use.lightweight.access.token.enabled": "false",
                        "exclude.issuer.from.auth.response": "false",
                        "backchannel.logout.session.required": "true",
                        "client_credentials.use_refresh_token": "false",
                        "tls.client.certificate.bound.access.tokens": "false",
                        "require.pushed.authorization.requests": "false",
                        "acr.loa.map": "{}",
                        "display.on.consent.screen": "false",
                        "token.response.type.bearer.lower-case": "false"
                    },
                    "authenticationFlowBindingOverrides": {},
                    "fullScopeAllowed": true,
                    "nodeReRegistrationTimeout": -1,
                    "protocolMappers": [
                        {
                            "id": "6ac5981d-8db9-4c62-a814-f6ce3195e9fd",
                            "name": "client roles",
                            "protocol": "openid-connect",
                            "protocolMapper": "oidc-usermodel-client-role-mapper",
                            "consentRequired": false,
                            "config": {
                                "introspection.token.claim": "true",
                                "multivalued": "true",
                                "userinfo.token.claim": "false",
                                "user.attribute": "foo",
                                "id.token.claim": "true",
                                "lightweight.claim": "true",
                                "access.token.claim": "true",
                                "claim.name": "pgadmin_role",
                                "jsonType.label": "String",
                                "usermodel.clientRoleMapping.clientId": "pgadmin"
                            }
                        }
                    ],
                    "defaultClientScopes": [
                        "web-origins",
                        "roles",
                        "profile",
                        "email"
                    ],
                    "optionalClientScopes": [
                        "address",
                        "phone",
                        "offline_access",
                        "microprofile-jwt"
                    ],
                    "access": {
                        "view": true,
                        "configure": true,
                        "manage": true
                    }
                }
            ],
            "clientScopes": [],
            "users": []
        }
