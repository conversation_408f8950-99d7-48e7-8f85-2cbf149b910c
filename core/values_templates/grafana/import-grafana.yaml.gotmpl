# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
        "realm": "kosmos",
        "roles": {
            "realm": [
                {
                    "name": "adminsysteme",
                    "description": "",
                    "composite": true,
                    "composites": {
                        "client": {
                            "grafana": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false,
                    "attributes": {}
                },
                {
                    "name": "admininfra",
                    "description": "",
                    "composite": true,
                    "composites": {
                        "client": {
                            "grafana": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false,
                    "attributes": {}
                },
                {
                    "name": "adminsecurite",
                    "description": "",
                    "composite": true,
                    "composites": {
                        "client": {
                            "grafana": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false,
                    "attributes": {}
                },
                {
                    "name": "dataing",
                    "description": "",
                    "composite": true,
                    "composites": {
                        "client": {
                            "grafana": [
                                "editor"
                            ]
                        }
                    },
                    "clientRole": false,
                    "attributes": {}
                }
            ],
            "client": {
                "grafana": [
                    {
                        "name": "admin",
                        "composite": false,
                        "clientRole": true,
                        "attributes": {}
                    },
                    {
                        "name": "editor",
                        "composite": false,
                        "clientRole": true,
                        "attributes": {}
                    },
                    {
                        "name": "viewer",
                        "composite": false,
                        "clientRole": true,
                        "attributes": {}
                    }
                ]
            }
        },
        "users": [],
        "clients": [
            {
                "clientId": "grafana",
                "name": "",
                "description": "",
                "rootUrl": "https://grafana.{{ .StateValues.domain }}",
                "adminUrl": "https://grafana.{{ .StateValues.domain }}",
                "baseUrl": "https://grafana.{{ .StateValues.domain }}",
                "enabled": true,
                "clientAuthenticatorType": "client-secret",
                "secret": "ref+k8s://v1/Secret/kosmos-monitoring/grafana-oidc-secret/client_secret",
                "publicClient": false,
                "redirectUris": [
                    "https://grafana.{{ .StateValues.domain }}/login/generic_oauth"
                ],
                "protocol": "openid-connect",
                "protocolMappers": [
                    {
                        "name": "client roles",
                        "protocol": "openid-connect",
                        "protocolMapper": "oidc-usermodel-client-role-mapper",
                        "consentRequired": false,
                        "config": {
                            "introspection.token.claim": "true",
                            "multivalued": "true",
                            "userinfo.token.claim": "true",
                            "user.attribute": "foo",
                            "id.token.claim": "true",
                            "lightweight.claim": "false",
                            "access.token.claim": "true",
                            "claim.name": "${client_id}.roles",
                            "jsonType.label": "String"
                        }
                    }
                ],
                "fullScopeAllowed": true,
                "frontchannelLogout": true,
                "directAccessGrantsEnabled": true,
                "attributes": {
                    "oidc.ciba.grant.enabled": "false",
                    "backchannel.logout.session.required": "true",
                    "post.logout.redirect.uris": "https://grafana.{{ .StateValues.domain }}/login",
                    "oauth2.device.authorization.grant.enabled": "false",
                    "display.on.consent.screen": "false",
                    "backchannel.logout.revoke.offline.tokens": "false"
                },
                "defaultClientScopes": [
                    "web-origins",
                    "profile",
                    "offline_access",
                    "roles",
                    "email"
                ],
                "optionalClientScopes": [
                    "acr",
                    "address",
                    "phone",
                    "microprofile-jwt",
                    "basic"
                ]
            }
        ]
    }
