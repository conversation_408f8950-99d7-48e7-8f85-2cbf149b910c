# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: true
  import: |
    {
        "realm": "kosmos",
        "groups": [
            {
                "name": "adminsysteme",
                "path": "/adminsysteme",
                "attributes": {},
                "realmRoles": [
                    "adminsysteme"
                ],
                "clientRoles": {},
                "subGroups": []
            },
            {
                "name": "adminsecurite",
                "path": "/adminsecurite",
                "attributes": {},
                "realmRoles": [
                    "adminsecurite"
                ],
                "clientRoles": {},
                "subGroups": []
            },
            {
                "name": "admininfra",
                "path": "/admininfra",
                "attributes": {},
                "realmRoles": [
                    "admininfra"
                ],
                "clientRoles": {},
                "subGroups": []
            },
            {
                "name": "dataing",
                "path": "/dataing",
                "attributes": {},
                "realmRoles": [
                    "dataing"
                ],
                "clientRoles": {},
                "subGroups": []
            }
        ],
        "clients": [],
        "clientScopes": [],
        "scopeMappings": [],
        "clientScopeMappings": {},
        "roles": {
            "realm": [
                {
                    "name": "adminsysteme",
                    "composite": true,
                    "clientRole": false,
                    "attributes": {}
                },
                {
                    "name": "adminsecurite",
                    "composite": true,
                    "clientRole": false,
                    "attributes": {}
                },
                {
                    "name": "admininfra",
                    "composite": true,
                    "clientRole": false,
                    "attributes": {}
                },
                {
                    "name": "dataing",
                    "composite": true,
                    "clientRole": false,
                    "attributes": {}
                }
            ],
            "client": {}
        },
        "users": [
            {
                "enabled": true,
                "username": "admsys",
                "email": "<EMAIL>",
                "emailVerified": true,
                "groups": ["adminsysteme"],
                "firstName": "admsys",
                "lastName": "admsys",
                "attributes": {},
                "requiredActions": [],
                "realmRoles": [],
                "clientRoles": {},
                "credentials": [
                    {
                        "type": "password",
                        "value": "admsys",
                        "temporary": false
                    }
                ]
            }
        ],
        "authenticationFlows": [
            {
                "alias": "Account verification options",
                "description": "Method with which to verity the existing account",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "idp-email-verification",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "ALTERNATIVE",
                        "priority": 20,
                        "autheticatorFlow": true,
                        "flowAlias": "Verify Existing Account by Re-authentication",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "Authentication Options",
                "description": "Authentication options.",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "basic-auth",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "basic-auth-otp",
                        "authenticatorFlow": false,
                        "requirement": "DISABLED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "auth-spnego",
                        "authenticatorFlow": false,
                        "requirement": "DISABLED",
                        "priority": 30,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "Browser - Conditional OTP",
                "description": "Flow to determine if the OTP is required for the authentication",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "conditional-user-configured",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "auth-otp-form",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "Direct Grant - Conditional OTP",
                "description": "Flow to determine if the OTP is required for the authentication",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "conditional-user-configured",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "direct-grant-validate-otp",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "First broker login - Conditional OTP",
                "description": "Flow to determine if the OTP is required for the authentication",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "conditional-user-configured",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "auth-otp-form",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "Handle Existing Account",
                "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "idp-confirm-link",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": true,
                        "flowAlias": "Account verification options",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "Reset - Conditional OTP",
                "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "conditional-user-configured",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "reset-otp",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "User creation or linking",
                "description": "Flow for the existing/non-existing user alternatives",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticatorConfig": "create unique user config",
                        "authenticator": "idp-create-user-if-unique",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "ALTERNATIVE",
                        "priority": 20,
                        "autheticatorFlow": true,
                        "flowAlias": "Handle Existing Account",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "Verify Existing Account by Re-authentication",
                "description": "Reauthentication of existing account",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "idp-username-password-form",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "CONDITIONAL",
                        "priority": 20,
                        "autheticatorFlow": true,
                        "flowAlias": "First broker login - Conditional OTP",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "browser",
                "description": "browser based authentication",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "auth-cookie",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "auth-spnego",
                        "authenticatorFlow": false,
                        "requirement": "DISABLED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "identity-provider-redirector",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 25,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "ALTERNATIVE",
                        "priority": 30,
                        "autheticatorFlow": true,
                        "flowAlias": "forms",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "clients",
                "description": "Base authentication for clients",
                "providerId": "client-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "client-secret",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "client-jwt",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "client-secret-jwt",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 30,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "client-x509",
                        "authenticatorFlow": false,
                        "requirement": "ALTERNATIVE",
                        "priority": 40,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "direct grant",
                "description": "OpenID Connect Resource Owner Grant",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "direct-grant-validate-username",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "direct-grant-validate-password",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "CONDITIONAL",
                        "priority": 30,
                        "autheticatorFlow": true,
                        "flowAlias": "Direct Grant - Conditional OTP",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "docker auth",
                "description": "Used by Docker clients to authenticate against the IDP",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "docker-http-basic-authenticator",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "first broker login",
                "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticatorConfig": "review profile config",
                        "authenticator": "idp-review-profile",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": true,
                        "flowAlias": "User creation or linking",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "forms",
                "description": "Username, password, otp and other auth forms.",
                "providerId": "basic-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "auth-username-password-form",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "CONDITIONAL",
                        "priority": 20,
                        "autheticatorFlow": true,
                        "flowAlias": "Browser - Conditional OTP",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "http challenge",
                "description": "An authentication flow based on challenge-response HTTP Authentication Schemes",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "no-cookie-redirect",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": true,
                        "flowAlias": "Authentication Options",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "registration",
                "description": "registration flow",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "registration-page-form",
                        "authenticatorFlow": true,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": true,
                        "flowAlias": "registration form",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "registration form",
                "description": "registration form",
                "providerId": "form-flow",
                "topLevel": false,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "registration-user-creation",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "registration-profile-action",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 40,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "registration-password-action",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 50,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "registration-recaptcha-action",
                        "authenticatorFlow": false,
                        "requirement": "DISABLED",
                        "priority": 60,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "reset credentials",
                "description": "Reset credentials for a user if they forgot their password or something",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "reset-credentials-choose-user",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "reset-credential-email",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 20,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticator": "reset-password",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 30,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    },
                    {
                        "authenticatorFlow": true,
                        "requirement": "CONDITIONAL",
                        "priority": 40,
                        "autheticatorFlow": true,
                        "flowAlias": "Reset - Conditional OTP",
                        "userSetupAllowed": false
                    }
                ]
            },
            {
                "alias": "saml ecp",
                "description": "SAML ECP Profile Authentication Flow",
                "providerId": "basic-flow",
                "topLevel": true,
                "builtIn": true,
                "authenticationExecutions": [
                    {
                        "authenticator": "http-basic-authenticator",
                        "authenticatorFlow": false,
                        "requirement": "REQUIRED",
                        "priority": 10,
                        "autheticatorFlow": false,
                        "userSetupAllowed": false
                    }
                ]
            }
        ],
        "authenticatorConfig": [
            {
                "alias": "create unique user config",
                "config": {
                    "require.password.update.after.registration": "false"
                }
            },
            {
                "alias": "review profile config",
                "config": {
                    "update.profile.on.first.login": "missing"
                }
            }
        ],
        "requiredActions": [
            {
                "alias": "CONFIGURE_TOTP",
                "name": "Configure OTP",
                "providerId": "CONFIGURE_TOTP",
                "enabled": true,
                "defaultAction": false,
                "priority": 10,
                "config": {}
            },
            {
                "alias": "TERMS_AND_CONDITIONS",
                "name": "Terms and Conditions",
                "providerId": "TERMS_AND_CONDITIONS",
                "enabled": false,
                "defaultAction": false,
                "priority": 20,
                "config": {}
            },
            {
                "alias": "UPDATE_PASSWORD",
                "name": "Update Password",
                "providerId": "UPDATE_PASSWORD",
                "enabled": true,
                "defaultAction": false,
                "priority": 30,
                "config": {}
            },
            {
                "alias": "UPDATE_PROFILE",
                "name": "Update Profile",
                "providerId": "UPDATE_PROFILE",
                "enabled": true,
                "defaultAction": false,
                "priority": 40,
                "config": {}
            },
            {
                "alias": "VERIFY_EMAIL",
                "name": "Verify Email",
                "providerId": "VERIFY_EMAIL",
                "enabled": true,
                "defaultAction": false,
                "priority": 50,
                "config": {}
            },
            {
                "alias": "delete_account",
                "name": "Delete Account",
                "providerId": "delete_account",
                "enabled": false,
                "defaultAction": false,
                "priority": 60,
                "config": {}
            },
            {
                "alias": "webauthn-register",
                "name": "Webauthn Register",
                "providerId": "webauthn-register",
                "enabled": true,
                "defaultAction": false,
                "priority": 70,
                "config": {}
            },
            {
                "alias": "webauthn-register-passwordless",
                "name": "Webauthn Register Passwordless",
                "providerId": "webauthn-register-passwordless",
                "enabled": true,
                "defaultAction": false,
                "priority": 80,
                "config": {}
            },
            {
                "alias": "update_user_locale",
                "name": "Update User Locale",
                "providerId": "update_user_locale",
                "enabled": true,
                "defaultAction": false,
                "priority": 1000,
                "config": {}
            }
        ],
        "browserFlow": "browser",
        "registrationFlow": "registration",
        "directGrantFlow": "direct grant",
        "resetCredentialsFlow": "reset credentials",
        "clientAuthenticationFlow": "clients",
        "dockerAuthenticationFlow": "docker auth"
    }
