# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
      "id": "kosmos",
      "realm": "kosmos",
      "accessTokenLifespan": 900,
      "groups": [],
      "clients": [
          {
              "clientId": "coder",
              "name": "",
              "description": "",
              "rootUrl": "",
              "adminUrl": "",
              "baseUrl": "",
              "surrogateAuthRequired": false,
              "enabled": true,
              "alwaysDisplayInConsole": false,
              "clientAuthenticatorType": "client-secret",
              "secret": "ref+k8s://v1/Secret/kosmos-dev-restricted/coder-oidc-secret/clientSecret+",
              "redirectUris": [
                  "https://coder.{{ .StateValues.domain }}/api/v2/users/oidc/callback"
              ],
              "webOrigins": [
                  "/*"
              ],
              "notBefore": 0,
              "bearerOnly": false,
              "consentRequired": false,
              "standardFlowEnabled": true,
              "implicitFlowEnabled": true,
              "directAccessGrantsEnabled": false,
              "serviceAccountsEnabled": false,
              "publicClient": false,
              "frontchannelLogout": true,
              "protocol": "openid-connect",
              "attributes": {
                  "request.object.signature.alg": "any",
                  "request.object.encryption.alg": "any",
                  "client.introspection.response.allow.jwt.claim.enabled": "false",
                  "standard.token.exchange.enabled": "false",
                  "frontchannel.logout.session.required": "true",
                  "oauth2.device.authorization.grant.enabled": "false",
                  "use.jwks.url": "false",
                  "backchannel.logout.revoke.offline.tokens": "false",
                  "use.refresh.tokens": "true",
                  "realm_client": "false",
                  "oidc.ciba.grant.enabled": "false",
                  "client.use.lightweight.access.token.enabled": "false",
                  "backchannel.logout.session.required": "true",
                  "client_credentials.use_refresh_token": "false",
                  "request.object.required": "not required",
                  "access.token.header.type.rfc9068": "false",
                  "acr.loa.map": "{}",
                  "require.pushed.authorization.requests": "false",
                  "tls.client.certificate.bound.access.tokens": "false",
                  "display.on.consent.screen": "false",
                  "request.object.encryption.enc": "any",
                  "token.response.type.bearer.lower-case": "false"
              },
              "authenticationFlowBindingOverrides": {
                  "browser": "3e74675f-610d-4dcb-a0f1-9a82ad777603"
              },
              "fullScopeAllowed": true,
              "nodeReRegistrationTimeout": -1,
              "protocolMappers": [
                  {
                      "name": "client roles",
                      "protocol": "openid-connect",
                      "protocolMapper": "oidc-usermodel-client-role-mapper",
                      "consentRequired": false,
                      "config": {
                          "introspection.token.claim": "true",
                          "multivalued": "true",
                          "userinfo.token.claim": "true",
                          "user.attribute": "foo",
                          "id.token.claim": "true",
                          "lightweight.claim": "false",
                          "access.token.claim": "true",
                          "claim.name": "coder_role",
                          "jsonType.label": "String",
                          "usermodel.clientRoleMapping.clientId": "coder"
                      }
                  }
              ],
              "defaultClientScopes": [
                  "web-origins",
                  "acr",
                  "roles",
                  "profile",
                  "basic",
                  "email"
              ],
              "optionalClientScopes": [
                  "address",
                  "phone",
                  "offline_access",
                  "organization",
                  "microprofile-jwt"
              ]
          }
      ],
      "clientScopes": [],
      "scopeMappings": [],
      "clientScopeMappings": {},
      "roles": {
          "realm": [
              {
                  "name": "adminsysteme",
                  "composite": true,
                  "composites": {
                      "client": {
                          "coder": [
                              "member"
                          ]
                      }
                  },
                  "clientRole": false
              },
              {
                  "name": "admininfra",
                  "composite": true,
                  "composites": {
                      "client": {
                          "coder": [
                              "member"
                          ]
                      }
                  },
                  "clientRole": false
              },
              {
                  "name": "adminsecurite",
                  "composite": true,
                  "composites": {
                      "client": {
                          "coder": [
                              "member"
                          ]
                      }
                  },
                  "clientRole": false
              },
              {
                  "name": "dataing",
                  "composite": true,
                  "composites": {
                      "client": {
                          "coder": [
                              "member"
                          ]
                      }
                  },
                  "clientRole": false
              }
          ],
          "client": {
              "coder": [
                  {
                      "name": "member",
                      "composite": false,
                      "clientRole": true
                  }
              ]
          }
      },
      "users": [],
      "authenticationFlows": [
          {
              "alias": "coder",
              "id": "3e74675f-610d-4dcb-a0f1-9a82ad777603",
              "description": "browser based authentication for coder",
              "providerId": "basic-flow",
              "topLevel": true,
              "builtIn": false,
              "authenticationExecutions": [
                  {
                      "authenticatorFlow": true,
                      "requirement": "ALTERNATIVE",
                      "priority": 34,
                      "autheticatorFlow": true,
                      "flowAlias": "coder cookie",
                      "userSetupAllowed": false
                  },
                  {
                      "authenticatorFlow": true,
                      "requirement": "ALTERNATIVE",
                      "priority": 32,
                      "autheticatorFlow": true,
                      "flowAlias": "coder forms",
                      "userSetupAllowed": false
                  }
              ]
          },
          {
              "alias": "coder cookie",
              "description": "",
              "providerId": "basic-flow",
              "topLevel": false,
              "builtIn": false,
              "authenticationExecutions": [
                  {
                      "authenticator": "auth-cookie",
                      "authenticatorFlow": false,
                      "requirement": "REQUIRED",
                      "priority": 0,
                      "autheticatorFlow": false,
                      "userSetupAllowed": false
                  },
                  {
                      "authenticatorFlow": true,
                      "requirement": "CONDITIONAL",
                      "priority": 1,
                      "autheticatorFlow": true,
                      "flowAlias": "missing_member_crole_cookie",
                      "userSetupAllowed": false
                  }
              ]
          },
          {
              "alias": "missing_member_crole_cookie",
              "description": "",
              "providerId": "basic-flow",
              "topLevel": false,
              "builtIn": false,
              "authenticationExecutions": [
                  {
                      "authenticatorConfig": "member_cookie_here",
                      "authenticator": "conditional-user-role",
                      "authenticatorFlow": false,
                      "requirement": "REQUIRED",
                      "priority": 0,
                      "autheticatorFlow": false,
                      "userSetupAllowed": false
                  },
                  {
                      "authenticator": "deny-access-authenticator",
                      "authenticatorFlow": false,
                      "requirement": "REQUIRED",
                      "priority": 1,
                      "autheticatorFlow": false,
                      "userSetupAllowed": false
                  }
              ]
          },
          {
              "alias": "coder forms",
              "description": "Username, password, otp and other auth forms.",
              "providerId": "basic-flow",
              "topLevel": false,
              "builtIn": false,
              "authenticationExecutions": [
                  {
                      "authenticator": "auth-username-password-form",
                      "authenticatorFlow": false,
                      "requirement": "REQUIRED",
                      "priority": 10,
                      "autheticatorFlow": false,
                      "userSetupAllowed": false
                  },
                  {
                      "authenticatorFlow": true,
                      "requirement": "CONDITIONAL",
                      "priority": 20,
                      "autheticatorFlow": true,
                      "flowAlias": "missing_member_crole",
                      "userSetupAllowed": false
                  }
              ]
          },
          {
              "alias": "missing_member_crole",
              "description": "",
              "providerId": "basic-flow",
              "topLevel": false,
              "builtIn": false,
              "authenticationExecutions": [
                  {
                      "authenticatorConfig": "member_here",
                      "authenticator": "conditional-user-role",
                      "authenticatorFlow": false,
                      "requirement": "REQUIRED",
                      "priority": 0,
                      "autheticatorFlow": false,
                      "userSetupAllowed": false
                  },
                  {
                      "authenticator": "deny-access-authenticator",
                      "authenticatorFlow": false,
                      "requirement": "REQUIRED",
                      "priority": 1,
                      "autheticatorFlow": false,
                      "userSetupAllowed": false
                  }
              ]
          }
      ],
      "authenticatorConfig": [
          {
              "alias": "member_cookie_here",
              "config": {
                  "condUserRole": "coder.member",
                  "negate": "true"
              }
          },
          {
              "alias": "member_here",
              "config": {
                  "condUserRole": "coder.member",
                  "negate": "true"
              }
          }
      ],
      "requiredActions": [],
      "browserFlow": "browser",
      "registrationFlow": "registration",
      "directGrantFlow": "direct grant",
      "resetCredentialsFlow": "reset credentials",
      "clientAuthenticationFlow": "clients",
      "dockerAuthenticationFlow": "docker auth"
    }
