# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local
    skipCertCheck: false
  import: |
    {
      "realm": "kosmos",
      "roles": {
        "realm": [
          {
            "name": "dataing",
            "composite": true,
            "clientRole": false,
            "attributes": {}
          },
          {
            "name": "adminsysteme",
            "composite": true,
            "clientRole": false,
            "attributes": {}
          }
        ]
      },
      "clients": [
      {
        "clientId": "argo-workflows",
        "name": "",
        "description": "",
        "rootUrl": "https://argo-workflows.{{ .StateValues.domain }}/",
        "adminUrl": "",
        "baseUrl": "",
        "surrogateAuthRequired": false,
        "enabled": true,
        "alwaysDisplayInConsole": false,
        "clientAuthenticatorType": "client-secret",
        "secret": "ref+k8s://v1/Secret/kosmos-data/argo-server-sso/client-secret",
        "redirectUris": [
          "*"
        ],
        "webOrigins": [
          "https://argo-workflows.{{ .StateValues.domain }}/"
        ],
        "notBefore": 0,
        "bearerOnly": false,
        "consentRequired": false,
        "standardFlowEnabled": true,
        "implicitFlowEnabled": false,
        "directAccessGrantsEnabled": false,
        "serviceAccountsEnabled": false,
        "publicClient": false,
        "frontchannelLogout": true,
        "protocol": "openid-connect",
        "attributes": {
          "oidc.ciba.grant.enabled": "false",
          "backchannel.logout.session.required": "true",
          "display.on.consent.screen": "false",
          "oauth2.device.authorization.grant.enabled": "false",
          "backchannel.logout.revoke.offline.tokens": "false"
        },
        "authenticationFlowBindingOverrides": {},
        "fullScopeAllowed": true,
        "nodeReRegistrationTimeout": -1,
        "protocolMappers": [
          {
            "name": "groups",
            "protocol": "openid-connect",
            "protocolMapper": "oidc-usermodel-realm-role-mapper",
            "consentRequired": false,
            "config": {
              "introspection.token.claim": "true",
              "multivalued": "true",
              "user.attribute": "foo",
              "id.token.claim": "true",
              "access.token.claim": "true",
              "claim.name": "groups",
              "jsonType.label": "String"
            }
          }
        ],
        "defaultClientScopes": [
          "web-origins",
          "acr",
          "roles",
          "profile",
          "basic",
          "email"
        ],
        "optionalClientScopes": [
          "address",
          "phone",
          "offline_access",
          "microprofile-jwt"
        ],
        "access": {
          "view": true,
          "configure": true,
          "manage": true
        }
      }
      ]
    }
