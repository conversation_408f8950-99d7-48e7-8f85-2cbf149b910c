# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
        "id": "kosmos",
        "realm": "kosmos",
        "users": [],
        "roles": {
            "realm": [
                {
                    "name": "adminsysteme",
                    "composite": true,
                    "composites": {
                        "client": {
                            "gitea": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false
                },
                {
                    "name": "adminsecurite",
                    "composite": true,
                    "composites": {
                        "client": {
                            "gitea": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false
                },
                {
                    "name": "admininfra",
                    "composite": true,
                    "composites": {
                        "client": {
                            "gitea": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false
                },
                {
                    "name": "dataing",
                    "composite": true,
                    "composites": {
                        "client": {
                            "gitea": [
                                "user"
                            ]
                        }
                    },
                    "clientRole": false
                }
            ],
            "client": {
                "gitea": [
                    {
                        "name": "admin",
                        "composite": false,
                        "clientRole": true
                    },
                    {
                        "name": "user",
                        "composite": false,
                        "clientRole": true
                    }
                ]
            }
        },
        "clients": [
            {
                "clientId": "gitea",
                "name": "",
                "description": "",
                "rootUrl": "https://gitea.{{ .StateValues.domain }}",
                "adminUrl": "https://gitea.{{ .StateValues.domain }}",
                "baseUrl": "https://gitea.{{ .StateValues.domain }}",
                "enabled": true,
                "publicClient": false,
                "clientAuthenticatorType": "client-secret",
                "secret": "ref+k8s://v1/Secret/kosmos-dev-restricted/gitea-oidc-secret/secret+",
                "redirectUris": [
                    "*"
                ],
                "protocol": "openid-connect",
                "protocolMappers": [
                    {
                        "name": "client roles",
                        "protocol": "openid-connect",
                        "protocolMapper": "oidc-usermodel-client-role-mapper",
                        "consentRequired": false,
                        "config": {
                            "introspection.token.claim": "true",
                            "multivalued": "true",
                            "userinfo.token.claim": "true",
                            "user.attribute": "foo",
                            "id.token.claim": "true",
                            "lightweight.claim": "false",
                            "access.token.claim": "true",
                            "claim.name": "giteam",
                            "jsonType.label": "String",
                            "usermodel.clientRoleMapping.clientId": "gitea"
                        }
                    }
                ],
                "fullScopeAllowed": true,
                "frontchannelLogout": true,
                "directAccessGrantsEnabled": true,
                "attributes": {
                    "oidc.ciba.grant.enabled": "false",
                    "backchannel.logout.session.required": "true",
                    "post.logout.redirect.uris": "https://gitea.{{ .StateValues.domain }}/oauth2/callback",
                    "oauth2.device.authorization.grant.enabled": "false",
                    "display.on.consent.screen": "false",
                    "backchannel.logout.revoke.offline.tokens": "false"
                },
                "defaultClientScopes": [
                    "web-origins",
                    "profile",
                    "offline_access",
                    "roles",
                    "email"
                ],
                "optionalClientScopes": [
                    "acr",
                    "address",
                    "phone",
                    "microprofile-jwt",
                    "basic"
                ]
            }
        ]
    }
