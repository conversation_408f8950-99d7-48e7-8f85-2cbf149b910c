# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
        "id": "kosmos",
        "realm": "kosmos",
        "users": [],
        "roles": {
            "realm": [
                {
                    "name": "adminsysteme",
                    "composite": true,
                    "composites": {
                        "client": {
                            "open-webui": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false
                },
                {
                    "name": "adminsecurite",
                    "composite": true,
                    "composites": {
                        "client": {
                            "open-webui": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false
                },
                {
                    "name": "admininfra",
                    "composite": true,
                    "composites": {
                        "client": {
                            "open-webui": [
                                "admin"
                            ]
                        }
                    },
                    "clientRole": false
                },
                {
                    "name": "dataing",
                    "composite": true,
                    "composites": {
                        "client": {
                            "open-webui": [
                                "user"
                            ]
                        }
                    },
                    "clientRole": false
                }
            ],
            "client": {
                "open-webui": [
                    {
                        "name": "admin",
                        "composite": false,
                        "clientRole": true
                    },
                    {
                        "name": "user",
                        "composite": false,
                        "clientRole": true
                    }
                ]
            }
        },
        "clients": [
            {
                "clientId": "open-webui",
                "name": "",
                "description": "",
                "rootUrl": "https://open-webui.{{.StateValues.domain }}",
                "adminUrl": "",
                "baseUrl": "",
                "surrogateAuthRequired": false,
                "enabled": true,
                "alwaysDisplayInConsole": false,
                "clientAuthenticatorType": "client-secret",
                "secret": "ref+k8s://v1/Secret/kosmos-ai/open-webui-oidc-secret/clientSecret+",
                "redirectUris": [
                    "https://open-webui.{{.StateValues.domain }}/oauth/oidc/callback"
                ],
                "webOrigins": [
                    "https://open-webui.{{.StateValues.domain }}/"
                ],
                "notBefore": 0,
                "bearerOnly": false,
                "consentRequired": false,
                "standardFlowEnabled": true,
                "implicitFlowEnabled": false,
                "directAccessGrantsEnabled": false,
                "serviceAccountsEnabled": false,
                "publicClient": false,
                "frontchannelLogout": true,
                "protocol": "openid-connect",
                "attributes": {
                    "realm_client": "false",
                    "oidc.ciba.grant.enabled": "false",
                    "client.secret.creation.time": "**********",
                    "backchannel.logout.session.required": "true",
                    "standard.token.exchange.enabled": "false",
                    "frontchannel.logout.session.required": "true",
                    "post.logout.redirect.uris": "https://open-webui.{{.StateValues.domain }}",
                    "oauth2.device.authorization.grant.enabled": "false",
                    "display.on.consent.screen": "false",
                    "backchannel.logout.revoke.offline.tokens": "false"
                },
                "authenticationFlowBindingOverrides": {},
                "fullScopeAllowed": true,
                "nodeReRegistrationTimeout": -1,
                "protocolMappers": [
                    {
                        "name": "client roles",
                        "protocol": "openid-connect",
                        "protocolMapper": "oidc-usermodel-client-role-mapper",
                        "consentRequired": false,
                        "config": {
                            "introspection.token.claim": "true",
                            "multivalued": "true",
                            "userinfo.token.claim": "false",
                            "user.attribute": "foo",
                            "id.token.claim": "true",
                            "lightweight.claim": "false",
                            "access.token.claim": "false",
                            "claim.name": "resource_access.${client_id}.roles",
                            "jsonType.label": "String",
                            "usermodel.clientRoleMapping.clientId": "open-webui"
                        }
                    }
                ],
                "defaultClientScopes": [
                    "web-origins",
                    "acr",
                    "roles",
                    "profile",
                    "basic",
                    "email"
                ],
                "optionalClientScopes": [
                    "address",
                    "phone",
                    "offline_access",
                    "organization",
                    "microprofile-jwt"
                ]
            }
        ]
    }
