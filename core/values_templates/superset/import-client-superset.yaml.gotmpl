# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
    keycloak: |
        clientId: admin-cli
        username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
        password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
        grantType: password
        baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local
        skipCertCheck: false
    import: |
        {
          "id": "kosmos",
          "realm": "kosmos",
          "roles": {
            "realm": [
              {
                "name": "dataing",
                "composite": true,
                "composites": {
                  "client": {
                    "superset":[
                      "superset_gamma"
                    ]
                  }
                },
                "clientRole": false,
                "attributes": {}
              },
              {
                "name": "admininfra",
                "composite": true,
                "composites": {
                  "client": {
                    "superset":[
                      "superset_admin"
                    ]
                  }
                },
                "clientRole": false,
                "attributes": {}
              },
              {
                "name": "adminsecurite",
                "composite": true,
                "composites": {
                  "client": {
                    "superset":[
                      "superset_admin"
                    ]
                  }
                },
                "clientRole": false,
                "attributes": {}
              },
              {
                "name": "adminsysteme",
                "composite": true,
                "composites": {
                  "client": {
                    "superset":[
                      "superset_admin"
                    ]
                  }
                },
                "clientRole": false,
                "attributes": {}
              }
            ],
            "client": {
              "superset" : [
                {
                  "name": "superset_alpha",
                  "composite": false,
                  "clientRole": true,
                  "attributes": {}
                },
                {
                  "name": "superset_gamma",
                  "composite": false,
                  "clientRole": true,
                  "attributes": {}
                },
                {
                  "name": "superset_admin",
                  "description": "",
                  "composite": false,
                  "clientRole": true,
                  "attributes": {}
                }
              ]
            }
          },
          "users": [],
          "clients": [
            {
              "clientId": "superset",
              "name": "superset",
              "description": "",
              "rootUrl": "https://superset.{{ .StateValues.domain }}",
              "adminUrl": "https://superset.{{ .StateValues.domain }}",
              "baseUrl": "",
              "surrogateAuthRequired": false,
              "enabled": true,
              "alwaysDisplayInConsole": false,
              "clientAuthenticatorType": "client-secret",
              "secret": "ref+k8s://v1/Secret/kosmos-data/superset-oidc-secret/client_secret+",
              "redirectUris": [
                "*"
              ],
              "webOrigins": [
                "https://superset.{{ .StateValues.domain }}"
              ],
              "notBefore": 0,
              "bearerOnly": false,
              "consentRequired": false,
              "standardFlowEnabled": true,
              "implicitFlowEnabled": false,
              "directAccessGrantsEnabled": true,
              "serviceAccountsEnabled": true,
              "publicClient": false,
              "frontchannelLogout": true,
              "protocol": "openid-connect",
              "attributes": {
                "saml.assertion.signature": "false",
                "client.secret.creation.time": "**********",
                "saml.force.post.binding": "false",
                "saml.multivalued.roles": "false",
                "saml.encrypt": "false",
                "oauth2.device.authorization.grant.enabled": "false",
                "saml.server.signature": "false",
                "backchannel.logout.revoke.offline.tokens": "false",
                "saml.server.signature.keyinfo.ext": "false",
                "exclude.session.state.from.auth.response": "false",
                "oidc.ciba.grant.enabled": "false",
                "backchannel.logout.session.required": "true",
                "saml_force_name_id_format": "false",
                "saml.client.signature": "false",
                "tls.client.certificate.bound.access.tokens": "false",
                "saml.authnstatement": "false",
                "display.on.consent.screen": "false",
                "saml.onetimeuse.condition": "false"
              },
              "authenticationFlowBindingOverrides": {},
              "fullScopeAllowed": true,
              "nodeReRegistrationTimeout": -1,
              "protocolMappers": [
                {
                  "name": "Client IP Address",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-usersessionmodel-note-mapper",
                  "consentRequired": false,
                  "config": {
                    "user.session.note": "clientAddress",
                    "id.token.claim": "true",
                    "access.token.claim": "true",
                    "claim.name": "clientAddress",
                    "jsonType.label": "String"
                  }
                },
                {
                  "name": "client roles",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-usermodel-client-role-mapper",
                  "consentRequired": false,
                  "config": {
                    "multivalued": "true",
                    "userinfo.token.claim": "true",
                    "id.token.claim": "true",
                    "access.token.claim": "true",
                    "claim.name": "resource_access.${client_id}.roles",
                    "jsonType.label": "String"
                  }
                },
                {
                  "name": "audience-mapper",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-audience-mapper",
                  "consentRequired": false,
                  "config": {
                    "included.client.audience": "eds",
                    "id.token.claim": "false",
                    "access.token.claim": "true"
                  }
                },
                {
                  "name": "Client Host",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-usersessionmodel-note-mapper",
                  "consentRequired": false,
                  "config": {
                    "user.session.note": "clientHost",
                    "id.token.claim": "true",
                    "access.token.claim": "true",
                    "claim.name": "clientHost",
                    "jsonType.label": "String"
                  }
                },
                {
                  "name": "Client ID",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-usersessionmodel-note-mapper",
                  "consentRequired": false,
                  "config": {
                    "user.session.note": "clientId",
                    "id.token.claim": "true",
                    "access.token.claim": "true",
                    "claim.name": "clientId",
                    "jsonType.label": "String"
                  }
                },
                {
                  "name": "realm roles",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-usermodel-realm-role-mapper",
                  "consentRequired": false,
                  "config": {
                    "userinfo.token.claim": "true",
                    "multivalued": "true",
                    "id.token.claim": "true",
                    "access.token.claim": "true",
                    "claim.name": "realm_access.roles",
                    "jsonType.label": "String"
                  }
                }
              ],
              "defaultClientScopes": [
                "web-origins",
                "acr",
                "roles",
                "profile",
                "email"
              ],
              "optionalClientScopes": [
                "address",
                "phone",
                "offline_access",
                "microprofile-jwt"
              ]
            }
          ]
        }
