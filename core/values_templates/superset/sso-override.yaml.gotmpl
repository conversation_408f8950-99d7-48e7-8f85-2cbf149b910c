configOverrides:
    enable_oauth: |
        from flask_appbuilder.security.manager import AUTH_OAUTH
        from superset.security import SupersetSecurityManager
        import logging
        logger = logging.getLogger()

        class CustomSsoSecurityManager(SupersetSecurityManager):

          def oauth_user_info(self, provider, response=None):
              logging.debug("Oauth2 provider: {0}.".format(provider))

              # and expects that authorization server checks the token, and response with user details
              logger.debug(" user_data PROVIDER: %s", self.appbuilder.sm.oauth_remotes[provider])
              me = self.appbuilder.sm.oauth_remotes[provider].get('openid-connect/userinfo')
              me.raise_for_status()
              data = me.json()
              return {
                  'username' : data['preferred_username'],
                  'name' : data['name'],
                  'email': data['email'],
                  'first_name': data['given_name'],
                  'last_name': data['family_name'],
                  'role_keys': data['resource_access']['superset']['roles'],
                  'is_active': True,
              }

        CUSTOM_SECURITY_MANAGER = CustomSsoSecurityManager

        # OAuth provider configuration for Keycloak
        AUTH_TYPE = AUTH_OAUTH
        OAUTH_PROVIDERS = [
          {
            'name': 'keycloak',
            'icon': 'fa-key',
            'token_key': 'access_token',
            'remote_app': {
              'client_id': 'superset',
              'client_secret': "ref+k8s://v1/Secret/kosmos-data/superset-oidc-secret/client_secret+",
              'client_kwargs': {
                  'scope': 'openid email profile',
                  'verify': False
              },

              "access_token_url": "https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/token",
              "authorize_url": "https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/auth",
              "request_token_url": None,
              "jwks_uri":"https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/certs",
              "api_base_url": 'https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect',

            },
          }
        ]

        AUTH_ROLES_MAPPING = {
          "superset_alpha": ["Alpha"],
          "superset_gamma": ["Gamma"],
          "superset_admin": ["Admin"],
        }

        AUTH_USER_REGISTRATION = True
        AUTH_USER_REGISTRATION_ROLE = "Alpha"
        AUTH_ROLES_SYNC_AT_LOGIN = True
