# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
        "realm": "kosmos",
        "groups": [],
        "clients": [
        {
              "clientId": "rancher",
              "name": "rancher",
              "description": "",
              "rootUrl": "",
              "adminUrl": "",
              "baseUrl": "",
              "surrogateAuthRequired": false,
              "enabled": true,
              "alwaysDisplayInConsole": false,
              "clientAuthenticatorType": "client-secret",
              "redirectUris": [
                "https://rancher.{{ .StateValues.domain }}/verify-auth"
              ],
              "webOrigins": [
                "https://rancher.{{ .StateValues.domain }}"
              ],
              "notBefore": 0,
              "bearerOnly": false,
              "consentRequired": false,
              "standardFlowEnabled": true,
              "implicitFlowEnabled": false,
              "directAccessGrantsEnabled": true,
              "serviceAccountsEnabled": false,
              "publicClient": false,
              "frontchannelLogout": true,
              "protocol": "openid-connect",
              "attributes": {
                "realm_client": "false",
                "oidc.ciba.grant.enabled": "false",
                "client.secret.creation.time": "**********",
                "backchannel.logout.session.required": "true",
                "oauth2.device.authorization.grant.enabled": "false",
                "backchannel.logout.revoke.offline.tokens": "false"
              },
              "authenticationFlowBindingOverrides": {},
              "fullScopeAllowed": true,
              "nodeReRegistrationTimeout": -1,
              "protocolMappers": [
                {
                  "name": "Group Path",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-group-membership-mapper",
                  "consentRequired": false,
                  "config": {
                    "full.path": "true",
                    "introspection.token.claim": "true",
                    "userinfo.token.claim": "true",
                    "multivalued": "true",
                    "id.token.claim": "true",
                    "lightweight.claim": "false",
                    "access.token.claim": "true",
                    "claim.name": "full_group_path"
                  }
                },
                {
                  "name": "Group mapper",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-group-membership-mapper",
                  "consentRequired": false,
                  "config": {
                    "full.path": "false",
                    "introspection.token.claim": "true",
                    "userinfo.token.claim": "true",
                    "multivalued": "true",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "access.token.claim": "false",
                    "claim.name": "groups"
                  }
                },
                {
                  "name": "Client Audience",
                  "protocol": "openid-connect",
                  "protocolMapper": "oidc-audience-mapper",
                  "consentRequired": false,
                  "config": {
                    "included.client.audience": "rancher",
                    "id.token.claim": "false",
                    "lightweight.claim": "false",
                    "access.token.claim": "true",
                    "introspection.token.claim": "true"
                  }
                }
              ],
              "defaultClientScopes": [
                "web-origins",
                "acr",
                "roles",
                "profile",
                "basic",
                "email"
              ],
              "optionalClientScopes": [
                "address",
                "phone",
                "organization",
                "offline_access",
                "microprofile-jwt"
              ]
            }
        ],
        "clientScopes": [],
        "scopeMappings": [],
        "clientScopeMappings": {},
        "roles": {
            "realm": [],
            "client": {}
        },
        "users": [
            {
                "enabled": true,
                "username": "ref+k8s://v1/Secret/cattle-system/rancher-admin-oidc/username+",
                "email": "ref+k8s://v1/Secret/cattle-system/rancher-admin-oidc/<EMAIL>",
                "emailVerified": true,
                "groups": [
                  "adminsysteme",
                  "adminsecurite",
                  "admininfra",
                  "dataing"
                ],
                "firstName": "admin",
                "lastName": "rancher",
                "attributes": {},
                "requiredActions": [],
                "realmRoles": [],
                "clientRoles": {
                    "realm-management": [
                      "view-users",
                      "query-users",
                      "query-groups"
                    ]
                },
                "credentials": [
                    {
                        "type": "password",
                        "value": "ref+k8s://v1/Secret/cattle-system/rancher-admin-oidc/password+",
                        "temporary": false
                    }
                ]
            }
        ]
    }
