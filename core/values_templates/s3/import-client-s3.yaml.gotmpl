# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
      "realm": "kosmos",
      "groups": [
        {
          "name": "adminsysteme",
          "path": "/adminsysteme",
          "attributes": {
            "policyMinio": [
              "consoleAdmin"
            ]
          },
          "realmRoles": [],
          "clientRoles": {},
          "subGroups": []
        }
      ],
      "clients": [
        {
          "clientId": "s3",
          "name": "s3",
          "description": "",
          "rootUrl": "https://s3.{{ .StateValues.domain }}/",
          "adminUrl": "",
          "baseUrl": "",
          "surrogateAuthRequired": false,
          "enabled": true,
          "alwaysDisplayInConsole": true,
          "clientAuthenticatorType": "client-secret",
          "secret": "ref+k8s://v1/Secret/kosmos-s3/minio-oidc-secret/MINIO_IDENTITY_OPENID_CLIENT_SECRET_KC+",
          "redirectUris": [
            "https://s3.{{ .StateValues.domain }}/oauth_callback"
          ],
          "webOrigins": [
            "https://s3.{{ .StateValues.domain }}/"
          ],
          "notBefore": 0,
          "bearerOnly": false,
          "consentRequired": false,
          "standardFlowEnabled": true,
          "implicitFlowEnabled": false,
          "directAccessGrantsEnabled": true,
          "authorizationServicesEnabled": true,
          "oauth2.token.exchange.grant.enabled": "true",
          "serviceAccountsEnabled": false,
          "publicClient": false,
          "frontchannelLogout": true,
          "protocol": "openid-connect",
          "attributes": {
            "standard.token.exchange.enabled": "true",
            "client.introspection.response.allow.jwt.claim.enabled": "false",
            "post.logout.redirect.uris": "https://s3.{{ .StateValues.domain }}/",
            "oauth2.device.authorization.grant.enabled": "false",
            "use.jwks.url": "false",
            "backchannel.logout.revoke.offline.tokens": "false",
            "use.refresh.tokens": "true",
            "realm_client": "false",
            "oidc.ciba.grant.enabled": "false",
            "client.use.lightweight.access.token.enabled": "false",
            "backchannel.logout.session.required": "true",
            "client_credentials.use_refresh_token": "false",
            "acr.loa.map": "{}",
            "require.pushed.authorization.requests": "false",
            "tls.client.certificate.bound.access.tokens": "false",
            "display.on.consent.screen": "false",
            "token.response.type.bearer.lower-case": "false"
          },
          "authenticationFlowBindingOverrides": {},
          "fullScopeAllowed": true,
          "nodeReRegistrationTimeout": -1,
          "defaultClientScopes": [
            "web-origins",
            "acr",
            "roles",
            "profile",
            "minio-authorization",
            "basic",
            "email"
          ],
          "optionalClientScopes": [
            "address",
            "phone",
            "offline_access",
            "organization",
            "microprofile-jwt"
          ]
        }
      ],
      "clientScopes": [
        {
          "name": "minio-authorization",
          "description": "",
          "protocol": "openid-connect",
          "attributes": {
            "include.in.token.scope": "true",
            "display.on.consent.screen": "true",
            "gui.order": "",
            "consent.screen.text": ""
          },
          "protocolMappers": [
            {
              "name": "minio-policy-mapper",
              "protocol": "openid-connect",
              "protocolMapper": "oidc-usermodel-attribute-mapper",
              "consentRequired": false,
              "config": {
                "aggregate.attrs": "true",
                "introspection.token.claim": "true",
                "multivalued": "true",
                "userinfo.token.claim": "true",
                "user.attribute": "policyMinio",
                "id.token.claim": "true",
                "lightweight.claim": "false",
                "access.token.claim": "true",
                "claim.name": "policyminio",
                "jsonType.label": "String"
              }
            }
          ]
        }
      ],
      "scopeMappings": [],
      "clientScopeMappings": {},
      "roles": {
        "realm": [],
        "client": {}
      },
      "users": []
    }
