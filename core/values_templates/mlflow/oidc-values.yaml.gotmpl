config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/keycloak-cluster-initial-admin/username
    password: ref+k8s://v1/Secret/kosmos-iam/keycloak-cluster-initial-admin/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local
    skipCertCheck: false
  import: |
    {
      "realm": "kosmos",
      "roles": {
        "realm": [],
        "client": {}
      },
      "clients": [
        {
          "clientId": "ref+k8s://v1/Secret/kosmos-data/mlflow-secrets/clientId",
          "secret": "ref+k8s://v1/Secret/kosmos-data/mlflow-secrets/clientSecret",
          "enabled": true,
          "clientAuthenticatorType": "client-secret",
          "redirectUris": [
            "https://mlflow.{{ .StateValues.domain }}/*",
            "https://mlflow.{{ .StateValues.domain }}/oauth2/*",
            "http://mlflow.{{ .StateValues.domain }}/*",
            "/*",
            "https://mlflow.{{ .StateValues.domain }}/oauth2/callback"
          ],
          "webOrigins": [
            "*",
            "/*"
          ],
          "publicClient": false,
          "serviceAccountsEnabled": true,
          "protocol": "openid-connect",
          "fullScopeAllowed": true,
          "protocolMappers": [
            {
              "name": "groups",
              "protocol": "openid-connect",
              "protocolMapper": "oidc-usermodel-realm-role-mapper",
              "consentRequired": false,
              "config": {
                "introspection.token.claim": "true",
                "multivalued": "true",
                "userinfo.token.claim": "true",
                "user.attribute": "foo",
                "id.token.claim": "true",
                "lightweight.claim": "false",
                "access.token.claim": "true",
                "claim.name": "groups",
                "jsonType.label": "String"
              }
            }
          ]
        }
      ]
    }
