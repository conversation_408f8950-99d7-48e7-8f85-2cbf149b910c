mlflow:
  auth:
    keycloakClient: ref+k8s://v1/Secret/kosmos-data/mlflow-secrets/clientId
    internalRealmUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local/realms/kosmos
    roleMappings:
      admin: [ adminsysteme ]
      MANAGE: [ admininfra ]
      EDIT: [ 'dataing' ]
      READ: [ adminsecurite ]

storage:
  backend:
    endpoint: pgcluster-rw.kosmos-sql.svc.cluster.local:5432
    dbUsername: ref+k8s://v1/Secret/kosmos-sql/mlflow-initpg-secret/app_db_user
    dbPassword: ref+k8s://v1/Secret/kosmos-sql/mlflow-initpg-secret/app_db_password

ingress:
  hosts:
    - mlflow.{{ .StateValues.domain }}
  tls:
    - secretName: mlflow-tls-secret
      hosts:
        - mlflow.{{ .StateValues.domain }}

proxy:
  enabled: true
  oidc:
    clientId: ref+k8s://v1/Secret/kosmos-data/mlflow-secrets/clientId
    clientSecret: ref+k8s://v1/Secret/kosmos-data/mlflow-secrets/clientSecret
    cookieSecret: ref+k8s://v1/Secret/kosmos-data/mlflow-secrets/cookieSecret
    redirectUrl: https://mlflow.{{ .StateValues.domain }}/oauth2/callback
    oidcIssuerUrl: https://auth.{{ .StateValues.domain }}/realms/kosmos
    loginUrl: https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/auth
    redeemUrl: https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/token
    validateUrl: https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/userinfo
    oidcJwksUrl: https://auth.{{ .StateValues.domain }}/realms/kosmos/protocol/openid-connect/certs
    allowedGroups: [adminsysteme, adminsecurite, admininfra, dataing ]
