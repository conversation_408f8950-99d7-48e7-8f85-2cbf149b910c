config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
      "realm": "kosmos",
      "groups": [
        {
          "name": "dataing",
          "path": "/dataing",
          "attributes": {
            "policyMinio": ["consoleAdmin"]
          }
        },
        {
          "name": "adminsysteme",
          "path": "/adminsysteme",
          "attributes": {
            "policyMinio": ["consoleAdmin"]
          }
        }
      ],
      "clientScopes": [
        {
          "name": "groups",
          "description": "Include user groups in token",
          "protocol": "openid-connect",
          "attributes": {
            "include.in.token.scope": "true",
            "display.on.consent.screen": "false"
          },
          "protocolMappers": [
            {
              "name": "groups",
              "protocol": "openid-connect",
              "protocolMapper": "oidc-group-membership-mapper",
              "consentRequired": false,
              "config": {
                "full.path": "false",
                "multivalued": "true",
                "id.token.claim": "true",
                "access.token.claim": "true",
                "userinfo.token.claim": "true",
                "claim.name": "groups"
              }
            }
          ]
        },
        {
          "name": "aud-s3-kosmos",
          "description": "Adds s3-kosmos to the aud claim",
          "protocol": "openid-connect",
          "attributes": {
            "include.in.token.scope": "true",
            "display.on.consent.screen": "false",
            "consent.screen.text": "",
            "gui.order": ""
          },
          "protocolMappers": [
            {
              "name": "aud-s3-kosmos-mapper",
              "protocol": "openid-connect",
              "protocolMapper": "oidc-audience-mapper",
              "consentRequired": false,
              "config": {
                "included.client.audience": "s3-kosmos",
                "id.token.claim": "true",
                "access.token.claim": "true"
              }
            }
          ]
        }
      ],
      "users": [
        {
          "username": "testuser1",
          "enabled": true,
          "emailVerified": true,
          "firstName": "testuser1",
          "lastName": "testuser1",
          "email": "<EMAIL>",
          "credentials": [
            {
              "type": "password",
              "value": "test",
              "temporary": false
            }
          ],
          "requiredActions": [],
          "realmRoles": [
            "offline_access",
            "uma_authorization"
          ],
          "clientRoles": {},
          "groups": [
            "dataing"
          ]
        },
        {
          "username": "testuser2",
          "enabled": true,
          "emailVerified": true,
          "firstName": "testuser2",
          "lastName": "testuser2",
          "email": "<EMAIL>",
          "credentials": [
            {
              "type": "password",
              "value": "test",
              "temporary": false
            }
          ],
          "requiredActions": [],
          "realmRoles": [
            "offline_access",
            "uma_authorization"
          ],
          "clientRoles": {},
          "groups": [
            "adminsysteme"
          ]
        },
        {
          "username": "testuser3",
          "enabled": true,
          "emailVerified": true,
          "firstName": "testuser3",
          "lastName": "testuser3",
          "email": "<EMAIL>",
          "credentials": [
            {
              "type": "password",
              "value": "test",
              "temporary": false
            }
          ],
          "requiredActions": [],
          "realmRoles": [
            "offline_access",
            "uma_authorization"
          ],
          "clientRoles": {},
          "groups": [
            "dataing"
          ]
        }
      ],
      "clients": [
        {
          "clientId": "kosmos-studio",
          "name": "",
          "rootUrl": "https://kosmos-studio.{{ .StateValues.domain }}",
          "adminUrl": "https://kosmos-studio.{{ .StateValues.domain }}",
          "baseUrl": "https://kosmos-studio.{{ .StateValues.domain }}",
          "surrogateAuthRequired": false,
          "enabled": true,
          "alwaysDisplayInConsole": false,
          "clientAuthenticatorType": "client-secret",
          "redirectUris": [
            "https://kosmos-studio.{{ .StateValues.domain }}/*"
          ],
          "webOrigins": [
            "https://kosmos-studio.{{ .StateValues.domain }}"
          ],
          "notBefore": 0,
          "bearerOnly": false,
          "consentRequired": false,
          "standardFlowEnabled": true,
          "implicitFlowEnabled": false,
          "directAccessGrantsEnabled": true,
          "serviceAccountsEnabled": false,
          "publicClient": true,
          "frontchannelLogout": true,
          "protocol": "openid-connect",
          "attributes": {
            "realm_client": "false",
            "oidc.ciba.grant.enabled": "false",
            "backchannel.logout.session.required": "true",
            "standard.token.exchange.enabled": "false",
            "frontchannel.logout.session.required": "true",
            "display.on.consent.screen": "false",
            "oauth2.device.authorization.grant.enabled": "false",
            "backchannel.logout.revoke.offline.tokens": "false"
          },
          "authenticationFlowBindingOverrides": {},
          "fullScopeAllowed": true,
          "nodeReRegistrationTimeout": -1,
          "defaultClientScopes": [
            "web-origins",
            "acr",
            "aud-s3-kosmos",
            "roles",
            "profile",
            "groups",
            "basic",
            "email"
          ],
          "optionalClientScopes": [
            "address",
            "phone",
            "offline_access",
            "organization",
            "microprofile-jwt"
          ],
          "access": {
            "view": true,
            "configure": true,
            "manage": true
          }
        }
      ]
    }
