# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
        "id": "kosmos",
        "realm": "kosmos",
        "roles": {
            "realm": [],
            "client": {}
        },
        "groups": [],
        "clients": [
            {
                "clientId": "synapse",
                "name": "synapse",
                "description": "",
                "rootUrl": "",
                "adminUrl": "",
                "baseUrl": "",
                "surrogateAuthRequired": false,
                "enabled": true,
                "alwaysDisplayInConsole": true,
                "clientAuthenticatorType": "client-secret",
                "secret": "ref+k8s://v1/Secret/kosmos-collab/synapse-oidc/oidc-secret+",
                "redirectUris": [
                    "https://synapse.{{ .StateValues.domain }}/_synapse/client/oidc/callback"
                ],
                "webOrigins": [
                    "/*"
                ],
                "notBefore": 0,
                "bearerOnly": false,
                "consentRequired": false,
                "standardFlowEnabled": true,
                "implicitFlowEnabled": false,
                "directAccessGrantsEnabled": true,
                "serviceAccountsEnabled": false,
                "publicClient": false,
                "frontchannelLogout": false,
                "protocol": "openid-connect",
                "attributes": {
                    "realm_client": "false",
                    "oidc.ciba.grant.enabled": "false",
                    "client.secret.creation.time": "**********",
                    "backchannel.logout.session.required": "true",
                    "backchannel.logout.url": "http://synapse-matrix-synapse-kosmos-matrix.kosmos-collab.svc.cluster.local/_synapse/client/oidc/backchannel_logout",
                    "post.logout.redirect.uris": "https://element.{{ .StateValues.domain }}/#/login",
                    "oauth2.device.authorization.grant.enabled": "false",
                    "display.on.consent.screen": "false",
                    "use.jwks.url": "false",
                    "backchannel.logout.revoke.offline.tokens": "true"
                },
                "authenticationFlowBindingOverrides": {},
                "fullScopeAllowed": true,
                "nodeReRegistrationTimeout": -1,
                "defaultClientScopes": [
                    "web-origins",
                    "acr",
                    "profile",
                    "roles",
                    "basic",
                    "email"
                ],
                "optionalClientScopes": [
                    "address",
                    "phone",
                    "offline_access",
                    "organization",
                    "microprofile-jwt"
                ]
            }
        ]
    }
