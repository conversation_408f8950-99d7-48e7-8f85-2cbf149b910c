hostname: synapse.{{ .StateValues.domain }}
persistence:
  size: 10Gi
sso:
  hostname: auth.{{ .StateValues.domain }}
  clientSecret: ref+k8s://v1/Secret/kosmos-collab/synapse-oidc/oidc-secret
db:
  name: "synapse"
  user: ref+k8s://v1/Secret/kosmos-sql/initpg-synapse-secret/app_db_user
  password: ref+k8s://v1/Secret/kosmos-sql/initpg-synapse-secret/app_db_password
  host: "pgcluster-rw.kosmos-sql.svc.cluster.local"

ingress:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
