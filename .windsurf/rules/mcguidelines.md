---
trigger: always_on
---

{
    "@context": {
        "@vocab": "https://schema.org/",
        "name": "mc_coding_rules",
        "description": "description",
        "uuid": "urn:uuid:mc_coding_rules",
        "labels": "keywords",
        "tags": "about",
        "metadata": "additionalProperty",
        "weight": "additionalProperty"
    },
    "@type": "CodingRules",
    "uuid": "urn:uuid:3d97ca1f-63d2-4a89-b6ec-370e40f7fdd8",
    "version": "3.0.0",
    "name": "AI Coding Agent Framework (Final + Weighted)",
    "description": "A comprehensive, final version of the AI Coding Agent Framework, covering governance, memory, compliance, code quality, security, observability, performance, operational excellence, AI orchestration, data privacy, continuity, user experience, and more—with integrated rule weights for risk-based prioritization.",
    "hasPart": [
        {
            "@type": "ItemList",
            "@id": "https://ckodex.com/rs1",
            "uuid": "urn:uuid:5a4f8b08-b253-42a2-b10d-55212b75fdae",
            "name": "RS1: Holistic Knowledge Orchestration",
            "description": "Establish a Unified Knowledge Continuum and polyoperator guidelines for enterprise-scale software governance.",
            "labels": [
                "governance",
                "knowledge-orchestration"
            ],
            "metadata": {
                "domain": "governance",
                "priority": "high"
            },
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:398b7f55-4252-43c7-9c87-1e295c24598e",
                    "position": 1,
                    "name": "RS1.1",
                    "description": "Establish a Unified Knowledge Continuum encompassing Core Guidelines, Project Cognitive Model, Contextual Memory System, Verification Protocol, and Execution Framework.",
                    "tags": [
                        "knowledge",
                        "continuum"
                    ],
                    "metadata": {
                        "domain": "governance",
                        "phase": "DESIGN",
                        "complexity": "MODERATE",
                        "severity": "critical",
                        "weight": 10
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:dc6deef4-5e14-426f-a008-5fe67f08bd9a",
                    "position": 2,
                    "name": "RS1.2",
                    "description": "Implement PolyOperator Guidelines v2.0.0 as the authoritative foundation for all development activities, encompassing coding standards, security, API design, containerization, observability, ML operations, performance engineering, data privacy, and legal compliance.",
                    "tags": [
                        "polyoperator",
                        "guidelines"
                    ],
                    "metadata": {
                        "domain": "governance",
                        "phase": "IMPLEMENT",
                        "complexity": "COMPLEX",
                        "severity": "high",
                        "weight": 9
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:763bd507-66bf-47f6-aabd-21ee0f873b64",
                    "position": 3,
                    "name": "RS1.3",
                    "description": "Structure knowledge in a semantically linked JSON-LD knowledge graph with cross-referenced categories and rules, enabling precise querying and rule application through context-aware ID resolution (categories/{category}/{rule-id}).",
                    "tags": [
                        "json-ld",
                        "semantic-graph"
                    ],
                    "metadata": {
                        "domain": "governance",
                        "phase": "PLAN",
                        "complexity": "MODERATE",
                        "severity": "medium",
                        "weight": 7
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:abaee442-0f84-4299-be8c-9679e1bc90c1",
                    "position": 4,
                    "name": "RS1.4",
                    "description": "Establish hierarchical @context definitions mapping all concepts to schema.org terms and domain-specific ontologies for machine-interpretable semantic understanding.",
                    "tags": [
                        "hierarchical-context",
                        "machine-interpretation"
                    ],
                    "metadata": {
                        "domain": "governance",
                        "phase": "PLAN",
                        "complexity": "MODERATE",
                        "severity": "medium",
                        "weight": 6
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:4de093f3-2d34-4ffa-a31f-3235d9912803",
                    "position": 5,
                    "name": "RS1.5",
                    "description": "Deploy a distributed gRPC Rule Engine for centralized rule evaluation, compliance verification, and dynamic rule updating with full audit capabilities.",
                    "tags": [
                        "grpc",
                        "rule-engine"
                    ],
                    "metadata": {
                        "domain": "governance",
                        "phase": "IMPLEMENT",
                        "complexity": "COMPLEX",
                        "severity": "critical",
                        "weight": 10
                    }
                }
            ]
        },
        {
            "@type": "ItemList",
            "@id": "https://ckodex.com/rs2",
            "uuid": "urn:uuid:b5af18e4-81db-4261-9cd4-951abf16ee76",
            "name": "RS2: Multi-dimensional Memory Architecture",
            "description": "Maintain a four-tier memory system, metamemory indexes, checkpointing, specialized knowledge domains, and rich metadata for artifacts.",
            "labels": [
                "memory",
                "architecture"
            ],
            "metadata": {
                "domain": "architecture",
                "priority": "high"
            },
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:0577c844-32bc-4ff3-89ff-fa9a69c6e216",
                    "position": 1,
                    "name": "RS2.1",
                    "description": "Implement a four-tiered memory system combining: Ephemeral Context Cache, Persistent Document Store, Vector Embedding Database, and Graph Knowledge Base.",
                    "tags": [
                        "ephemeral-cache",
                        "document-store",
                        "vector-db",
                        "graph"
                    ],
                    "metadata": {
                        "domain": "architecture",
                        "phase": "ANALYZE",
                        "complexity": "COMPLEX",
                        "severity": "high",
                        "weight": 9
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:92a66afa-22dd-4fe4-aeab-b780b3a60bdb",
                    "position": 2,
                    "name": "RS2.2",
                    "description": "Maintain metamemory indexes tracking cognitive linkages between disparate components to prevent knowledge fragmentation and enable holistic understanding.",
                    "tags": [
                        "metamemory",
                        "linkage"
                    ],
                    "metadata": {
                        "domain": "architecture",
                        "phase": "ANALYZE",
                        "complexity": "MODERATE",
                        "severity": "medium",
                        "weight": 7
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:67d939d9-a289-49ce-9d31-a4882a17244e",
                    "position": 3,
                    "name": "RS2.3",
                    "description": "Implement temporal checkpointing (CP-YY.MM.DD-N) with three-dimensional state capture: functional state, decision context, and compliance status.",
                    "tags": [
                        "checkpointing",
                        "time-travel"
                    ],
                    "metadata": {
                        "domain": "architecture",
                        "phase": "DESIGN",
                        "complexity": "MODERATE",
                        "severity": "medium",
                        "weight": 6
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:dcb015b4-a38d-4316-a2c8-735f43b55874",
                    "position": 4,
                    "name": "RS2.4",
                    "description": "Create specialized knowledge domains with .memory/{domain}/ namespaces for project, architecture, patterns, decisions, technical debt, and compliance tracking.",
                    "tags": [
                        "namespaces",
                        "knowledge-domains"
                    ],
                    "metadata": {
                        "domain": "architecture",
                        "phase": "DESIGN",
                        "complexity": "SIMPLE",
                        "severity": "low",
                        "weight": 4
                    }
                },
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:ee99cee9-094a-49fa-bad0-e3318e4c0343",
                    "position": 5,
                    "name": "RS2.5",
                    "description": "Generate rich metadata for all artifacts including purpose, dependencies, versioning, security implications, performance characteristics, and compliance attestations.",
                    "tags": [
                        "metadata",
                        "artifacts"
                    ],
                    "metadata": {
                        "domain": "architecture",
                        "phase": "IMPLEMENT",
                        "complexity": "MODERATE",
                        "severity": "medium",
                        "weight": 7
                    }
                }
            ]
        },
        {
            "@type": "ItemList",
            "@id": "https://ckodex.com/rs3",
            "uuid": "urn:uuid:a3fc3f28-b345-4d2b-b6ee-d42671250c02",
            "name": "RS3: Milestone-Driven Development Framework",
            "description": "Comprehensive lifecycle framework with dynamic rule subsets, milestone deliverables, continuous verification, and traceability matrix.",
            "labels": [
                "development",
                "milestones"
            ],
            "metadata": {
                "domain": "process",
                "priority": "high"
            },
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "uuid": "urn:uuid:8845a6c5-095a-44b3-8b28-534df209a193",
                    "position": 1,
                    "name": "RS3.1",
                    "description": "Orchestrate development through a comprehensive lifecycle framework spanning Discovery, Architecture, MVP Implementation, Extended Features, Compliance Verification, Deployment, Maintenance, and Controlled Retirement.",
                    "tags": [
                        "lifecycle",
                        "orchestration"
                    ],
                    "metad