# Platform Provisioner (core/) – Architecture, Deployment Order, State and Dependencies

Updated: 2025-08-12
Owner: Platform Engineering

## 1) Executive Summary

The platform is deployed through a set of Helmfiles under `core/` in a layered sequence to reduce risk and make dependencies explicit. Helmfile `needs:` graphs and readiness gates (`wait`, `waitForJobs`) ensure CRDs, operators, and dependent workloads converge safely. Security posture is strong (secret references, image pinning); we propose standardizing TLS annotations, adding NetworkPolicies, and instituting CI policy checks to reach enterprise-grade reliability and compliance.

Key benefits for leadership:
- Predictable rollouts with explicit order and checks
- Clear dependency rationale and diagrams for audit/readiness
- Actionable scripts and guidance for safe, repeatable deployments


## 2) System Overview

Core orchestration manifests (relative to repo root):
- `core/helmfile_core.yaml.gotmpl`
- `core/helmfile_dhx.yaml.gotmpl`
- `core/helmfile_ai.yaml.gotmpl`
- `core/helmfile_gpu.yaml.gotmpl`
- `core/helmfile_abac.yaml.gotmpl`
- `core/helmfile_scaleway.yaml.gotmpl`

Representative application charts (under `kosmos_apps/`):
- `kosmos_apps/cert-manager/`, `kosmos_apps/lvm-csi/`, `kosmos_apps/zot/`
- `kosmos_apps/monitoring/`, `kosmos_apps/postgresql/`, `kosmos_apps/keycloak/`
- `kosmos_apps/keycloakimporter/`, `kosmos_apps/s3/`, `kosmos_apps/pgadmin/`
- `kosmos_apps/gitea/`, `kosmos_apps/jupyterhub4/`, `kosmos_apps/open-webui/`
- `kosmos_apps/gpu-operator/`, `kosmos_apps/rancher/`, plus others (superset, redis, vector, model-serving, clickhouse)


## 3) Layered Architecture (Logical)

```mermaid
--- 
title: Layered Architecture (Logical)
config:
  layout: elk
  look: neo
  theme: neo


---
flowchart TB
  subgraph L1["Layer 1: Foundation"]
    NS[Namespaces]
    CM[cert-manager]
    LVM[lvm-csi TopoLVM]
    REG[zot registry]
  end

  subgraph L2["Layer 2: Data/IAM/Monitoring"]
    CRD[monitoring CRDs]
    CNPG[Postgres operator]
    PG[Postgres cluster]
    KCOP[Keycloak operator]
    KC[Keycloak cluster]
    S3OP[S3 operator]
    S3TEN[S3 tenant/cluster]
    GRAF[Grafana/Monitoring stack]
    RANCH[Rancher]
    PGADM[PGAdmin]
    GITEA[Gitea]
  end

  subgraph L3["Layer 3: Optional Stacks"]
    GPU[GPU operator]
    AI[Open WebUI]
    KSERV[KServe + SA]
    ABAC[IAD/DCS]
    CLOUD[Scaleway variant]
  end

  NS --> CM --> LVM --> REG
  CRD --> CNPG --> PG --> KC
  KCOP --> KC
  KC --> S3OP
  S3OP --> S3TEN
  CRD --> GRAF
  CNPG --> PGADM
  KC --> GITEA
  RANCH
  GPU
  AI --> KSERV
  ABAC
  CLOUD
```


## 4) Canonical Deployment Order

Top-level sequence across Helmfiles:

1. Foundation – `core/helmfile_core.yaml.gotmpl`
2. Data, IAM, Monitoring – `core/helmfile_dhx.yaml.gotmpl`
3. Optional: GPU – `core/helmfile_gpu.yaml.gotmpl`
4. Optional: AI – `core/helmfile_ai.yaml.gotmpl`
5. Optional: ABAC – `core/helmfile_abac.yaml.gotmpl`
6. Cloud Variant (profile) – `core/helmfile_scaleway.yaml.gotmpl`

Rationale:
- CRDs must exist before operators; operators before custom resources.
- Storage class and namespaces must precede workloads creating PVCs.
- Keycloak realm/clients must precede OIDC-integrated apps (Gitea, PGAdmin, JupyterHub, Open WebUI, MinIO Console).


## 5) Detailed Topology (per Helmfile)

### 5.1 `core/helmfile_core.yaml.gotmpl`
- cert-manager
- cert-manager-init → cert-manager
- kosmos-namespaces (declarative namespaces)
- lvm-csi → [cert-manager-init, kosmos-namespaces]
- kosmos-registry-secrets
- kosmos-registry → [lvm-csi, kosmos-registry-secrets]

### 5.2 `core/helmfile_dhx.yaml.gotmpl`
- monitoringcrds
- cnpg → monitoringcrds
- pgcluster → cnpg
- keycloak-operator → keycloak-cluster → keycloak-secrets
- realm and client importers depend on `keycloak-secrets`/`keycloak-cluster`
- rancher-secrets → rancher
- pgadmin4-secrets → pgadmin4
- grafana-secrets → monitoring-stack
- s3-operator → s3-secrets → keycloakimporter-import-s3 → s3-cluster
- gitea-secrets → keycloakimporter-import-gitea → gitea
- jupyterhub-secrets → keycloakimporter-jupyterhub → jupyterhub

### 5.3 `core/helmfile_gpu.yaml.gotmpl`
- monitoringcrds
- gpu-operator → monitoringcrds

### 5.4 `core/helmfile_ai.yaml.gotmpl`
- open-webui-secrets → keycloakimporter-open-webui → open-webui
- kserve-crd → kserve, kserve-sa
- Note: replace presync `kubectl apply namespaces/kosmos-ai.yaml` with namespaces chart for full declarative state

### 5.5 `core/helmfile_abac.yaml.gotmpl`
- iad-s3, dcs (consume S3) → keycloakimporter-kosmos-studio (ensure realm exists)

### 5.6 `core/helmfile_scaleway.yaml.gotmpl` (variant)
- monitoringcrds → gpu-operator
- rancher-secrets → rancher
- cnpg → pgcluster
- s3-operator → s3-secrets → s3-cluster
- pgadmin4-secrets → pgadmin4
- keycloak-operator → keycloak-cluster
- keycloakimporter-kosmos-studio
- kosmos-studio-secrets → kosmos-studio
- tileserver, kosmos-doc


## 6) State and Dependency Management

- Helmfile environments: `environments.default.values` provide `domain` and similar parameters, referenced as `{{ .StateValues.domain }}`.
- Helm state: stored in-cluster (Kubernetes Secrets) per release/namespace (Helm default).
- Readiness: `wait: true` and `waitForJobs: true` widely used to block until controllers/Jobs are ready.
- Dependencies: Helmfile `needs:` defines explicit DAGs (e.g., CRDs → operators → clusters). Cross-Helmfile order is enforced by our documented sequence.
- Chart deps: several releases use `skipDeps: true`. This requires governance via pinned versions and CI guardrails (see §8).


## 7) Operational Commands

Full syncs:
```bash
helmfile -f core/helmfile_core.yaml.gotmpl sync
helmfile -f core/helmfile_dhx.yaml.gotmpl sync
# Optional stacks
helmfile -f core/helmfile_gpu.yaml.gotmpl sync
helmfile -f core/helmfile_ai.yaml.gotmpl sync
helmfile -f core/helmfile_abac.yaml.gotmpl sync
# Variant
helmfile -f core/helmfile_scaleway.yaml.gotmpl sync
```

Scoped syncs by label (examples):
```bash
helmfile -f core/helmfile_dhx.yaml.gotmpl sync -l app=postgresql
helmfile -f core/helmfile_dhx.yaml.gotmpl sync -l app=keycloak
helmfile -f core/helmfile_core.yaml.gotmpl sync -l app=zot
```

Post-sync readiness examples:
```bash
# Wait for all pods of a Helm release (by instance label) in a namespace
kubectl -n kosmos-iam wait --for=condition=Ready pod -l app.kubernetes.io/instance=keycloak-cluster --timeout=600s

# Ensure MinIO Tenant CRD is established (before tenant readiness)
kubectl wait --for=condition=Established crd/tenants.minio.min.io --timeout=120s
```


## 8) Security, Compliance, and Policy Gates

- TLS/Ingress:
  - Standardize `cert-manager.io/cluster-issuer` across all ingresses in `core/` values and chart overlays.
  - Ensure internal service traffic is encrypted (TLS or service mesh mTLS) where applicable.

- NetworkPolicies:
  - Add default-deny per namespace; then explicit allows for DNS, ingress controllers, monitoring, and required app-to-app flows (e.g., apps → Keycloak, apps → S3, apps → Postgres).

- Policy and schema validation (pre-commit + CI):
  - Pre-commit: `yamllint`, `helm lint`, `kubeconform` (w/ CRD schemas), `chart-testing (ct)`, `conftest` (OPA/Kyverno policies).
  - CI pipeline: template all releases per Helmfile, then run schema + policy checks; block merges on failures.
  - Example policies: disallow `:latest` tags; require TLS issuer annotations on Ingress; require NetworkPolicy in each namespace; restrict `hostNetwork`, `hostPath`, privileged pods.

- Secrets management:
  - Continue referencing secrets via Kubernetes Secrets (e.g., `ref+k8s://...`). Avoid embedding secrets in values files.


## 9) Risks and Mitigations

- CRD/operator races – mitigated by `needs:` ordering and readiness waits.
- Dependency drift with `skipDeps: true` – mitigate with Renovate for chart/image pinning and periodic dependency validation.
- Inconsistent TLS and annotations – mitigate with standardized issuer and ingress annotations in shared values overlays.
- Network exposure – default-deny NetworkPolicies with explicit allow lists.


## 10) 90‑Day Roadmap

- P0 (Weeks 1–2)
  - Standardize cert-manager issuer annotations across all ingresses.
  - Replace remaining imperative hooks (e.g., in `core/helmfile_ai.yaml.gotmpl`) with declarative namespaces chart.
  - Introduce baseline NetworkPolicies.

- P1 (Weeks 3–6)
  - Add `.pre-commit-config.yaml` and CI gates (schema, policy, chart-testing).
  - Enable Renovate for Helm chart and image updates; document `skipDeps` governance.

- P2 (Weeks 7–12)
  - SRE runbooks: Keycloak, S3, Postgres backup/restore; upgrade guides.
  - Observability baselines: dashboards + alerts for critical components (Keycloak, S3, Postgres, Prometheus, Grafana).


## 11) Appendices

### A. Helmfiles → kosmos_apps Mapping (examples)
- `../../cert-manager/*` → `kosmos_apps/cert-manager/*`
- `../../lvm-csi/topolvm` → `kosmos_apps/lvm-csi/*`
- `../../zot/*` → `kosmos_apps/zot/*`
- `../../monitoring/*` → `kosmos_apps/monitoring/*`
- `../../postgresql/*` → `kosmos_apps/postgresql/*`
- `../../keycloak/*` → `kosmos_apps/keycloak/*`
- `../../keycloakimporter/*` → `kosmos_apps/keycloakimporter/*`
- `../../s3/*` → `kosmos_apps/s3/*`
- `../../pgadmin/*` → `kosmos_apps/pgadmin/*`
- `../../gitea/*` → `kosmos_apps/gitea/*`
- `../../jupyterhub4/*` → `kosmos_apps/jupyterhub4/*`
- `../../open-webui/*` → `kosmos_apps/open-webui/*`
- `../../gpu-operator/*` → `kosmos_apps/gpu-operator/*`
- `../../rancher/*` → `kosmos_apps/rancher/*`

### B. Glossary
- CRD – CustomResourceDefinition
- DAG – Directed Acyclic Graph
- OIDC – OpenID Connect
- mTLS – Mutual TLS

---

For questions or escalation, contact Platform Engineering. This document accompanies the orchestration commands and recommendations to support safe, auditable deployments of the platform’s core services.
