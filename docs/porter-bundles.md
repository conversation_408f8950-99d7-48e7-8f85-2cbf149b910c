# Porter Bundles per UML Layer

This repo provides Porter bundles to deploy each architecture layer independently using the Porter `exec` mixin and Helmfile.

## Bundles

- Foundation (L1): `bundles/foundation/porter.yaml` → `core/helmfile_core.yaml.gotmpl`
- Data/IAM/Monitoring (L2): `bundles/data-iam-monitoring/porter.yaml` → `core/helmfile_dhx.yaml.gotmpl`
- GPU (L3): `bundles/gpu/porter.yaml` → `core/helmfile_gpu.yaml.gotmpl`
- AI (L3): `bundles/ai/porter.yaml` → `core/helmfile_ai.yaml.gotmpl`
- ABAC (L3): `bundles/abac/porter.yaml` → `core/helmfile_abac.yaml.gotmpl`
- Cloud Variant: `bundles/scaleway/porter.yaml` → `core/helmfile_scaleway.yaml.gotmpl`

## Prerequisites

- Porter CLI installed on your workstation or CI runner.
- Invocation image contains `helmfile`, `kubectl`, and `helm`, and includes the repo `core/` at `/workspace/core` (adjust paths in manifests if you use a different layout).
- Kubeconfig provided to the bundle as a credential at `/root/.kube/config` (update the path if needed).

## Usage (examples)

Run Porter commands from within each bundle directory:

1. Build the bundle image
```
porter build
```

2. Install the layer (choose environment)
```
porter install --param helmfile_env=default --cred kubeconfig
```

3. Upgrade or uninstall
```
porter upgrade --param helmfile_env=default --cred kubeconfig
porter uninstall --param helmfile_env=default --cred kubeconfig
```

Optional: target specific releases via Helmfile label selectors (when supported by the bundle):
```
porter install --param selector_label="app=postgresql" --param helmfile_env=default --cred kubeconfig
```

## Notes

- There is no official Porter Helmfile mixin; we use the `exec` mixin to invoke Helmfile directly.
- Ensure remaining imperative hooks (e.g., namespace presync in `core/helmfile_ai.yaml.gotmpl`) are replaced with declarative charts for full idempotence.
- See `docs/manager-report-core.md` for architecture overview, dependencies, and deployment order.
