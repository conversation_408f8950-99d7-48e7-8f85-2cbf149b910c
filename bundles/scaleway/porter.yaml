# Porter Bundle: <PERSON> Variant (Scaleway)
# Prereqs: helmfile, kubectl/helm available; core/ mounted at /workspace/core

schemaVersion: 1.0.0
name: kosmos-scaleway
version: 0.1.0
description: "Deploy Scaleway variant helmfile (combined stack)"

author: "Platform Engineering"
registry: ""

mixins:
  - exec

credentials:
  - name: kubeconfig
    description: "Kubeconfig for target cluster"
    required: true
    path: /root/.kube/config

parameters:
  - name: helmfile_env
    type: string
    default: "default"
    description: "Helmfile environment name"
  - name: selector_label
    type: string
    default: ""
    description: "Optional helmfile label selector"

install:
  - exec:
      description: "Sync Scaleway variant (helmfile_scaleway)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_scaleway.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

upgrade:
  - exec:
      description: "Upgrade Scaleway variant (helmfile_scaleway)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_scaleway.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

uninstall:
  - exec:
      description: "Destroy Scaleway variant (helmfile_scaleway)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_scaleway.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" destroy $SEL_OPTS
