# Porter Bundle: AI Layer (L3)
# Prereqs: helmfile, kubectl/helm available; core/ mounted at /workspace/core
# Note: Replace any imperative namespace hooks in helmfile_ai with declarative namespace chart upstream.

schemaVersion: 1.0.0
name: kosmos-ai
version: 0.1.0
description: "Deploy AI stack: Open WebUI, KServe"

author: "Platform Engineering"
registry: ""

mixins:
  - exec

credentials:
  - name: kubeconfig
    description: "Kubeconfig for target cluster"
    required: true
    path: /root/.kube/config

parameters:
  - name: helmfile_env
    type: string
    default: "default"
    description: "Helmfile environment name"
  - name: selector_label
    type: string
    default: ""
    description: "Optional helmfile label selector, e.g. app=open-webui"

install:
  - exec:
      description: "Sync AI (helmfile_ai)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_ai.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

upgrade:
  - exec:
      description: "Upgrade AI (helmfile_ai)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_ai.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

uninstall:
  - exec:
      description: "Destroy AI (helmfile_ai)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_ai.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" destroy $SEL_OPTS
