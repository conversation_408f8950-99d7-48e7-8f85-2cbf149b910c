# Porter Bundle: <PERSON><PERSON> Layer (L3)
# Prereqs: helmfile, kubectl/helm available; core/ mounted at /workspace/core

schemaVersion: 1.0.0
name: kosmos-abac
version: 0.1.0
description: "Deploy ABAC stack: IAD/DCS and Kosmos Studio importer"

author: "Platform Engineering"
registry: ""

mixins:
  - exec

credentials:
  - name: kubeconfig
    description: "Kubeconfig for target cluster"
    required: true
    path: /root/.kube/config

parameters:
  - name: helmfile_env
    type: string
    default: "default"
    description: "Helmfile environment name"
  - name: selector_label
    type: string
    default: ""
    description: "Optional helmfile label selector"

install:
  - exec:
      description: "Sync ABAC (helmfile_abac)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_abac.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

upgrade:
  - exec:
      description: "Upgrade ABAC (helmfile_abac)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_abac.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

uninstall:
  - exec:
      description: "Destroy ABAC (helmfile_abac)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_abac.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" destroy $SEL_OPTS
