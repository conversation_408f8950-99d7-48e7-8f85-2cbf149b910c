# Porter Bundle: Data/IAM/Monitoring Layer (L2)
# Prereqs:
# - Invocation image includes helmfile in PATH and kubectl/helm as needed
# - Repository core/ directory copied to /workspace/core inside the image

schemaVersion: 1.0.0
name: kosmos-data-iam-monitoring
version: 0.1.0
description: "Deploy Data/IAM/Monitoring Layer: CNPG, Postgres cluster, Keycloak, S3, Monitoring, Rancher, Gitea, PGAdmin, JupyterHub"

author: "Platform Engineering"
registry: ""

mixins:
  - exec

credentials:
  - name: kubeconfig
    description: "Kubeconfig for target cluster"
    required: true
    path: /root/.kube/config

parameters:
  - name: helmfile_env
    type: string
    default: "default"
    description: "Helmfile environment name (matches environments.* in helmfiles)"
  - name: selector_label
    type: string
    default: ""
    description: "Optional helmfile label selector, e.g. app=postgresql"

install:
  - exec:
      description: "Sync Data/IAM/Monitoring (helmfile_dhx)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_dhx.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

upgrade:
  - exec:
      description: "Upgrade Data/IAM/Monitoring (helmfile_dhx)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_dhx.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync $SEL_OPTS

uninstall:
  - exec:
      description: "Destroy Data/IAM/Monitoring (helmfile_dhx)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
        SELECTOR_LABEL: "{{ bundle.parameters.selector_label }}"
      arguments:
        - -ec
        - |
          SEL_OPTS=""; [ -n "$SELECTOR_LABEL" ] && SEL_OPTS="-l $SELECTOR_LABEL"
          helmfile -f /workspace/core/helmfile_dhx.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" destroy $SEL_OPTS
