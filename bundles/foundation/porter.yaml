# Porter Bundle: Foundation Layer (L1)
# Prereqs:
# - Invocation image includes helmfile in PATH and kubectl/helm as needed
# - Repository core/ directory copied to /workspace/core inside the image
#   (adjust paths if you use a different destination)

schemaVersion: 1.0.0
name: kosmos-foundation
version: 0.1.0
description: "Deploy Foundation Layer: namespaces, cert-manager, lvm-csi, zot registry"

author: "Platform Engineering"
registry: ""

mixins:
  - exec

credentials:
  - name: kubeconfig
    description: "Kubeconfig for target cluster"
    required: true
    path: /root/.kube/config

parameters:
  - name: helmfile_env
    type: string
    default: "default"
    description: "Helmfile environment name (matches environments.* in helmfiles)"

install:
  - exec:
      description: "Sync Foundation (helmfile_core)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
      arguments:
        - -ec
        - |
          helmfile -f /workspace/core/helmfile_core.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync

upgrade:
  - exec:
      description: "Upgrade Foundation (helmfile_core)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
      arguments:
        - -ec
        - |
          helmfile -f /workspace/core/helmfile_core.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" sync

uninstall:
  - exec:
      description: "Destroy Foundation (helmfile_core)"
      command: bash
      environment:
        KUBECONFIG: /root/.kube/config
      arguments:
        - -ec
        - |
          helmfile -f /workspace/core/helmfile_core.yaml.gotmpl \
            --environment "{{ bundle.parameters.helmfile_env }}" destroy
