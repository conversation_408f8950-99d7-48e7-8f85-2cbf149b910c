stages:
  - test
  - helm build
  - helmfile update
  - deploy kosmos-dev
  - deploy kosmos-int

include:
  - project: "athea/devops/athea-toolbox"
    ref: "main"
    file: "/templates/test/sonar.gitlab-ci.yml"
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helm/helm.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helmfile/helmfile.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/deployment/deployment.gitlab-ci.yml'

variables:
  VALUES_TARGET_DIR_NAME: none


### ANCHORS
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG =~ /^initpg-\d+\.\d+\.\d+$/
      variables:
        HELM_DIR_PATH: initpg
        HELMFILE_LABEL: initpg=true
    - if: $CI_COMMIT_TAG =~ /^inits3-\d+\.\d+\.\d+$/
      variables:
        HELM_DIR_PATH: inits3
        HELMFILE_LABEL: inits3=true


### SONAR
sonar:
  extends: .sonar
  before_script:
    - helm lint initpg/
    - helm lint inits3/
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - initpg/**/*
        - inits3/**/*


### HELM
helm-build:
  extends: .build_helm
  <<: *tag_rules


### HELMFILE
helmfile-update:
  extends: .helmfile_update
  <<: *tag_rules


### DEPLOY
kosmos-dev-deployment:
  extends: .kosmos_dev_deployment
  rules:
    - if: $CI_COMMIT_TAG =~ /^initpg-\d+\.\d+\.\d+$/
      variables:
        HELMFILE_LABEL: initpg=true
      when: manual
    - if: $CI_COMMIT_TAG =~ /^inits3-\d+\.\d+\.\d+$/
      variables:
        HELMFILE_LABEL: inits3=true
      when: manual

kosmos-int-deployment:
  extends: .kosmos_int_deployment
  needs:
    - helmfile-update
  rules:
    - if: $CI_COMMIT_TAG =~ /^initpg-\d+\.\d+\.\d+$/
      variables:
        HELMFILE_LABEL: initpg=true
      when: manual
    - if: $CI_COMMIT_TAG =~ /^inits3-\d+\.\d+\.\d+$/
      variables:
        HELMFILE_LABEL: inits3=true
      when: manual
