{{- $appBucketUser := default ((printf "%s%s%s" .Values.appBucketUserPrefix "_" (randAlpha 8))) .Values.appBucketUser | lower | b64enc }}
{{- $appBucketPassword := (randAlphaNum 12) | b64enc }}
{{- $existingSecret := (lookup "v1" "Secret" .Release.Namespace (include "inits3.secretName" .)) }}
{{- if $existingSecret }}
  {{- $appBucketUser = index $existingSecret.data "app_bucket_user" }}
  {{- $appBucketPassword = index $existingSecret.data "app_bucket_password" }}
{{- end -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "inits3.secretName" . }}
  labels:
    {{- include "inits3.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-5"
    "helm.sh/resource-policy": "keep"
data:
  app_bucket_user: {{ $appBucketUser }}
  app_bucket_password: {{ $appBucketPassword }}
type: Opaque
