{{/*
Create the job name: {{ .Release.Name }}-job
*/}}
{{- define "inits3.jobName" -}}
{{- printf "%s" .Release.Name | trunc 30  -}}-job
{{- end }}

{{/*
Create the secret name: {{ .Release.Name }}-secret
*/}}
{{- define "inits3.secretName" -}}
{{- printf "%s" .Release.Name | trunc 30 -}}-secret
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "inits3.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Expand the name of the chart.
*/}}
{{- define "inits3.name" -}}
{{- default .Chart.Name | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "inits3.labels" -}}
helm.sh/chart: {{ include "inits3.chart" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/name: {{ include "inits3.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
