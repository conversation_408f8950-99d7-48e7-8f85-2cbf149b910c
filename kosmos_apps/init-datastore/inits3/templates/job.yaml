---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "inits3.jobName" . }}
  labels:
    {{- include "inits3.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation
  namespace: {{ .Release.Namespace }}
spec:
  parallelism: 1
  completions: 1
  template:
    metadata:
      name: {{ .Chart.Name }}
      labels:
        {{- include "inits3.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "inits3.jobName" . }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command:
            - "bash"
            - "-c"
            - |
              s3_alias_name='init-alias'
              for i in {1..100}; do
                /usr/bin/mc -C /tmp/.mc config host add ${s3_alias_name} http://minio.{{ .Release.Namespace }}.svc.cluster.local ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD} && break
                sleep 2
              done
              user_exists=$(/usr/bin/mc -C /tmp/.mc admin user info ${s3_alias_name} ${APP_BUCKET_USER} 1>/dev/null 2>&1 && echo true || echo false)
              if [ "${user_exists}" = "false" ]; then
                /usr/bin/mc -C /tmp/.mc admin user add ${s3_alias_name} ${APP_BUCKET_USER} ${APP_BUCKET_PASSWORD}
              else
                echo "Application user already exists, skipping..."
              fi
              {{- if .Values.createAdminAccount }}
                /usr/bin/mc -C /tmp/.mc admin policy attach ${s3_alias_name} consoleAdmin --user ${APP_BUCKET_USER}
              {{- end }}
              for bucket in $APP_BUCKET_NAME ; do
                bucket_exists=$(/usr/bin/mc -C /tmp/.mc stat ${s3_alias_name}/${bucket} 1>/dev/null 2>&1 && echo true || echo false)
                if [ "${bucket_exists}" = "false" ]; then
                  /usr/bin/mc -C /tmp/.mc mb ${s3_alias_name}/${bucket}
                  policy_name="${APP_BUCKET_USER}_allow_${bucket}"
                  cat << EOF > /tmp/.mc/${policy_name}.json
                  {
                    "Version": "2012-10-17",
                    "Statement": [
                      {
                        "Sid": "AllowThisBucketOnly",
                        "Effect": "Allow",
                        "Action": "s3:*",
                        "Resource": [
                          "arn:aws:s3:::${bucket}/*",
                          "arn:aws:s3:::${bucket}"
                        ]
                      }
                    ]
                  }
              EOF
                  /usr/bin/mc -C /tmp/.mc admin policy create ${s3_alias_name} ${policy_name} /tmp/.mc/${policy_name}.json
                  /usr/bin/mc -C /tmp/.mc admin policy attach ${s3_alias_name} ${policy_name} --user ${APP_BUCKET_USER}
                else
                  echo "Bucket ${bucket} already exists, skipping..."
                fi
              done
          env:
            - name: APP_BUCKET_NAME
              value: {{ required "ERROR: appBucketName parameter must be set !" .Values.appBucketName }}
            - name: APP_BUCKET_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "inits3.secretName" . }}
                  key: app_bucket_user
            - name: APP_BUCKET_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "inits3.secretName" . }}
                  key: app_bucket_password
            - name: MINIO_ROOT_USER
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.s3SecretName }}
                  key: accessKey
            - name: MINIO_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.s3SecretName }}
                  key: secretKey
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
          - name: mc-config
            mountPath: /tmp/.mc
      volumes:
      - name: mc-config
        emptyDir:
          medium: "Memory"
