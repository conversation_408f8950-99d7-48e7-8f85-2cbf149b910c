image:
  repository: quay.io/minio/mc
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

podSecurityContext: {}

securityContext:
  capabilities:
    drop:
    - ALL
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  seccompProfile:
    type: "RuntimeDefault"
  runAsUser: 1000

resources:
  requests:
    cpu: 50m
    memory: 32Mi
  limits:
    cpu: 1
    memory: 256Mi

# S3 secret name that contains root credentials
s3SecretName: "minio-secrets"

# Application s3 username prefix, included in credentials generation.
# adapt it to easily identify your user in postgres in case of debug.
# will be in the form of '{app_prefix}_{rand_generated}'.
# Not used if 'appDbUser' not empty.
appBucketUserPrefix: "app"

# Application postgres username, if empty, user will be generated based on 'appUserPrefix' + rand alphanum characters.
# NOT RECOMMENDED, use appDbUserPrefix instead.
appBucketUser: ""

# Wanted application database name to create. Can be multiple with same user, separated by white space.
appBucketName: ""

# Will create a service account attached with admin policy 'minioConsole'
# if set to true, buckets creation is skipped
createAdminAccount: false
