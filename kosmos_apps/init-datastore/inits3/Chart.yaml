apiVersion: v2
name: inits3
type: application
description: Initialize S3 service account and bucket

version: 1.1.4
appVersion: "RELEASE.2025-03-12T17-29-24Z"

maintainers:
  - name: Athea
    email: <EMAIL>

home: https://athea.tech/

sources:
  - https://gitlab.corp.athea/athea/kosmos/apps/init-datastore/-/tree/develop/inits3

keywords:
  - inits3
  - init-datastore
  - s3
  - minio

annotations:
  images: |
    - name: mc
      image: quay.io/minio/mc:RELEASE.2025-03-12T17-29-24Z
