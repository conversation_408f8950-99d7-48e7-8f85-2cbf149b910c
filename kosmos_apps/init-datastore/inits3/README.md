# InitS3 Chart

This chart is designed to be consume by the applications that needs S3 buckets.
Its purpose is to create a service account and buckets, plus the credentials associated to it for the applications.

It will also create and attach policies to allow read/write operations on the created bucket(s).

## Values descriptions

Few parameters are required:

- **s3SecretName:** s3 secret name where the cluster root credentials are stored, it is needed by the job in order to create user and bucket(s).  
  **It only works with official Minio Operator** since this key generated by it must be present:
  - config.env

- **appBucketUserPrefix:** Prefix application username, generation will be done following that pattern:  
  {app_prefix}_{rand_AlphaNum }
  Usefull in order to easily identify whose user belong to which application.  
  Note: overriden by 'appBucketUser' parameter if not empty

- **appBucketUser:** Application service account to create that will be allowed to connect to the application bucket(s).  
  **It is not recommended to use it**, as user should only be generated for security purpose.  
  Note: override 'appBucketUserPrefix' parameter

- **appBucketName:** Application bucket name to create. It can be more than one, names must be separated with white space.  
  E.g: bucket1 bucket2

- **createAdminAccount:** Set to true, the account created will have the policy *'minioConsole'* attached to it.  
  Note: this will skip the buckets creation and will only create the account with admin rights.

## How to integrate it with helmfile

Suppose we have one application that connect to S3 metier:

- Application name: pkafe
- Application needed buckets: pkafe_api, pkafe_backend

Here the code to include in helmfile:

```yaml
- name: pkafe-inits3
  namespace: tenants-s3
  needs:
    - tenants-s3/s3-cluster
  chart: athea/inits3
  version: x.x.x
  set:
    - name: appBucketName
      value: pkafe_api pkafe_backend
    - name: appBucketUserPrefix
      value: pkafe
```

Do not forget to add the 'needs' block for your application that now depend on inits3 chart:

```yaml
needs:
  - tenants-s3/pkafe-inits3
```

**NOTE:** If your application already exist, you should remove it with its buckets before installing using inits3 chart.

### Application values template code

Now your application can automatically get the credentials during the deployment from the secrets newly created using k8s+ref concept:

Suppose your application reference the credentials in a secret config:

```yaml
---
apiVersion: v1
kind: Secret
metadata:
  name: pkafe-secret
type: Opaque
data:
  PKAFE_BUCKET_USER: ref+k8s://v1/Secret/tenants-s3/pkafe-inits3-secret/app_bucket_user
  PKAFE_BUCKET_PASSWORD: ref+k8s://v1/Secret/tenants-s3/pkafe-inits3-secret/app_bucket_password
```

This config will tell helmfile to retrieve the secret named 'pkafe-inits3-secret' in s3 metier namespace, and replace the values on the fly during the installation process, without leaving any secrets on the operator machine.

## Naming convention

Release name is important as it will define your job, pod and secret name, and thus, you should always name it following that pattern:

- {APP_NAME}-inits3

E.g: app name = vstore, release name should be: vstore-inits3

'*appDbUserPrefix*' is also important as it will be easier for the admin team to identify whose applications users are register in s3 backend, so you should also name it using your application name.

## Secret life cycle

Secret containing your application credentials are stored on the S3 cluster namespace.  
Secret name: {.Release.name}-secret

If the secret does not exist, the chart with generate:

- service account based on your 'appBucketUserPrefix' setting (or will use 'appBucketUser' value if defined)
- the password

If the secret already exists, reinstalling inits3 wont modify it.

**WARNING:** Secret is kept even if you uninstall the chart.

## S3 actions

Kubernetes job will tests few things:

If service account already exists, it wont fail nor recreate it.  
If the buckets already exist, it wont fail nor recreate it.

But if buckets already exist, it wont create policies to allow the user to use it.

### Things to improve chart

- create policy and attach it on a new service account if buckets already exist
- add option to delete secret on uninstall if wanted
- add post uninstall job to also delete service account and buckets if wanted
- remove username option (user should be generated instead of defined)
- add bucket name in secret so the application can directly fetch it instead of having in app values
