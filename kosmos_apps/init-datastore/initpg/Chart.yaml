apiVersion: v2
name: initpg
type: application
description: Initialize PostgreSQL user and database

version: 1.0.4
appVersion: "16.6-4"

maintainers:
  - name: Athea
    email: <EMAIL>

home: https://athea.tech/

sources:
  - https://gitlab.corp.athea/athea/kosmos/apps/init-datastore/-/tree/develop/initpg

keywords:
  - initpg
  - init-datastore
  - postgresql
  - sql

annotations:
  images: |
    - name: postgresql
      image: ghcr.io/cloudnative-pg/postgresql:16.6-4
