{{- $appDbUser := default ((printf "%s%s%s" .Values.appDbUserPrefix "_" (randAlpha 8)) | replace "-" "_") .Values.appDbUser | lower | b64enc }}
{{- $appDbPassword := (randAlphaNum 12) | b64enc }}
{{- $existingSecret := (lookup "v1" "Secret" .Release.Namespace (include "initpg.secretName" .)) }}
{{- if $existingSecret }}
  {{- $appDbUser = index $existingSecret.data "app_db_user" }}
  {{- $appDbPassword = index $existingSecret.data "app_db_password" }}
{{- end -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "initpg.secretName" . }}
  labels:
    {{- include "initpg.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-5"
    "helm.sh/resource-policy": "keep"
data:
  app_db_user: {{ $appDbUser }}
  app_db_password: {{ $appDbPassword }}
type: Opaque
