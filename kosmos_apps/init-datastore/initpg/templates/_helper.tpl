{{/*
Create the job name: {{ .Release.Name }}-job
*/}}
{{- define "initpg.jobName" -}}
{{- printf "%s" .Release.Name | trunc 30  -}}-job
{{- end }}

{{/*
Create the secret name: {{ .Release.Name }}-secret
*/}}
{{- define "initpg.secretName" -}}
{{- printf "%s" .Release.Name | trunc 30 -}}-secret
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "initpg.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Expand the name of the chart.
*/}}
{{- define "initpg.name" -}}
{{- default .Chart.Name | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "initpg.labels" -}}
helm.sh/chart: {{ include "initpg.chart" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/name: {{ include "initpg.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
