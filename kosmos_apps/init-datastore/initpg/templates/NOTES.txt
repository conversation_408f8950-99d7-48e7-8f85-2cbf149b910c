{{ .Release.Name }} installed !

Release name: {{ .Release.Name }}

Release info:

  $ helm -n {{ .Release.Namespace }} status {{ .Release.Name }}
  $ helm -n {{ .Release.Namespace }} get all {{ .Release.Name }}

To get the job status:

  $ kubectl -n {{ .Release.Namespace }} get jobs -l app.kubernetes.io/instance={{ .Release.Name }}

To get the pod job status:

  $ kubectl -n {{ .Release.Namespace }} get pods -l app.kubernetes.io/instance={{ .Release.Name }}

If you want to get the logs:

  $ kubectl -n {{ .Release.Namespace }} logs -l app.kubernetes.io/instance={{ .Release.Name }}

To get your application credentials:

  $ echo "app pg username: $(kubectl -n {{ .Release.Namespace }} get secrets {{ .Release.Name }}-secret -o jsonpath={.data.app_db_user} | base64 -d)"
  $ echo "app pg password: $(kubectl -n {{ .Release.Namespace }} get secrets {{ .Release.Name }}-secret -o jsonpath={.data.app_db_password} | base64 -d)"
