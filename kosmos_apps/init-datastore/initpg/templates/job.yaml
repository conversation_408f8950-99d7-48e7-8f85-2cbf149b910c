---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "initpg.jobName" . }}
  labels:
    {{- include "initpg.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation
  namespace: {{ .Release.Namespace }}
spec:
  parallelism: 1
  completions: 1
  template:
    metadata:
      name: {{ .Chart.Name }}
      labels:
        {{- include "initpg.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "initpg.jobName" . }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command:
          - "bash"
          - "-c"
          - |
            PGHOST="${PGHOST}.{{ .Release.Namespace }}.svc.cluster.local."
            USER_EXISTS=$(psql -tAc "SELECT 1 FROM pg_user WHERE usename = '${APP_DB_USER}'") || { echo 'ERROR: please check logs' ; exit 1; }
            if [[ "$USER_EXISTS" == "1" ]]; then
              echo "Application service account already exists, skipping..."
            else
              echo "Creating application database service account..."
              psql -c "CREATE USER ${APP_DB_USER} WITH PASSWORD '${APP_DB_PASSWORD}'"
            fi
            for db in $APP_DB_NAME ; do
              DB_EXISTS=$(psql -tAc "SELECT 1 FROM pg_database WHERE datname = '${db}'")
              if [[ "$DB_EXISTS" == "1" ]]; then
                APP_DB_OWNER=$(psql -tAc "SELECT pg_catalog.pg_get_userbyid(d.datdba) FROM pg_catalog.pg_database d WHERE d.datname = '$db';")
                if [[ "$APP_DB_OWNER" == "$APP_DB_USER" ]]; then
                  echo "Database '$db' already exists and is owned by the application service account. skipping..."
                else
                  echo "Error: Database '$db' exists but is assigned to '$APP_DB_OWNER', expected '$APP_DB_USER'."
                  db_error=true
                fi
              else
                echo "Creating application database '$db'..."
                psql -c "CREATE DATABASE ${db} OWNER ${APP_DB_USER}"
              fi
            done
            if [[ -v db_error ]]; then
              echo 'ERROR: something went wrong, please check logs above.'
              exit 1
            fi
          env:
            - name: APP_DB_NAME
              value: {{ required "ERROR: appDbName parameter must be set !" .Values.appDbName }}
            - name: APP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "initpg.secretName" . }}
                  key: app_db_user
            - name: APP_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "initpg.secretName" . }}
                  key: app_db_password
            - name: PGHOST
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.pgSecretName }}
                  key: host
            - name: PGPORT
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.pgSecretName }}
                  key: port
            - name: PGUSER
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.pgSecretName }}
                  key: user
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.pgSecretName }}
                  key: password
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
