image:
  repository: ghcr.io/cloudnative-pg/postgresql
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

podSecurityContext: {}

securityContext:
  capabilities:
    drop:
    - ALL
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  seccompProfile:
    type: "RuntimeDefault"
  runAsUser: 1000

resources:
  requests:
    cpu: 50m
    memory: 32Mi
  limits:
    cpu: 1
    memory: 128Mi

# PostgreSQL secret name that contains root credentials
pgSecretName: "postgresql-cluster-superuser"

# Application postgres username prefix, included in credentials generation.
# adapt it to easily identify your user in postgres in case of debug.
# will be in the form of '{app_prefix}_{rand_generated}'.
# NOTE: '-' are replaced by '_' for pg requierements.
# Not used if 'appDbUser' not empty.
appDbUserPrefix: "app"

# Application postgres username, if empty, user will be generated based on 'appUserPrefix' + rand alphanum characters.
# NOT RECOMMENDED, use appDbUserPrefix instead.
appDbUser: ""

# Wanted application database name to create. Can be multiple with same user, separated by white space.
# Database name should only contains lowercase alphanumeric + dot (.) + underscore (_)
appDbName: ""
