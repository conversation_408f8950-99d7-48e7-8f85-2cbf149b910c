# InitPG Chart

This chart is designed to be consume by the applications that needs PostgreSQL database backend.
Its purpose is to create a service account and database(s) with the proper permission, plus the credentials associated to it for the applications.

## Values descriptions

Few parameters are required:

- **pgSecretName:** PostgreSQL secret name where the instance root credentials are stored, it is needed by the job in order to create user and database(s).  
  **It only works with CloudNativePG cluster** since those keys generated by it must be present:
  - host
  - port
  - user
  - password

- **appDbUserPrefix:** Prefix application username, generation will be done following that pattern:  
  {app_prefix}_{rand_AlphaNum }
  Usefull in order to easily identify whose user belong to which application.  
  Note: overriden by 'appDbUser' parameter if not empty

- **appDbUser:** Application service account to create that will be allowed to connect to the application database(s).  
  **It is not recommended to use it**, as user should only be generated for security purpose.  
  Note: override 'appDbUserPrefix' parameter

- **appDbName:** Application database name to create. It can be more than one, names must be separated with white space.  
  E.g: app1 app2

## Direct loacl helm usage

```/bin/sh
helm install initpg -n kosmos-sql --set pgSecretName=pgcluster-superuser --set appDbName=coder --set appDbUser=coder initpg/
```

## How to integrate it with helmfile

### Helmfile code

Suppose we have one application that connect to postgres technique:

- Application name: pkafe
- Application needed databases: pkafe_api, pkafe_backend

Here the code to include in helmfile:

```yaml
- name: pkafe-initpg
  namespace: kosmos-sql
  needs:
    - kosmos-sql/postgresql-technique
  chart: athea/initpg
  version: x.x.x
  set:
    - name: pgSecretName
      value: postgresql-technique-cluster-superuser
    - name: appDbName
      value: pkafe_api pkafe_backend
    - name: appDbUserPrefix
      value: pkafe
```

Do not forget to add the 'needs' block for your application that now depend on initpg chart:

```yaml
...
needs:
  - kosmos-sql/pkafe-initpg
...
```

**NOTE:** If your application already exist, you should remove it with its databases before installing using initpg chart.

### Application values template code

Now your application can automatically get the credentials during the deployment from the secrets newly created using k8s+ref concept:

Suppose your application reference the credentials in a secret config:

```yaml
---
apiVersion: v1
kind: Secret
metadata:
  name: pkafe-secret
type: Opaque
data:
  PKAFE_DB_USER: ref+k8s://v1/Secret/kosmos-sql/pkafe-initpg-secret/app_db_user
  PKAFE_DB_PASSWORD: ref+k8s://v1/Secret/kosmos-sql/pkafe-initpg-secret/app_db_password
```

This config will tell helmfile to retrieve the secret named 'pkafe-initpg-secret' in postgres technique namespace, and replace the values on the fly during the installation process, without leaving any secrets on the operator machine.

## Naming convention

Release name is important as it will define your job, pod and secret name, and thus, you should always name it following that pattern:

- {APP_NAME}-initpg

E.g: app name = vstore, release name should be: vstore-initpg

'*appDbUserPrefix*' is also important as it will be easier for the admin team to identify whose applications users are register in PG backend, so you should also name it using your application name.

## Secret life cycle

Secret containing your application credentials are stored on the postgreSQL cluster namespace.  
Secret name: {.Release.name}-secret

If the secret does not exist, the chart with generate:

- service account based on your 'appDbUserPrefix' setting (or will use 'appDbUser' value if defined)
- the password

If the secret already exists, reinstalling initpg wont modify it.

**WARNING:** Secret is kept even if you uninstall the chart.

## PostgreSQL actions

Kubernetes job will tests few things:

If service account already exists, it wont fail or recreate it.  
If databases already exist, it wont fail or recreate it.
Job will also test if service account has proper rights on the databases -> will fail if the databases belong to another user.

### Things to improve chart

- add option to delete secret on uninstall if wanted
- add post uninstall job to also delete user and databases if wanted
- remove username option (user should be generated instead of defined)
- add database name in secret so application can directly fetch it instead of having in app values
