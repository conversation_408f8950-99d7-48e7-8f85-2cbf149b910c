sonar.projectKey=${env.CI_PROJECT_ID}-${env.CI_PROJECT_NAME}-${env.CI_COMMIT_REF_SLUG}
sonar.projectName=${env.CI_PROJECT_PATH} | ${env.CI_COMMIT_REF_NAME}
sonar.projectVersion=${env.CI_COMMIT_SHORT_SHA}
sonar.projectDescription=Init Datastore Helm charts - ${env.CI_PROJECT_URL}
sonar.projectCreation.mainBranchName=${env.CI_COMMIT_REF_NAME}
sonar.links.smc=${env.CI_PROJECT_URL}
sonar.scm.provider=git
sonar.scanner.connectTimeout=10
