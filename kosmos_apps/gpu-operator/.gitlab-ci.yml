stages:
  - test
  - helm build
  - helmfile update
  - deploy kosmos-dev
  - deploy kosmos-int

include:
  - project: "athea/devops/athea-toolbox"
    ref: "main"
    file: "/templates/test/sonar.gitlab-ci.yml"
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helm/helm.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helmfile/helmfile.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/deployment/deployment.gitlab-ci.yml'

variables:
  VALUES_TARGET_DIR_NAME: gpu-operator
  HELMFILE_LABEL: app=gpu-operator


### ANCHORS
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG =~ /^gpu-operator-\d+\.\d+\.\d+(-\d+)?$/
      variables:
        HELM_DIR_PATH: "gpu-operator"


### SONAR
sonar:
  extends: .sonar
  before_script:
    - helm lint gpu-operator/
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - gpu-operator/**/*


# HELM
helm-build:
  extends: .build_helm
  rules:
    - if: $CI_COMMIT_TAG =~ /^gpu-operator-\d+\.\d+\.\d+$/
      variables:
        HELM_DIR_PATH: gpu-operator/


### HELMFILE
helmfile-update:
  extends: .helmfile_update
  rules:
    - if: $CI_COMMIT_TAG =~ /^gpu-operator-\d+\.\d+\.\d+$/
      variables:
        HELM_DIR_PATH: gpu-operator/
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - values/**/*


### DEPLOY
kosmos-dev-deployment:
  extends: .kosmos_dev_deployment
  rules:
    - if: $CI_COMMIT_TAG =~ /^gpu-operator-\d+\.\d+\.\d+$/
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - values/**/*
      when: manual

kosmos-int-deployment:
  extends: .kosmos_int_deployment
  needs:
    - job: helmfile-update
      optional: true
  rules:
    - if: $CI_COMMIT_TAG =~ /^gpu-operator-\d+\.\d+\.\d+$/
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - values/**/*
