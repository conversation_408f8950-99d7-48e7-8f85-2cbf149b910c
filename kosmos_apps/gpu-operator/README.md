# Deploy operator

## Prerequsistes

Ubuntu 22.04 LTS as toolkit and driver will be deployed in container.

## Deploy (kosmos-system ns must be privileged)
```/bin/sh
helm upgrade --install --wait gpu-operator --create-namespace -f values.yaml -n kosmos-system gpu-operator/
```

## Monitoring
```/bin/sh
kubectl apply --server-side -n kosmos-monitoring -f dcgm-exporter-dashboard.yaml
```

## Test
```/bin/sh
kubectl apply -f test.yaml -n kosmos-system-restricted
```

# Uninstall

## Operator
```/bin/sh
helm uninstall gpu-operator -n kosmos-system
```
