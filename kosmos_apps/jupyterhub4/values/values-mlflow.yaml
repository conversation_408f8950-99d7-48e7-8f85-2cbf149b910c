ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - jupyterhub.dh.artemis-ia.fr
  tls:
    - hosts:
        - jupyterhub.dh.artemis-ia.fr
      secretName: jupyterhub-tls-secret
hub:
  cookieSecret: 2488684553752f70fc043c9182428cc3aeaaa11d25ffa222d27b1b770d27876f
  extraEnv:
    OAUTH_TLS_VERIFY: "false"
    OAUTH2_TLS_VERIFY: "false"
  extraConfig:
    pass_oauth_access_token: |

      import json
      import base64

      def extract_sub_from_jwt(token: str) -> str:
          """
          Extract the 'sub' claim from a JWT token.
          """
          token = token.strip().strip('"').strip("'")
          parts = token.split('.')
          payload_b64 = parts[1]

          payload_b64 = payload_b64.strip().replace("-", "+").replace("_", "/")

          pad_len = (4 - len(payload_b64) % 4) % 4
          payload_b64 += '=' * pad_len
          payload_bytes = base64.b64decode(payload_b64)
          payload = json.loads(payload_bytes.decode("utf-8"))
          return payload.get('sub', '')
      async def add_access_token_to_env(spawner):
          """
          Copy the user's Keycloak access_token from auth_state
          into the single-user pod's env var OAUTH2_ACCESS_TOKEN.
          """
          auth_state = await spawner.user.get_auth_state() or {}
          print(f"Got auth_state: {auth_state}")
          token = auth_state.get('access_token')
          if not token:
              return
          print(f"Adding OAUTH2_ACCESS_TOKEN to single-user environment {token}")
          spawner.environment['OAUTH2_ACCESS_TOKEN'] = token
          spawner.environment['OAUTH2_SUB'] = extract_sub_from_jwt(token)

      # Register the hook for every single-user spawner
      c.Spawner.pre_spawn_hook = add_access_token_to_env
  config:
    # VERY USEFUL DOCUMENTATION
    # https://oauthenticator.readthedocs.io/en/latest/reference/api/gen/oauthenticator.generic.html
    # NOTE: "Add to ID token" and "Add to userinfo" must be enabled in keycloak (Clients > "jupyterhub" -> Client details > Dedicated scopes > (jupyterhub-dedicated) > Mapper details (client roles))
    GenericOAuthenticator:
      client_id: jupyterhub
      client_secret: sfXIvtnT5tDQYQSlFQaVWv1cVgVOO3xF
      oauth_callback_url: https://jupyterhub.dh.artemis-ia.fr/hub/oauth_callback
      authorize_url: https://iam.dh.artemis-ia.fr/realms/kosmos/protocol/openid-connect/auth
      token_url: https://iam.dh.artemis-ia.fr/realms/kosmos/protocol/openid-connect/token
      userdata_url: https://iam.dh.artemis-ia.fr/realms/kosmos/protocol/openid-connect/userinfo
      tls_verify: false
      enable_auth_state: true
      redirectToServer: true
      #allow_all: true
      scope: ["openid", "email"]
      username_claim: "preferred_username"
      # login_service: keycloak # userfriendly button name
      auth_state_groups_key: "oauth_user.resource_access.jupyterhub.roles" # must match roles in json
      claim_groups_key: "groups"
      manage_groups: true
      validate_server_cert: false
      userdata_params:
        state: state

      allowed_groups: # default users must match keycloak roles
        - datascientist
      admin_groups: # admin users must match keycloak roles
        - adminsysteme
      #http_request_kwargs:
      #  ca_certs: "/etc/ssl/certs/tls.crt" # keycloak CA

    JupyterHub:
      admin_access: true
      authenticator_class: generic-oauth
      cookie_secure: true
      cookie_options:
        SameSite: None

    KubeSpawner:
      container_security_context:
        privileged: false
        runAsUser: 1000
        runAsGroup: 1000
        allowPrivilegeEscalation: false
        capabilities:
          drop:
          - ALL
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
singleuser:
  cloudMetadata:
    # block set to true will append a privileged initContainer using the
    # iptables to block the sensitive metadata server at the provided ip.
    blockWithIptables: false
  # FIXME change with CertManager+helmfile valref when available..
  #extraVolumeMounts:
  #- name: keycloak-ca
  #  mountPath: /etc/ssl/certs/
  #  readOnly: false
  #extraVolumes:
  #- name: keycloak-ca
  #  secret:
  #    secretName: keycloak-ca
