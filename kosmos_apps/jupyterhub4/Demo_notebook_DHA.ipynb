{"cells": [{"cell_type": "markdown", "id": "bf8e8045-8f3e-4355-b5c5-5968b555b654", "metadata": {}, "source": ["# Basic Notebook use in DHA"]}, {"cell_type": "markdown", "id": "b78c052f-9c1c-4d8c-898b-84ff89de35de", "metadata": {}, "source": ["## Aim\n", "This notebook is a quick example to show the full process of data ingestion on the DAH. It will cover :\n", " 1. Reading a csv file from a S3 bucket.\n", " 2. Writing as a table into a pg database.\n", " 3. Check and read from the pg database.\n", " 4. [Upload a file to s3](#4)"]}, {"cell_type": "code", "execution_count": 1, "id": "9d67d9c8-7eb8-42e9-aef4-441fcf31d3d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: boto3 in /opt/conda/lib/python3.12/site-packages (1.38.11)\n", "Requirement already satisfied: psycopg2-binary in /opt/conda/lib/python3.12/site-packages (2.9.9)\n", "Requirement already satisfied: botocore<1.39.0,>=1.38.11 in /opt/conda/lib/python3.12/site-packages (from boto3) (1.38.11)\n", "Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /opt/conda/lib/python3.12/site-packages (from boto3) (1.0.1)\n", "Requirement already satisfied: s3transfer<0.13.0,>=0.12.0 in /opt/conda/lib/python3.12/site-packages (from boto3) (0.12.0)\n", "Requirement already satisfied: python-dateutil<3.0.0,>=2.1 in /opt/conda/lib/python3.12/site-packages (from botocore<1.39.0,>=1.38.11->boto3) (2.9.0.post0)\n", "Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /opt/conda/lib/python3.12/site-packages (from botocore<1.39.0,>=1.38.11->boto3) (2.4.0)\n", "Requirement already satisfied: six>=1.5 in /opt/conda/lib/python3.12/site-packages (from python-dateutil<3.0.0,>=2.1->botocore<1.39.0,>=1.38.11->boto3) (1.17.0)\n", "Requirement already satisfied: psycopg2==2.9.9 in /opt/conda/lib/python3.12/site-packages (from psycopg2-binary) (2.9.9)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install boto3 psycopg2-binary"]}, {"cell_type": "markdown", "id": "04f5c7bd-50de-453c-be88-27bc455a35d2", "metadata": {}, "source": ["## 1. Reading a csv file from a s3 bucket"]}, {"cell_type": "markdown", "id": "ef757410-b240-42d3-b2ef-da944142ad89", "metadata": {}, "source": ["### Connection to s3"]}, {"cell_type": "code", "execution_count": 7, "id": "6c2bf5f9-9ba8-4639-81f6-2fd4f2507bfb", "metadata": {}, "outputs": [], "source": ["import boto3\n", "import pandas as pd\n", "import io\n", "from io import StringIO\n", "\n", "s3_ip = \"s3-cluster-hl.kosmos-s3\"\n", "\n", "s3 = boto3.client(\n", "         service_name='s3',\n", "         aws_access_key_id='minioadmin',\n", "         aws_secret_access_key='6aGaNmninjg8',\n", "         endpoint_url=f'http://{s3_ip}:9000',\n", ")"]}, {"cell_type": "markdown", "id": "6f55d976-66b3-4cda-bcf4-64b5b8f98876", "metadata": {}, "source": ["### Read the file"]}, {"cell_type": "code", "execution_count": 9, "id": "6ad9d971-0141-4ea0-82c2-574bcfee4292", "metadata": {}, "outputs": [], "source": ["# Read the file from the S3 bucket\n", "# Specify the bucket name and file path\n", "bucket_name = 'demo'\n", "file_path = 'data/MiddleEast_1000.csv' \n", "\n", "try:\n", "    response = s3.get_object(Bucket=bucket_name, Key=file_path)\n", "    csv_content = response['Body'].read().decode('utf-8')\n", "\n", "    # Load the CSV content into a pandas DataFrame\n", "    df = pd.read_csv(StringIO(csv_content))\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")"]}, {"cell_type": "markdown", "id": "c5e1b661-fe6a-4700-9846-55e589b05070", "metadata": {}, "source": ["## 2. Writing as a table into a pg database."]}, {"cell_type": "markdown", "id": "d1b5e979-6456-4326-9ec4-1b12782a30dc", "metadata": {}, "source": ["### Connection to pg database"]}, {"cell_type": "code", "execution_count": 11, "id": "bc92849f-dbab-4626-9a2d-25ffd6ed24b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection successful!\n"]}], "source": ["from sqlalchemy import create_engine, inspect, Column, Integer, String, text\n", "from sqlalchemy.orm import sessionmaker, declarative_base\n", "\n", "# Database connection parameters\n", "db_user = \"postgres\"  # default user at init\n", "db_password = \"bHhlZoJK01tWxk4pkjzYi795YJoTcvDlOYtlDJI40uZOdqT9QtouYBNaNgiHcty0\"  # created when init pg container\n", "db_ip = \"pgcluster-rw.kosmos-sql\"  # optained with the following command:\n", "# docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' <postgres-container>\n", "db_datname = \"postgres\"  # Default database at init\n", "db_url = f'postgresql://{db_user}:{db_password}@{db_ip}:5432/{db_datname}'\n", "\n", "# Define the SQLAlchemy engine (could active the verbose mode with echo=True)\n", "engine = create_engine(db_url, echo=False)\n", "\n", "# Test the connection\n", "try:\n", "    connection = engine.connect()\n", "    print(\"Connection successful!\")\n", "    connection.close()\n", "except Exception as e:\n", "    print(f\"Connection failed: {e}\")\n"]}, {"cell_type": "markdown", "id": "08aa9c23-5cdf-49e7-95b6-dabc47b7682c", "metadata": {}, "source": ["### Write into the database"]}, {"cell_type": "code", "execution_count": 12, "id": "0b719254-faf5-4330-87da-4ea4671c90df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame successfully dumped into the 'events' table.\n"]}], "source": ["# Define the table name\n", "table_name = 'events'\n", "\n", "# Dump the DataFrame into the PostgreSQL database\n", "try:\n", "    df.to_sql(table_name, engine, if_exists='replace', index=False)\n", "    print(f\"DataFrame successfully dumped into the '{table_name}' table.\")\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")"]}, {"cell_type": "markdown", "id": "e83ffc14-1957-45dc-a4a4-cdc70efffaf1", "metadata": {}, "source": ["## 3. Check and read from the pg database"]}, {"cell_type": "code", "execution_count": 13, "id": "2bdcaaf9-d0cb-4a49-af89-9d658d5931e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Contents of table 'events':\n", "(0, 'YEM98304', '2025-04-11', 2025, 1, 'Demonstrations', 'Protests', 'Peaceful protest', 'Protesters (Yemen)', 'Government of Yemen (2017-) Houthi', 'Protesters', None, None, None, 'Protesters only', None, 887, 'Middle East', 'Yemen', 'Ibb', 'Far Al Udayn', None, '<PERSON>', 13.9145, 43.7869, 1, 'Yemen News Agency (SABA) - Houthi', 'National', \"On 11 April 2025, protesters held a large Houthi-sponsored protest in Al Jillah (Far Al Udayn, Ibb) in solidarity with the Palestinian people, denoun ... (73 characters truncated) ... e 'Zionist enemy' in the context of the latest round of violence in Israel-Palestine. Houthi-affiliated political officials took part in the protest.\", 0, 'crowd size=large', 1744664315)\n", "(1, 'YEM98323', '2025-04-11', 2025, 1, 'Demonstrations', 'Protests', 'Peaceful protest', 'Protesters (Yemen)', 'Government of Yemen (2017-) Houthi', 'Protesters', None, None, None, 'Protesters only', None, 887, 'Middle East', 'Yemen', '<PERSON>bb', 'Far Al Udayn', None, '<PERSON><PERSON><PERSON>', 13.8868, 43.7488, 1, 'Yemen News Agency (SABA) - Houthi', 'National', \"On 11 April 2025, protesters held a large Houthi-sponsored protest in Husayd (Far Al Udayn, Ibb) in solidarity with the Palestinian people, denouncin ... (70 characters truncated) ... e 'Zionist enemy' in the context of the latest round of violence in Israel-Palestine. Houthi-affiliated political officials took part in the protest.\", 0, 'crowd size=large', 1744664315)\n"]}], "source": ["# Define the SQL query\n", "select_query = text(f\"\"\"SELECT * FROM {table_name}\n", "LIMIT 2\"\"\")\n", "\n", "# Execute the query\n", "print(f\"Contents of table '{table_name}':\")\n", "with engine.connect() as connection:\n", "    result = connection.execute(select_query)\n", "    for row in result:\n", "        print(row)"]}, {"cell_type": "code", "execution_count": 14, "id": "5f3c4929-ee13-402a-805d-5c9d16c7a4f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column names of the 'events' table:\n", "['Unnamed: 0', 'event_id_cnty', 'event_date', 'year', 'time_precision', 'disorder_type', 'event_type', 'sub_event_type', 'actor1', 'assoc_actor_1', 'inter1', 'actor2', 'assoc_actor_2', 'inter2', 'interaction', 'civilian_targeting', 'iso', 'region', 'country', 'admin1', 'admin2', 'admin3', 'location', 'latitude', 'longitude', 'geo_precision', 'source', 'source_scale', 'notes', 'fatalities', 'tags', 'timestamp']\n"]}], "source": ["# Get the column names of the table\n", "try:\n", "    # Create an inspector\n", "    inspector = inspect(engine)\n", "\n", "    # Get the column information\n", "    columns = inspector.get_columns(table_name)\n", "\n", "    # Extract and print the column names\n", "    column_names = [column['name'] for column in columns]\n", "    print(f\"Column names of the '{table_name}' table:\")\n", "    print(column_names)\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")"]}, {"cell_type": "markdown", "id": "12bfd0ba-bfdd-4b6f-ac00-5ac2812bfcd3", "metadata": {}, "source": ["## 4. Upload a file into s3"]}, {"cell_type": "code", "execution_count": 16, "id": "c93150ec-78dd-422c-b549-2f6e35e30722", "metadata": {}, "outputs": [], "source": ["! echo \"test file\" > test_file.txt"]}, {"cell_type": "code", "execution_count": 17, "id": "1ad6fe07-73e1-4389-9880-a0cf4591631d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File 'test_file.txt' uploaded successfully to 'demo/data/test_file.txt'.\n"]}], "source": ["# Specify the bucket name and file paths\n", "bucket_name = 'demo'\n", "file_path = 'test_file.txt'  # the path to file in jupyter\n", "s3_key = 'data/test_file.txt'  # the desired key (path) in the S3 bucket\n", "\n", "# Upload the file to the S3 bucket\n", "try:\n", "    s3.upload_file(file_path, bucket_name, s3_key)\n", "    print(f\"File '{file_path}' uploaded successfully to '{bucket_name}/{s3_key}'.\")\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "386ad1b8-cac5-4270-90a6-b287796a17ad", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Key</th>\n", "      <th>LastModified</th>\n", "      <th>ETag</th>\n", "      <th>Size</th>\n", "      <th>StorageClass</th>\n", "      <th>Owner.DisplayName</th>\n", "      <th>Owner.ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/MiddleEast_1000.csv</td>\n", "      <td>2025-05-09 12:12:26.807000+00:00</td>\n", "      <td>\"2b201017804ce0540a7dae17b5bdfee5\"</td>\n", "      <td>621014</td>\n", "      <td>STANDARD</td>\n", "      <td>minio</td>\n", "      <td>02d6176db174dc93cb1b899f7c6078f08654445fe8cf1b...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>data/test_file.txt</td>\n", "      <td>2025-05-09 12:29:48.450000+00:00</td>\n", "      <td>\"b05403212c66bdc8ccc597fedf6cd5fe\"</td>\n", "      <td>10</td>\n", "      <td>STANDARD</td>\n", "      <td>minio</td>\n", "      <td>02d6176db174dc93cb1b899f7c6078f08654445fe8cf1b...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Key                     LastModified  \\\n", "0  data/MiddleEast_1000.csv 2025-05-09 12:12:26.807000+00:00   \n", "1        data/test_file.txt 2025-05-09 12:29:48.450000+00:00   \n", "\n", "                                 ETag    Size StorageClass Owner.DisplayName  \\\n", "0  \"2b201017804ce0540a7dae17b5bdfee5\"  621014     STANDARD             minio   \n", "1  \"b05403212c66bdc8ccc597fedf6cd5fe\"      10     STANDARD             minio   \n", "\n", "                                            Owner.ID  \n", "0  02d6176db174dc93cb1b899f7c6078f08654445fe8cf1b...  \n", "1  02d6176db174dc93cb1b899f7c6078f08654445fe8cf1b...  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["def list_objects(bucket_name):\n", "    # list all objects in the bucket\n", "    response = s3.list_objects(Bucket=bucket_name)\n", "    return pd.json_normalize(response['Contents'])\n", "\n", "list_objects(bucket_name=bucket_name)"]}, {"cell_type": "code", "execution_count": null, "id": "2a57e2ef-05bb-4770-a24c-e123219a6f6e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}