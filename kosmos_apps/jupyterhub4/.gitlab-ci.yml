stages:
  - helm test
  - helm build
  - helmfile update
  - deploy kosmos-dev
  - deploy kosmos-int

include:
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helm/helm.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helmfile/helmfile.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/deployment/deployment.gitlab-ci.yml'

variables:
  VALUES_TARGET_DIR_NAME: jupyterhub
  HELMFILE_LABEL: app=jupyterhub

### ANCHORS
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG =~ /^jupyterhub-\d+\.\d+\.\d+(-\d+)?$/
      variables:
        HELM_DIR_PATH: jupyterhub
    - if: $CI_COMMIT_TAG =~ /^jupyterhub-secrets-\d+\.\d+\.\d+$/
      variables:
        HELM_DIR_PATH: jupyterhub-secrets


### HELM
jupyterhub:
  extends: .test_helm
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - jupyterhub/**/*
      variables:
        HELM_DIR_PATH: jupyterhub

jupyterhub-secrets:
  extends: .test_helm
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - jupyterhub-secrets/**/*
      variables:
        HELM_DIR_PATH: jupyterhub-secrets


helm-build:
  extends: .build_helm
  <<: *tag_rules


### HELMFILE
helmfile-values-sync:
  extends: .helmfile_values_sync
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - helmfile-values/**/*

helmfile-update:
  extends: .helmfile_update
  <<: *tag_rules


### DEPLOY
kosmos-dev-deployment:
  extends: .kosmos_dev_deployment
  rules:
    - if: $CI_COMMIT_TAG =~ /^jupyterhub-\d+\.\d+\.\d+(-\d+)?$/
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - helmfile-values/**/*
      when: manual

kosmos-int-deployment:
  extends: .kosmos_int_deployment
  needs:
    - job: helmfile-values-sync
      optional: true
    - job: helmfile-update
      optional: true
  rules:
    - if: $CI_COMMIT_TAG =~ /^jupyterhub-\d+\.\d+\.\d+(-\d+)?$/
    - if: $CI_COMMIT_TAG =~ /^jupyterhub-secrets-\d+\.\d+\.\d+$/
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - helmfile-values/**/*
