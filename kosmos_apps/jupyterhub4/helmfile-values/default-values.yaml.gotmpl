hub:
  cookieSecret: ref+k8s://v1/Secret/kosmos-data/jupyterhub-oidc-secret/cookieSecret
  extraEnv:
    OAUTH_TLS_VERIFY: "false"
    OAUTH2_TLS_VERIFY: "false"
    KEYCLOAK_ENCODING_KEY: wip
  extraConfig:
    pass_oauth_access_token: |
      import json
      import base64

      def extract_creds_from_jwt(token: str) -> str:
          """
          Extract the 'sub' claim from a JWT token.
          """
          token = token.strip().strip('"').strip("'")
          parts = token.split('.')
          payload_b64 = parts[1]

          payload_b64 = payload_b64.strip().replace("-", "+").replace("_", "/")

          pad_len = (4 - len(payload_b64) % 4) % 4
          payload_b64 += '=' * pad_len
          payload_bytes = base64.b64decode(payload_b64)
          payload = json.loads(payload_bytes.decode("utf-8"))
          return payload.get('preferred_username', ''), payload.get('sub', '')
      async def add_access_token_to_env(spawner):
          """
          Copy the user's Keycloak access_token from auth_state
          into the single-user pod's env var OAUTH2_ACCESS_TOKEN.
          """
          auth_state = await spawner.user.get_auth_state() or {}
          print(f"Got auth_state: {auth_state}")
          token = auth_state.get('access_token')
          if not token:
              return
          print(f"Adding OAUTH2_ACCESS_TOKEN to single-user environment {token}")
          spawner.environment['OAUTH2_ACCESS_TOKEN'] = token
          creds = extract_creds_from_jwt(token)
          spawner.environment['MLFLOW_TRACKING_USERNAME'] = creds[0]
          spawner.environment['MLFLOW_TRACKING_PASSWORD'] = creds[1]
      origin = "https://jupyterhub.{{ .StateValues.domain }}"
      c.Spawner.default_url = "/lab"
      c.Spawner.args = ['--NotebookApp.allow_origin=*']
      c.JupyterHub.tornado_settings = {
        "headers": {
        "Access-Control-Allow-Origin": '*',
          },
      }

      def generate_vstore_jwt_tokens(spawner):
          """
          Generates JWT tokens for technical vstore with HS256 and
          for business vstore with RS256, both using the Keycloak user
          which spawned the pod, and passed them to the latter as env
          variables. Also adds keycloak username as env variable.
          """
          import jwt
          import time
          import os
          from datetime import timedelta, datetime, timezone

          spawner.environment["JUPYTERHUB_USER"] = spawner.user.name

          payload = {
            'sub': spawner.user.name,
            'preferred_username': spawner.user.name,
            'tenant_id': "{{ .StateValues.tenantName }}",
            'resource_access': {
              'eds': {
                'roles': ['storage-get', 'labels-get']
              }
            }
          }
          
          try:
            token=jwt.encode(payload, os.getenv("KEYCLOAK_ENCODING_KEY"), "RS256")
            spawner.environment["VSTORE_TOKEN"] = token
          except Exception as e:
            print(e)

          payload_tech = {
            'sub': spawner.user.name,
            'preferred_username': spawner.user.name,
            'tenant_id': "{{ .StateValues.tenantName }}",
          }
          try:
            token_tech=jwt.encode(payload_tech, "kosmossecret", "HS256")
            spawner.environment["VSTORE_TECH_TOKEN"] = token_tech
          except Exception as e:
            print(e)
      
      async def custom_pre_hook(spawner):
        await add_access_token_to_env(spawner)
        generate_vstore_jwt_tokens(spawner)


      c.Spawner.pre_spawn_hook = custom_pre_hook
      c.Application.log_level = "INFO"
      c.Application.log_format = "{\"name\": \"%(name)s\", \"level\": \"%(levelname)s\", \"message\": \"%(message)s\"}"
  extraFiles:
    ca.crt:
      mountPath: /etc/ssl/certs/rootCA.crt
      stringData: ref+k8s://v1/Secret/kosmos-system-restricted/kosmos-ca-secret/ca.crt

  config:
    # VERY USEFUL DOCUMENTATION
    # https://oauthenticator.readthedocs.io/en/latest/reference/api/gen/oauthenticator.generic.html
    # NOTE: "Add to ID token" and "Add to userinfo" must be enabled in keycloak (Clients > "jupyterhub" -> Client details > Dedicated scopes > (jupyterhub-dedicated) > Mapper details (client roles))
    GenericOAuthenticator:
      client_id: jupyterhub
      client_secret: ref+k8s://v1/Secret/kosmos-data/jupyterhub-oidc-secret/clientSecret
      oauth_callback_url: https://jupyterhub.{{ .StateValues.domain }}/hub/oauth_callback
      authorize_url: https://auth.{{ .StateValues.domain }}/realms/{{ .StateValues.tenantName }}/protocol/openid-connect/auth
      token_url: https://auth.{{ .StateValues.domain }}/realms/{{ .StateValues.tenantName }}/protocol/openid-connect/token
      userdata_url: https://auth.{{ .StateValues.domain }}/realms/{{ .StateValues.tenantName }}/protocol/openid-connect/userinfo
      tls_verify: false
      enable_auth_state: true
      redirectToServer: true
      scope: ["openid", "email"]
      username_claim: "preferred_username"
      login_service: keycloak # userfriendly button name
      auth_state_groups_key: "oauth_user.resource_access.jupyterhub.roles" # must match roles in json
      claim_groups_key: "groups"
      manage_groups: true
      validate_server_cert: false
      userdata_params:
        state: state
      allowed_groups:
        - dataingenieur
        - datascientist
        - dataanalyste
        - supervisdon
      admin_groups:
        - adminmetier
      http_request_kwargs:
        ca_certs: "/etc/ssl/certs/rootCA.crt" # keycloak CA

    JupyterHub:
      admin_access: true
      authenticator_class: generic-oauth
      cookie_secure: true
      cookie_options:
        SameSite: None

    KubeSpawner:
      container_security_context:
        runAsUser: 1000
        runAsGroup: 1000
        allowPrivilegeEscalation: false
        capabilities:
          drop:
          - ALL
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
  concurrentSpawnLimit: 64
  consecutiveFailureLimit: 5
  db:
    type: sqlite-pvc
    pvc:
      accessModes:
        - ReadWriteOnce
      storage: 1Gi
  image:
    name: quay.io/jupyterhub/k8s-hub
    tag: "4.2.0"
  podSecurityContext:
    runAsNonRoot: true
    fsGroup: 1000
    seccompProfile:
      type: "RuntimeDefault"
  containerSecurityContext:
    runAsUser: 1000
    runAsGroup: 1000
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
  pdb:
    enabled: false
  networkPolicy:
    enabled: false
  livenessProbe:
    # The livenessProbe's aim to give JupyterHub sufficient time to startup but
    # be able to restart if it becomes unresponsive for ~5 min.
    enabled: true
    initialDelaySeconds: 300
    periodSeconds: 10
    failureThreshold: 30
    timeoutSeconds: 3
  readinessProbe:
    # The readinessProbe's aim is to provide a successful startup indication,
    # but following that never become unready before its livenessProbe fail and
    # restarts it if needed. To become unready following startup serves no
    # purpose as there are no other pod to fallback to in our non-HA deployment.
    enabled: true
    initialDelaySeconds: 0
    periodSeconds: 2
    failureThreshold: 1000
    timeoutSeconds: 1

rbac:
  create: true

# proxy relates to the proxy pod, the proxy-public service, and the autohttps
# pod and proxy-http service.
proxy:
    ## rollingUpdate:
    ## - WARNING:
    ##   This is required to be set explicitly blank! Without it being
    ##   explicitly blank, k8s will let eventual old values under rollingUpdate
    ##   remain and then the Deployment becomes invalid and a helm upgrade would
    ##   fail with an error like this:
    ##
    ##     UPGRADE FAILED
    ##     Error: Deployment.apps "proxy" is invalid: spec.strategy.rollingUpdate: Forbidden: may not be specified when strategy `type` is 'Recreate'
    ##     Error: UPGRADE FAILED: Deployment.apps "proxy" is invalid: spec.strategy.rollingUpdate: Forbidden: may not be specified when strategy `type` is 'Recreate'
  deploymentStrategy:
    type: "Recreate"  
    rollingUpdate:
  service:
    type: ClusterIP
  chp:
    containerSecurityContext:
      runAsNonRoot: true
      runAsUser: 65534 # nobody user
      runAsGroup: 65534 # nobody group
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"
    livenessProbe:
      enabled: true
      initialDelaySeconds: 60
      periodSeconds: 10
      failureThreshold: 30
      timeoutSeconds: 3
    readinessProbe:
      enabled: true
      initialDelaySeconds: 0
      periodSeconds: 2
      failureThreshold: 1000
      timeoutSeconds: 1
    networkPolicy:
      enabled: false
    pdb:
      enabled: false
  traefik:
    containerSecurityContext:
      runAsNonRoot: true
      runAsUser: 65534 # nobody user
      runAsGroup: 65534 # nobody group
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"
    networkPolicy:
      enabled: false
    pdb:
      enabled: false
    serviceAccount:
      create: true
  secretSync:
    containerSecurityContext:
      runAsNonRoot: true
      runAsUser: 65534 # nobody user
      runAsGroup: 65534 # nobody group
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"
  https:
    enabled: false

# singleuser relates to the configuration of KubeSpawner which runs in the hub
# pod, and its spawning of user pods such as jupyter-myusername.
singleuser:
  networkTools:
    image:
      name: quay.io/jupyterhub/k8s-network-tools
      tag: "4.2.0"
  cloudMetadata:
    # block set to true will append a privileged initContainer using the
    # iptables to block the sensitive metadata server at the provided ip.
    blockWithIptables: false
  networkPolicy:
    enabled: false
  events: true
  extraLabels:
    hub.jupyter.org/network-access-hub: "true"
  allowPrivilegeEscalation: false
  uid: 1000
  fsGid: 100
  storage:
    type: dynamic
    static:
      pvcName:
      subPath: "{username}"
    capacity: 2Gi
    homeMountPath: /home/<USER>
    dynamic:
      storageClass:
      pvcNameTemplate: jupyter-notebook-{user_server}
      volumeNameTemplate: volume-{user_server}
      storageAccessModes: [ReadWriteOnce]
  image:
    name: quay.io/jupyter/minimal-notebook
    pullPolicy: IfNotPresent
    tag: "python-3.12.10"
  startTimeout: 300
  cpu:
    limit: .5
    guarantee: .5
  memory:
    limit: 1G
    guarantee: 1G
  cmd: jupyterhub-singleuser
  profileList:
    - display_name: "Minimal"
      description: "Spawns a notebook server"
      kubespawner_override:
        image: "quay.io/jupyter/minimal-notebook:python-3.12.10"
        pullPolicy: IfNotPresent

    - display_name: "Minimal with GPU"
      description: "Spawns a notebook server with GPU"
      kubespawner_override:
        image: "quay.io/jupyter/minimal-notebook:python-3.12.10"
        pullPolicy: IfNotPresent
        {{ if .StateValues.gpuOperator.enabled }}
        extra_resource_guarantees:
          nvidia.com/gpu: 1
        extra_pod_config:
          runtimeClassName: nvidia
        extra_resource_limits:
          nvidia.com/gpu: "1"
        {{ else if (.StateValues | getOrNil "jupyterhub.gpu.selector") }}
        node_selector:
          {{ .StateValues.jupyterhub.gpu.selector | toYaml }}
        {{ end }}

    - display_name: "GPU PyTorch"
      description: "Spawns a notebook server with access to a GPU"
      kubespawner_override:
        image: "quay.io/jupyter/pytorch-notebook:cuda12-python-3.12.10"
        pullPolicy: IfNotPresent
        {{ if .StateValues.gpuOperator.enabled }}
        extra_resource_guarantees:
          nvidia.com/gpu: 1
        extra_pod_config:
          runtimeClassName: nvidia
        extra_resource_limits:
          nvidia.com/gpu: "1"
        {{ else if (.StateValues | getOrNil "jupyterhub.gpu.selector") }}
        node_selector:
          {{ .StateValues.jupyterhub.gpu.selector | toYaml }}
        {{ end }}

    - display_name: "SciPy"
      description: "Spawns a notebook with libraries for computational science and machine learning"
      kubespawner_override:
        image: "quay.io/jupyter/scipy-notebook:python-3.12.10"
        pullPolicy: IfNotPresent

    - display_name: "SciPy with GPU"
      description: "Spawns a notebook with libraries for computational science and machine learning with access to a GPU"
      kubespawner_override:
        image: "quay.io/jupyter/scipy-notebook:python-3.12.10"
        pullPolicy: IfNotPresent
                {{ if .StateValues.gpuOperator.enabled }}
        extra_resource_guarantees:
          nvidia.com/gpu: 1
        extra_pod_config:
          runtimeClassName: nvidia
        extra_resource_limits:
          nvidia.com/gpu: "1"
        {{ else if (.StateValues | getOrNil "jupyterhub.gpu.selector") }}
        node_selector:
          {{ .StateValues.jupyterhub.gpu.selector | toYaml }}
        {{ end }}

    - display_name: "R language"
      description: "Spawns a notebook supporting R"
      kubespawner_override:
        image: "quay.io/jupyter/r-notebook:python-3.12.10"
        pullPolicy: IfNotPresent

    - display_name: "R language with GPU"
      description: "Spawns a notebook supporting R with access to a GPU"
      kubespawner_override:
        image: "quay.io/jupyter/r-notebook:python-3.12.10"
        pullPolicy: IfNotPresent
        {{ if .StateValues.gpuOperator.enabled }}
        extra_resource_guarantees:
          nvidia.com/gpu: 1
        extra_pod_config:
          runtimeClassName: nvidia
        extra_resource_limits:
          nvidia.com/gpu: "1"
        {{ else if (.StateValues | getOrNil "jupyterhub.gpu.selector") }}
        node_selector:
          {{ .StateValues.jupyterhub.gpu.selector | toYaml }}
        {{ end }}

    - display_name: "Apache Spark for Python and R"
      description: "Spawns a notebook supporting Spark for Python and R"
      kubespawner_override:
        image: "quay.io/jupyter/r-notebook:python-3.12.10"
        pullPolicy: IfNotPresent

    - display_name: "Apache Spark for Python and R with GPU"
      description: "Spawns a notebook supporting Spark for Python and R with access to a GPU"
      kubespawner_override:
        image: "quay.io/jupyter/r-notebook:python-3.12.10"
        pullPolicy: IfNotPresent   
        {{ if .StateValues.gpuOperator.enabled }}
        extra_resource_guarantees:
          nvidia.com/gpu: 1
        extra_pod_config:
          runtimeClassName: nvidia
        extra_resource_limits:
          nvidia.com/gpu: "1"
        {{ else if (.StateValues | getOrNil "jupyterhub.gpu.selector") }}
        node_selector:
          {{ .StateValues.jupyterhub.gpu.selector | toYaml }}
        {{ end }}     

    - display_name: "MLOps"
      description: "MLOps image (Pytorch + Keds + Transformers + Mlflow )"
      kubespawner_override:
        image: "hosted-registry.corp.athea/mlops-notebook:3.12.10-3-athea"
        cpu_guarantee: 2
        cpu_limit: 8
        mem_guarantee: 1G
        mem_limit: 16G
        storage_capacity: 10Gi
  extraEnv:
    CURL_CA_BUNDLE: ""
    PIP_INDEX_URL: "https://gitea.{{ .StateValues.domain }}/api/packages/athea/pypi/simple/"
    PIP_TRUSTED_HOST: gitea.{{ .StateValues.domain }}
    SAP_API_KEY: ref+k8s://v1/Secret/kosmos-system-restricted/sap-keys-secret/tec-sap-jupyterhub
    apiUrl: http://sap-back-kosmos-api-server.kosmos-system-restricted.svc.cluster.local:3080
    edsApiUrl: http://eds-back-kosmos-eds-api-server.kosmos-system-restricted.svc.cluster.local:3080
    SAP_ns: kosmos-system-restricted
    MLFLOW_TRACKING_URI: http://mlflow.kosmos-data:5000

# scheduling relates to the user-scheduler pods and user-placeholder pods.
scheduling:
  userScheduler:
    enabled: false
    containerSecurityContext:
      runAsNonRoot: true
      runAsUser: 65534 # nobody user
      runAsGroup: 65534 # nobody group
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"

  userPlaceholder:
    enabled: true
    image:
      name: registry.k8s.io/pause
      # tag is automatically bumped to new patch versions by the
      # watch-dependencies.yaml workflow.
      #
      # If you update this, also update prePuller.pause.image.tag
      #
      tag: "3.10"
    containerSecurityContext:
      runAsNonRoot: true
      runAsUser: 65534 # nobody user
      runAsGroup: 65534 # nobody group
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"
  corePods:
    tolerations:
      - key: hub.jupyter.org/dedicated
        operator: Equal
        value: core
        effect: NoSchedule
      - key: hub.jupyter.org_dedicated
        operator: Equal
        value: core
        effect: NoSchedule
    nodeAffinity:
      matchNodePurpose: prefer
  userPods:
    tolerations:
      - key: hub.jupyter.org/dedicated
        operator: Equal
        value: user
        effect: NoSchedule
      - key: hub.jupyter.org_dedicated
        operator: Equal
        value: user
        effect: NoSchedule
    nodeAffinity:
      matchNodePurpose: prefer

# prePuller relates to the hook|continuous-image-puller DaemonsSets
prePuller:
  containerSecurityContext:
    runAsNonRoot: true
    runAsUser: 65534 # nobody user
    runAsGroup: 65534 # nobody group
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  # hook relates to the hook-image-awaiter Job and hook-image-puller DaemonSet
  hook:
    enabled: false
    containerSecurityContext:
      runAsNonRoot: true
      runAsUser: 65534 # nobody user
      runAsGroup: 65534 # nobody group
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"
  continuous:
    enabled: false
  pullProfileListImages: true
  pause:
    containerSecurityContext:
      runAsNonRoot: true
      runAsUser: 65534 # nobody user
      runAsGroup: 65534 # nobody group
      allowPrivilegeEscalation: false
      capabilities:
        drop: ["ALL"]
      seccompProfile:
        type: "RuntimeDefault"
    image:
      name: registry.k8s.io/pause
      # tag is automatically bumped to new patch versions by the
      # watch-dependencies.yaml workflow.
      #
      # If you update this, also update scheduling.userPlaceholder.image.tag
      #
      tag: "3.10"

ingress:
  enabled: true
  annotations:
    {{- with .StateValues.ingressTlsConfig.annotations }}
    {{- . | toYaml | nindent 4 }}
    {{- end }}
  hosts:
    - jupyterhub.{{ .StateValues.domain }}
  tls:
    - hosts:
        - jupyterhub.{{ .StateValues.domain }}
      secretName: jupyterhub-tls-secret

cull:
  enabled: true
  users: false # --cull-users
  adminUsers: true # --cull-admin-users
  removeNamedServers: false # --remove-named-servers
  timeout: 3600 # --timeout
  every: 600 # --cull-every
  concurrency: 10 # --concurrency
  maxAge: 0 # --max-age
