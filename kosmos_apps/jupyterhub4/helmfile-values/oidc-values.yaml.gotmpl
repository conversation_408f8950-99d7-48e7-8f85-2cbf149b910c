# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username
    password: ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local
    skipCertCheck: false
  import: |
    {
      "realm": "{{ .StateValues.tenantName }}",
      "roles": {
        "realm": [
          {
            "name": "adminmetier",
            "composite": true,
            "composites": {
              "client": {
                "jupyterhub":[
                  "admin"
                ],
                "portal":[
                  "jupyterhub_IHM"
                ]
              }
            },
            "clientRole": false,
            "attributes": {}
          },
          {
            "name": "datascientist",
            "composite": true,
            "composites": {
              "client": {
                "jupyterhub":[
                  "viewer"
                ],
                "portal":[
                  "jupyterhub_IHM"
                ]
              }
            },
            "clientRole": false,
            "attributes": {}
          },
          {
            "name": "supervisdon",
            "composite": true,
            "composites": {
              "client": {
                "jupyterhub":[
                  "viewer"
                ],
                "portal":[
                  "jupyterhub_IHM"
                ]
              }
            },
            "clientRole": false,
            "attributes": {}
          },
          {
            "name": "dataanalyste",
            "composite": true,
            "composites": {
              "client": {
                "jupyterhub":[
                  "viewer"
                ],
                "portal":[
                  "jupyterhub_IHM"
                ]
              }
            },
            "clientRole": false,
            "attributes": {}
          },
          {
            "name": "dataingenieur",
            "composite": true,
            "composites": {
              "client": {
                "jupyterhub":[
                  "viewer"
                ],
                "portal":[
                  "jupyterhub_IHM"
                ]
              }
            },
            "clientRole": false,
            "attributes": {}
          }
        ],
        "client": {
          "jupyterhub" : [
            {
              "name": "viewer",
              "composite": false,
              "clientRole": true,
              "attributes": {}
            },
            {
              "name": "admin",
              "description": "",
              "composite": false,
              "clientRole": true,
              "attributes": {}
            }
          ],
          "portal": [
            {
              "name": "jupyterhub_IHM",
              "composite": false,
              "clientRole": true,
              "attributes": {}
            }
          ]
        }
      },
      "users": [],
      "clients": [
      {
        "clientId": "jupyterhub",
        "name": "jupyterhub",
        "description": "",
        "rootUrl": "",
        "adminUrl": "",
        "baseUrl": "",
        "surrogateAuthRequired": false,
        "enabled": true,
        "alwaysDisplayInConsole": false,
        "clientAuthenticatorType": "client-secret",
        "secret": "ref+k8s://v1/Secret/kosmos-data/jupyterhub-oidc-secret/clientSecret",
        "redirectUris": [
          "/*",
          "https://jupyterhub.{{ .StateValues.domain }}/hub/oauth_callback"
        ],
        "webOrigins": [
          "/*"
        ],
        "notBefore": 0,
        "bearerOnly": false,
        "consentRequired": false,
        "standardFlowEnabled": true,
        "implicitFlowEnabled": false,
        "directAccessGrantsEnabled": true,
        "serviceAccountsEnabled": false,
        "publicClient": false,
        "frontchannelLogout": true,
        "protocol": "openid-connect",
        "attributes": {
          "oidc.ciba.grant.enabled": "false",
          "client.secret.creation.time": "**********",
          "backchannel.logout.session.required": "true",
          "display.on.consent.screen": "false",
          "oauth2.device.authorization.grant.enabled": "false",
          "backchannel.logout.revoke.offline.tokens": "false"
        },
        "authenticationFlowBindingOverrides": {},
        "fullScopeAllowed": true,
        "nodeReRegistrationTimeout": -1,
        "protocolMappers": [
          {
            "name": "client roles",
            "protocol": "openid-connect",
            "protocolMapper": "oidc-usermodel-client-role-mapper",
            "consentRequired": false,
            "config": {
              "introspection.token.claim": "true",
              "multivalued": "true",
              "userinfo.token.claim": "true",
              "user.attribute": "foo",
              "id.token.claim": "true",
              "lightweight.claim": "false",
              "access.token.claim": "true",
              "claim.name": "oauth_user.resource_access.${client_id}.roles",
              "jsonType.label": "String"
            }
          },
          {
            "name": "groups",
            "protocol": "openid-connect",
            "protocolMapper": "oidc-usermodel-realm-role-mapper",
            "consentRequired": false,
            "config": {
              "introspection.token.claim": "true",
              "multivalued": "true",
              "user.attribute": "foo",
              "id.token.claim": "true",
              "access.token.claim": "true",
              "claim.name": "groups",
              "jsonType.label": "String"
            }
          }
        ],
        "defaultClientScopes": [
          "web-origins",
          "acr",
          "roles",
          "profile",
          "basic",
          "email"
        ],
        "optionalClientScopes": [
          "address",
          "phone",
          "offline_access",
          "microprofile-jwt"
        ],
        "access": {
          "view": true,
          "configure": true,
          "manage": true
        }
      }
      ]
    }
