{{- /*
    Returns given number of random Hex characters.
    - randNumeric 4 | atoi generates a random number in [0, 10^4)
      This is a range range evenly divisble by 16, but even if off by one,
      that last partial interval offsetting randomness is only 1 part in 625.
    - mod N 16 maps to the range 0-15
    - printf "%x" represents a single number 0-15 as a single hex character
*/}}
{{- define "randHex" -}}
    {{- $result := "" }}
    {{- range $i := until . }}
        {{- $rand_hex_char := mod (randNumeric 4 | atoi) 16 | printf "%x" }}
        {{- $result = print $result $rand_hex_char }}
    {{- end }}
    {{- $result }}
{{- end }}

---
{{- $clientSecret := randAlphaNum 32 }}
# this has to be in b64 format since the application reads the b64 string, so it's encoded twice in kube secret
{{- $cookieSecret := include "randHex" 64 }}
{{- $existingSecret := (lookup "v1" "Secret" .Release.Namespace "jupyterhub-oidc-secret") }}
{{- if $existingSecret }}
  {{- if (index $existingSecret.data "clientSecret") }}
    {{- $clientSecret = index $existingSecret.data "clientSecret" | b64dec }}
  {{- end }}
  {{- if (index $existingSecret.data "cookieSecret") }}
    {{- $cookieSecret = index $existingSecret.data "cookieSecret" | b64dec }}
  {{- end }}
{{- end }}
apiVersion: v1
kind: Secret
metadata:
  name: jupyterhub-oidc-secret
  labels:
    {{- include "jupyterhub-secrets.labels" . | nindent 4 }}
    app.kubernetes.io/component: oidc
stringData:
  clientId: {{ .Values.clientId }}
  clientSecret: {{ $clientSecret }}
  cookieSecret: {{ $cookieSecret }}
type: Opaque
