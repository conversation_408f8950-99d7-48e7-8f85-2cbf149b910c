{{/*
Expand the name of the chart.
*/}}
{{- define "jupyterhub-secrets.name" -}}
{{- .Chart.Name | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "jupyterhub-secrets.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "jupyterhub-secrets.labels" -}}
helm.sh/chart: {{ include "jupyterhub-secrets.chart" . }}
{{ include "jupyterhub-secrets.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/part-of: jupyterhub
{{- end }}

{{/*
Selector labels
*/}}
{{- define "jupyterhub-secrets.selectorLabels" -}}
app.kubernetes.io/name: {{ include "jupyterhub-secrets.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}
