{{- if .Values.proxy.traefik.pdb.enabled -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: proxy
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
spec:
  {{- if not (typeIs "<nil>" .Values.proxy.traefik.pdb.maxUnavailable) }}
  maxUnavailable: {{ .Values.proxy.traefik.pdb.maxUnavailable }}
  {{- end }}
  {{- if not (typeIs "<nil>" .Values.proxy.traefik.pdb.minAvailable) }}
  minAvailable: {{ .Values.proxy.traefik.pdb.minAvailable }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "jupyterhub.matchLabels" . | nindent 6 }}
{{- end }}
