{{- $HTTPS := (and .Values.proxy.https.hosts .Values.proxy.https.enabled) -}}
{{- $autoHTTPS := (and $HTTPS (eq .Values.proxy.https.type "letsencrypt")) -}}
{{- if $autoHTTPS -}}
{{- if .Values.proxy.traefik.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "jupyterhub.autohttps-serviceaccount.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
{{- end }}
{{- end }}
