{{- if .Values.proxy.chp.pdb.enabled -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "jupyterhub.proxy.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
spec:
  {{- if not (typeIs "<nil>" .Values.proxy.chp.pdb.maxUnavailable) }}
  maxUnavailable: {{ .Values.proxy.chp.pdb.maxUnavailable }}
  {{- end }}
  {{- if not (typeIs "<nil>" .Values.proxy.chp.pdb.minAvailable) }}
  minAvailable: {{ .Values.proxy.chp.pdb.minAvailable }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "jupyterhub.matchLabels" . | nindent 6 }}
{{- end }}
