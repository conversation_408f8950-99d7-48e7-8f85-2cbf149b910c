{{- $manualHTTPS := and .Values.proxy.https.enabled (eq .Values.proxy.https.type "manual") -}}
{{- if $manualHTTPS -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "jupyterhub.proxy-public-manual-tls.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
type: kubernetes.io/tls
data:
  tls.crt: {{ .Values.proxy.https.manual.cert | required "Required configuration missing: proxy.https.manual.cert" | b64enc }}
  tls.key: {{ .Values.proxy.https.manual.key | required "Required configuration missing: proxy.https.manual.key" | b64enc }}
{{- end }}
