kind: Secret
apiVersion: v1
metadata:
  name: {{ include "jupyterhub.hub.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
type: Opaque
data:
  {{- $values := merge dict .Values }}
  {{- /* also passthrough subset of Chart / Release */}}
  {{- $_ := set $values "Chart" (dict "Name" .Chart.Name "Version" .Chart.Version "AppVersion" .Chart.AppVersion) }}
  {{- $_ := set $values "Release" (pick .Release "Name" "Namespace" "Service") }}
  values.yaml: {{ $values | toYaml | b64enc | quote }}

  {{- with .Values.hub.db.password }}
  # Used to mount MYSQL_PWD or PGPASSWORD on hub pod, unless hub.existingSecret
  # is set as then that k8s Secret's value must be specified instead.
  hub.db.password: {{ . | b64enc | quote }}
  {{- end }}

  # Any JupyterHub Services api_tokens are exposed in this k8s Secret as a
  # convinience for external services running in the k8s cluster that could
  # mount them directly from this k8s Secret.
  {{- range $key, $service := .Values.hub.services }}
  hub.services.{{ $key }}.apiToken: {{ include "jupyterhub.hub.services.get_api_token" (list $ $key) | b64enc | quote }}
  {{- end }}

  # During Helm template rendering, these values that can be autogenerated for
  # users are set using the following logic:
  #
  # 1. Use chart configuration's value
  # 2. Use k8s Secret's value
  # 3. Use a new autogenerated value
  #
  # hub.config.ConfigurableHTTPProxy.auth_token: for hub to proxy-api authorization (JupyterHub.proxy_auth_token is deprecated)
  # hub.config.JupyterHub.cookie_secret:         for cookie encryption
  # hub.config.CryptKeeper.keys:                 for auth state encryption
  #
  hub.config.ConfigurableHTTPProxy.auth_token: {{ include "jupyterhub.hub.config.ConfigurableHTTPProxy.auth_token" . | required "This should not happen: blank output from 'jupyterhub.hub.config.ConfigurableHTTPProxy.auth_token' template" | b64enc | quote }}
  hub.config.JupyterHub.cookie_secret: {{ include "jupyterhub.hub.config.JupyterHub.cookie_secret" . | required "This should not happen: blank output from 'jupyterhub.hub.config.JupyterHub.cookie_secret' template" | b64enc | quote }}
  hub.config.CryptKeeper.keys: {{ include "jupyterhub.hub.config.CryptKeeper.keys" . | required "This should not happen: blank output from 'jupyterhub.hub.config.CryptKeeper.keys' template" | b64enc | quote }}

  {{- with include "jupyterhub.extraFiles.data" .Values.hub.extraFiles }}
  {{- . | nindent 2 }}
  {{- end }}

{{- with include "jupyterhub.extraFiles.stringData" .Values.hub.extraFiles }}
stringData:
  {{- . | nindent 2 }}
{{- end }}
