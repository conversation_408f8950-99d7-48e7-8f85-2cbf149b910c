{{- if .Values.hub.pdb.enabled -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "jupyterhub.hub.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
spec:
  {{- if not (typeIs "<nil>" .Values.hub.pdb.maxUnavailable) }}
  maxUnavailable: {{ .Values.hub.pdb.maxUnavailable }}
  {{- end }}
  {{- if not (typeIs "<nil>" .Values.hub.pdb.minAvailable) }}
  minAvailable: {{ .Values.hub.pdb.minAvailable }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "jupyterhub.matchLabels" . | nindent 6 }}
{{- end }}
