{{- if .Values.rbac.create -}}
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "jupyterhub.hub.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
rules:
  - apiGroups: [""]       # "" indicates the core API group
    resources: ["pods", "persistentvolumeclaims", "secrets", "services"]
    verbs: ["get", "watch", "list", "create", "delete"]
  - apiGroups: [""]       # "" indicates the core API group
    resources: ["events"]
    verbs: ["get", "watch", "list"]
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "jupyterhub.hub.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    name: {{ include "jupyterhub.hub-serviceaccount.fullname" . }}
    namespace: "{{ .Release.Namespace }}"
roleRef:
  kind: Role
  name: {{ include "jupyterhub.hub.fullname" . }}
  apiGroup: rbac.authorization.k8s.io
{{- end }}
