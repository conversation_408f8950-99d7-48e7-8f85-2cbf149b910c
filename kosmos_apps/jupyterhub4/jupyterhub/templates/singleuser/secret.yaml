{{- if .Values.singleuser.extraFiles }}
kind: Secret
apiVersion: v1
metadata:
  name: {{ include "jupyterhub.singleuser.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
type: Opaque
{{- with include "jupyterhub.extraFiles.data" .Values.singleuser.extraFiles }}
data:
  {{- . | nindent 2 }}
{{- end }}
{{- with include "jupyterhub.extraFiles.stringData" .Values.singleuser.extraFiles }}
stringData:
  {{- . | nindent 2 }}
{{- end }}
{{- end }}
