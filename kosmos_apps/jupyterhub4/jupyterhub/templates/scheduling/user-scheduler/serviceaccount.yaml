{{- if .Values.scheduling.userScheduler.enabled -}}
{{- if .Values.scheduling.userScheduler.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "jupyterhub.user-scheduler-serviceaccount.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
  {{- with .Values.scheduling.userScheduler.serviceAccount.annotations }}
  annotations:
    {{- . | toYaml | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
