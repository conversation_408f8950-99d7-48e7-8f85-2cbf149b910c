{{- if .Values.scheduling.userScheduler.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "jupyterhub.user-scheduler-deploy.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
spec:
  {{- if not (typeIs "<nil>" .Values.scheduling.userScheduler.revisionHistoryLimit) }}
  revisionHistoryLimit: {{ .Values.scheduling.userScheduler.revisionHistoryLimit }}
  {{- end }}
  replicas: {{ .Values.scheduling.userScheduler.replicas }}
  selector:
    matchLabels:
      {{- include "jupyterhub.matchLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "jupyterhub.matchLabelsLegacyAndModern" . | nindent 8 }}
        {{- with .Values.scheduling.userScheduler.labels }}
        {{- . | toYaml | nindent 8 }}
        {{- end }}
      annotations:
        checksum/config-map: {{ include (print $.Template.BasePath "/scheduling/user-scheduler/configmap.yaml") . | sha256sum }}
        {{- with .Values.scheduling.userScheduler.annotations }}
        {{- . | toYaml | nindent 8 }}
        {{- end }}
    spec:
      {{ with include "jupyterhub.user-scheduler-serviceaccount.fullname" . }}
      serviceAccountName: {{ . }}
      {{- end }}
      {{- if .Values.scheduling.podPriority.enabled }}
      priorityClassName: {{ include "jupyterhub.priority.fullname" . }}
      {{- end }}
      {{- with .Values.scheduling.userScheduler.nodeSelector }}
      nodeSelector:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      {{- with concat .Values.scheduling.corePods.tolerations .Values.scheduling.userScheduler.tolerations }}
      tolerations:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      {{- include "jupyterhub.coreAffinity" . | nindent 6 }}
      volumes:
        - name: config
          configMap:
            name: {{ include "jupyterhub.user-scheduler-deploy.fullname" . }}
      {{- with include "jupyterhub.imagePullSecrets" (dict "root" . "image" .Values.scheduling.userScheduler.image) }}
      imagePullSecrets: {{ . }}
      {{- end }}
      containers:
        - name: kube-scheduler
          image: {{ .Values.scheduling.userScheduler.image.name }}:{{ .Values.scheduling.userScheduler.image.tag }}
          {{- with .Values.scheduling.userScheduler.image.pullPolicy }}
          imagePullPolicy: {{ . }}
          {{- end }}
          command:
            - /usr/local/bin/kube-scheduler
            # NOTE: --authentication-skip-lookup=true is used to avoid a
            #       seemingly harmless error, if we need to not skip
            #       "authentication lookup" in the future, see the linked issue.
            #
            # ref: https://github.com/jupyterhub/zero-to-jupyterhub-k8s/issues/1894
            - --config=/etc/user-scheduler/config.yaml
            - --authentication-skip-lookup=true
            - --v={{ .Values.scheduling.userScheduler.logLevel }}
          volumeMounts:
            - mountPath: /etc/user-scheduler
              name: config
          livenessProbe:
            httpGet:
              path: /healthz
              scheme: HTTPS
              port: 10259
            initialDelaySeconds: 15
          readinessProbe:
            httpGet:
              path: /healthz
              scheme: HTTPS
              port: 10259
          {{- with .Values.scheduling.userScheduler.resources }}
          resources:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
          {{- with .Values.scheduling.userScheduler.containerSecurityContext }}
          securityContext:
            {{- . | toYaml | nindent 12 }}
          {{- end }}
      {{- with .Values.scheduling.userScheduler.extraPodSpec }}
      {{- . | toYaml | nindent 6 }}
      {{- end }}
{{- end }}
