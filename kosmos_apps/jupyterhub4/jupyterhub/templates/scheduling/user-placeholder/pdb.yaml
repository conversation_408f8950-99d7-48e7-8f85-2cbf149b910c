{{- /*
The cluster autoscaler should be allowed to evict and reschedule these pods if
it would help in order to scale down a node.
*/}}
{{- if .Values.scheduling.userPlaceholder.enabled -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "jupyterhub.user-placeholder.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
spec:
  minAvailable: 0
  selector:
    matchLabels:
      {{- include "jupyterhub.matchLabels" . | nindent 6 }}
{{- end }}
