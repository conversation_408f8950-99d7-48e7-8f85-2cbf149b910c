{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "jupyterhub.ingress.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- . | toYaml | nindent 4 }}
  {{- end }}
spec:
  {{- with .Values.ingress.ingressClassName }}
  ingressClassName: "{{ . }}"
  {{- end }}
  rules:
    {{- range $host := .Values.ingress.hosts | default (list "") }}
    - http:
        paths:
          - path: {{ $.Values.hub.baseUrl | trimSuffix "/" }}/{{ $.Values.ingress.pathSuffix }}
            pathType: {{ $.Values.ingress.pathType }}
            backend:
              service:
                name: {{ include "jupyterhub.proxy-public.fullname" $ }}
                port:
                  name: http
          {{- with $.Values.ingress.extraPaths }}  
          {{- . | toYaml | nindent 10 }}  
          {{- end }}  
      {{- if $host }}
      host: {{ $host | quote }}
      {{- end }}
    {{- end }}
  {{- with .Values.ingress.tls }}
  tls:
    {{- . | toYaml | nindent 4 }}
  {{- end }}
{{- end }}
