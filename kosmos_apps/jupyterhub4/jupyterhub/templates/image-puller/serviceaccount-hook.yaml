{{- /*
ServiceAccount for the hook image-puller daemonset
*/}}
{{- if .Values.prePuller.hook.serviceAccountImagePuller.create -}}
{{- if (include "jupyterhub.imagePuller.daemonset.hook.install" .) -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "jupyterhub.hook-image-puller-serviceaccount.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
    hub.jupyter.org/deletable: "true"
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
    "helm.sh/hook-weight": "-10"
    {{- with .Values.prePuller.hook.serviceAccountImagePuller.annotations }}
    {{- . | toYaml | nindent 4 }}
    {{- end }}
{{- end }}
{{- end }}
