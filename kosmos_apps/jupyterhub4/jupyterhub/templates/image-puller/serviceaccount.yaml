{{- /*
ServiceAccount for the pre-puller hook's image-awaiter-job
*/}}
{{- if .Values.prePuller.hook.serviceAccount.create -}}
{{- if (include "jupyterhub.imagePuller.daemonset.hook.install" .) -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "jupyterhub.hook-image-awaiter-serviceaccount.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
    hub.jupyter.org/deletable: "true"
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
    "helm.sh/hook-weight": "0"
    {{- with .Values.prePuller.hook.serviceAccount.annotations }}
    {{- . | toYaml | nindent 4 }}
    {{- end }}
{{- end }}
{{- end }}
