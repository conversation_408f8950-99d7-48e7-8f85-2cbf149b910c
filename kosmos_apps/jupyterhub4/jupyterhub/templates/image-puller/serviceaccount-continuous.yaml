{{- /*
ServiceAccount for the continuous image-puller daemonset
*/}}
{{- if .Values.prePuller.continuous.serviceAccount.create -}}
{{- if .Values.prePuller.continuous.enabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "jupyterhub.continuous-image-puller.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.prePuller.continuous.serviceAccount.annotations }}
    {{- . | toYaml | nindent 4 }}
    {{- end }}
{{- end }}
{{- end }}
