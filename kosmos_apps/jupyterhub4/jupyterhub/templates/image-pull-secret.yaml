{{- if .Values.imagePullSecret.create }}
kind: Secret
apiVersion: v1
metadata:
  name: {{ include "jupyterhub.image-pull-secret.fullname" . }}
  labels:
    {{- include "jupyterhub.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation
    "helm.sh/hook-weight": "-20"
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: {{ include "jupyterhub.dockerconfigjson" . }}
{{- end }}
