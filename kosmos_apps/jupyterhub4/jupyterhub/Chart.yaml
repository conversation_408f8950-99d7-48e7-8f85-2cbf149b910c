annotations:
  artifacthub.io/images: |
    - image: quay.io/jupyterhub/configurable-http-proxy:4.6.3
      name: configurable-http-proxy
    - image: quay.io/jupyterhub/k8s-hub:4.2.0
      name: k8s-hub
    - image: quay.io/jupyterhub/k8s-image-awaiter:4.2.0
      name: k8s-image-awaiter
    - image: quay.io/jupyterhub/k8s-network-tools:4.2.0
      name: k8s-network-tools
    - image: quay.io/jupyterhub/k8s-secret-sync:4.2.0
      name: k8s-secret-sync
    - image: quay.io/jupyterhub/k8s-singleuser-sample:4.2.0
      name: k8s-singleuser-sample
    - image: registry.k8s.io/kube-scheduler:v1.30.11
      name: kube-scheduler
    - image: registry.k8s.io/pause:3.10
      name: pause
    - image: registry.k8s.io/pause:3.10
      name: pause
    - image: traefik:v3.3.5
      name: traefik
apiVersion: v2
appVersion: 5.3.0
description: Multi-user Jupyter installation
home: https://z2jh.jupyter.org
icon: https://hub.jupyter.org/helm-chart/images/hublogo.svg
keywords:
- jupyter
- jupyterhub
- z2jh
kubeVersion: '>=1.28.0-0'
maintainers:
- email: <EMAIL>
  name: Erik <PERSON>dell
- name: Simon Li
  url: https://github.com/manics/
name: jupyterhub
sources:
- https://github.com/jupyterhub/zero-to-jupyterhub-k8s
version: 4.2.0
