{"__inputs": [{"name": "DS_PROMETHEUS", "label": "clickhouse-operator-prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "panel", "id": "bargauge", "name": "Bar gauge", "version": ""}, {"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.1.1"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "ZooKeeper Dashboard for Prometheus metrics scraper", "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "iteration": 1622185085598, "links": [], "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 14, "panels": [], "title": "Overall", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "hiddenSeries": false, "id": 224, "interval": "", "legend": {"alignAsTable": false, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "znode_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} znode_count", "refId": "A"}, {"expr": "ephemerals_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} ephemerals", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "znode_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "hiddenSeries": false, "id": 4, "interval": "", "legend": {"alignAsTable": false, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(znode_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} znode_count_rate", "refId": "A"}, {"expr": "rate(ephemerals_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} ephemerals_rate", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "znode_count_rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "displayName": "", "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 48, "links": [], "options": {"displayMode": "lcd", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": true}, "pluginVersion": "7.1.1", "targets": [{"expr": "global_sessions{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "global_sessions", "type": "bargauge"}, {"cacheTimeout": null, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 52, "links": [], "options": {"displayMode": "lcd", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": true}, "pluginVersion": "7.1.1", "targets": [{"expr": "local_sessions{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "local_sessions ", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "hiddenSeries": false, "id": 168, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(write_per_namespace_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} write_per_namespace:{{key}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "write_per_namespace", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "hiddenSeries": false, "id": 200, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(read_per_namespace_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} read_per_namespace:{{key}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "read_per_namespace", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 25}, "hiddenSeries": false, "id": 132, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "approximate_data_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} approximate_data_size", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "approximate_data_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 25}, "hiddenSeries": false, "id": 90, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(packets_received{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} packets_received", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "packets_received", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2720", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2721", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 25}, "hiddenSeries": false, "id": 56, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(packets_sent{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} packets_sent", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "packets_sent", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2773", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2774", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 3, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 33}, "hiddenSeries": false, "id": 184, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:302", "alias": "/.+response_packet_cache_hits.*/", "color": "#37872D"}, {"$$hashKey": "object:303", "alias": "/.+response_packet_get_children_cache_hits.*/", "color": "#F2CC0C"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(response_packet_cache_hits{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / (increase(response_packet_cache_misses{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) + increase(response_packet_cache_hits{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} response_packet_cache_hits_rate", "refId": "A"}, {"expr": "increase(response_packet_get_children_cache_hits{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / (increase(response_packet_get_children_cache_misses{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) + increase(response_packet_get_children_cache_hits{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])) ", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} response_packet_get_children_cache_hits_rate", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "response_packet_cache", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:235", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:236", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 33}, "hiddenSeries": false, "id": 120, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(unrecoverable_error_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} unrecoverable_error_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "unrecoverable_error_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:382", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:383", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 33}, "hiddenSeries": false, "id": 235, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(digest_mismatches_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} digest_mismatches_count", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "digest_mismatches_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:435", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:436", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 41}, "hiddenSeries": false, "id": 140, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "startup_snap_load_time", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} startup_snap_load_time-{{quantile}}", "refId": "A"}, {"expr": "startup_snap_load_time_count", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} startup_snap_load_time_count", "refId": "B"}, {"expr": "increase(startup_snap_load_time_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(startup_snap_load_time_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} startup_snap_load_time", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "startup_snap_load_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:500", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:501", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 41}, "hiddenSeries": false, "id": 192, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "startup_txns_loaded", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} startup_txns_loaded-{{quantile}}", "refId": "A"}, {"expr": "startup_txns_loaded_count", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} startup_txns_loaded_count", "refId": "B"}, {"expr": "increase(startup_txns_loaded_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(startup_txns_loaded_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} startup_txns_loaded", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "startup_txns_loaded", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:553", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:554", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 41}, "hiddenSeries": false, "id": 210, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "d<PERSON><PERSON><PERSON>", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} dbinittime-{{quantile}}", "refId": "A"}, {"expr": "dbinittime_count", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} dbinittime_count", "refId": "B"}, {"expr": "dbinittime_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} db_init_time", "refId": "C"}, {"expr": "dbinittime_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} db_init_time", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "db_init_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:606", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:607", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 49}, "id": 82, "panels": [{"cacheTimeout": null, "colorBackground": true, "colorPostfix": false, "colorPrefix": false, "colorValue": false, "colors": ["#73BF69", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "short", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 50}, "id": 124, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.2", "postfix": "", "postfixFontSize": "80%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "max(quorum_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "quorum_size", "refId": "A"}], "thresholds": "50,80", "timeFrom": null, "timeShift": null, "title": "quorum_size", "type": "singlestat", "valueFontSize": "200%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#F2495C", "#F2495C", "#F2495C"], "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "ms", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 50}, "id": 225, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.2", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "leader_uptime{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}}", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "leader", "type": "singlestat", "valueFontSize": "120%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "name"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#5794F2", "#5794F2", "#5794F2"], "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "ms", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 50}, "id": 222, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.2.2", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "leader_uptime{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} leader_uptime", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "leader_uptime", "type": "singlestat", "valueFontSize": "120%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 58}, "id": 215, "links": [], "options": {"displayMode": "lcd", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": true}, "pluginVersion": "7.1.1", "targets": [{"expr": "max(learners{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "learners", "refId": "B"}, {"expr": "max(synced_non_voting_followers{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "synced_non_voting_followers", "refId": "C"}, {"expr": "max(synced_observers{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "synced_observers", "refId": "D"}], "timeFrom": null, "timeShift": null, "title": "learner/observer", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 66}, "hiddenSeries": false, "id": 70, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "election_time", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} election_time", "refId": "A"}, {"expr": "election_time_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} election_time_sum(ms)", "refId": "C"}, {"expr": "election_time_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} election_time_count", "refId": "B"}, {"expr": "increase(election_time_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(election_time_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} election_avg_time", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "election_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:711", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:712", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 74}, "hiddenSeries": false, "id": 144, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "uptime{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} uptime", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "quorum uptime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 74}, "hiddenSeries": false, "id": 130, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "learner_commit_received_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} learner_commit_received_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "learner_commit_received_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 82}, "hiddenSeries": false, "id": 148, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "commit_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} commit_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "commit_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 82}, "hiddenSeries": false, "id": 166, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "snap_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} snap_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "snap_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 82}, "hiddenSeries": false, "id": 186, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "diff_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} diff_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diff_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 82}, "hiddenSeries": false, "id": 206, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "looking_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} looking_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "looking_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 90}, "hiddenSeries": false, "id": 154, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "proposal_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} proposal_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "proposal_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 90}, "hiddenSeries": false, "id": 214, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "last_proposal_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} last_proposal_size", "refId": "A"}, {"expr": "max_proposal_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} max_proposal_size", "refId": "B"}, {"expr": "min_proposal_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} min_proposal_size", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "proposal_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 90}, "hiddenSeries": false, "id": 202, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(follower_sync_time_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} follower_sync_time", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "follower_sync_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 98}, "hiddenSeries": false, "id": 217, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "learner_handler_qp_size_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} learner_handler_qp_size_sum sid:{{key}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "learner_handler_qp_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 98}, "hiddenSeries": false, "id": 208, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "quit_leading_due_to_disloyal_voter{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} quit_leading_due_to_disloyal_voter", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "quit_leading_due_to_disloyal_voter", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 98}, "hiddenSeries": false, "id": 219, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(om_commit_process_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} om_commit_process_time", "refId": "C"}, {"expr": "rate(om_proposal_process_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} om_proposal_process_time", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Observer Master", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Quorum/Leader Election", "type": "row"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 36, "panels": [], "title": "Session/Watch", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 51}, "hiddenSeries": false, "id": 212, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "watch_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} watch_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "watch_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:776", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:777", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 51}, "hiddenSeries": false, "id": 178, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"expr": "irate(node_changed_watch_count_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} node_changed_watch_count", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "node_changed_watch_triggered", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:829", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:830", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 51}, "hiddenSeries": false, "id": 174, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_children_watch_count_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} node_children_watch_count", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "node_children_watch_triggered", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:882", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:883", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 59}, "hiddenSeries": false, "id": 150, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"expr": "irate(node_deleted_watch_count_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} node_deleted_watch_count", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "node_deleted_watch_triggered", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:998", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:999", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 59}, "hiddenSeries": false, "id": 96, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_created_watch_count_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} node_created_watch_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "node_created_watch_triggered", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1306", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1307", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 59}, "hiddenSeries": false, "id": 223, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(revalidate_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} revalidate_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "revalidate_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1359", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1360", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 67}, "hiddenSeries": false, "id": 46, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"expr": "irate(stale_sessions_expired{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} stale_sessions_expired", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "stale_sessions_expired", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1424", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1425", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 67}, "hiddenSeries": false, "id": 194, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:1496", "alias": "/.+dead_watchers_cleaner_latency_avg$/", "color": "#E02F44", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(dead_watchers_cleared{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} dead_watchers_cleared", "refId": "A"}, {"expr": "irate(dead_watchers_queued{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} dead_watchers_queued", "refId": "B"}, {"expr": "increase(dead_watchers_cleaner_latency_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(dead_watchers_cleaner_latency_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} dead_watchers_cleaner_latency_avg", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "dead_watchers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1478", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1479", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 67}, "hiddenSeries": false, "id": 62, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"expr": "irate(add_dead_watcher_stall_time{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} add_dead_watcher_stall_time", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "add_dead_watcher_stall_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1543", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1544", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 75}, "id": 102, "panels": [], "title": "Client/Connection", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 76}, "hiddenSeries": false, "id": 66, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "outstanding_requests{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} outstanding_requests", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "outstanding_requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1608", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1609", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 76}, "hiddenSeries": false, "id": 104, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "last_client_response_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} last_client_response_size", "refId": "A"}, {"expr": "min_client_response_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} min_client_response_size", "refId": "B"}, {"expr": "max_client_response_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} max_client_response_size", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "client_response_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1661", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1662", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 76}, "hiddenSeries": false, "id": 146, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(bytes_received_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} bytes_received_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "bytes_received", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1714", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1715", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 84}, "hiddenSeries": false, "id": 176, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(connection_request_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} connection_request_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "connection_request_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1771", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1772", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 84}, "hiddenSeries": false, "id": 190, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "num_alive_connections{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} num_alive_connections", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "num_alive_connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1824", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1825", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 84}, "hiddenSeries": false, "id": 86, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(connection_rejected{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} connection_rejected", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "connection_rejected", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1877", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1878", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 92}, "hiddenSeries": false, "id": 128, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(connection_drop_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} connection_drop_count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "connection_drop_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1930", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1931", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 92}, "hiddenSeries": false, "id": 60, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "connection_drop_probability{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} connection_drop_probability", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "connection_drop_probability", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2084", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2085", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 92}, "hiddenSeries": false, "id": 54, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(sessionless_connections_expired{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sessionless_connections_expired", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "sessionless_connections_expired", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2137", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2138", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "how much tokens deficit per sec, during connection throttling", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 100}, "hiddenSeries": false, "id": 180, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(connection_token_deficit_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} connection_token_deficit", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "connection_token_deficit", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2190", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2191", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 108}, "id": 112, "panels": [], "title": "Disk/Snapshot", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {}, "mappings": [], "max": 1024, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 800}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 109}, "id": 122, "interval": "", "links": [], "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "7.1.1", "targets": [{"expr": "open_file_descriptor_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "open_file_descriptor", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "frequency and the average time for FSYNC", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 117}, "hiddenSeries": false, "id": 114, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "sideWidth": null, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2339", "alias": "/.+fsynctime quantile.*/", "color": "#E02F44"}, {"$$hashKey": "object:306", "alias": "/.+fsynctime_count_rate/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "fsynctime{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} fsynctime quantile={{quantile}}", "refId": "A"}, {"expr": "irate(fsynctime_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} fsynctime_count_rate", "refId": "B"}, {"expr": "fsynctime_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} fsynctime_sum(ms)", "refId": "C"}, {"expr": "(increase(fsynctime_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])) / increase(fsynctime_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} fsynctime_avg(μs)", "refId": "D"}, {"expr": "fsynctime_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} fsynctime_rate", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "fsync_time / fsync_rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2321", "decimals": null, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2322", "decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": 10}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 125}, "hiddenSeries": false, "id": 88, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2544", "alias": "/.+snapshottime_count_rate.*/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"expr": "snapshottime{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} snapshot_time quantile={{quantile}}", "refId": "C"}, {"expr": "increase(snapshottime_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} snapshottime_count_rate", "refId": "D"}, {"expr": "snapshottime_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} snapshottime_count", "refId": "A"}, {"expr": "snapshottime_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"} / snapshottime_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} snapshottime_avg(ms)", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "snapshot_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2526", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2527", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 133}, "id": 136, "panels": [], "title": "Prep_Processor", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 134}, "hiddenSeries": false, "id": 158, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(prep_process_time_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(prep_process_time_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} prep_process_time", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "prep_process_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2826", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2827", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 134}, "hiddenSeries": false, "id": 138, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prep_processor_queue_time_ms{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\", quantile=\"0.99\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} prep_processor_queue_time_ms quantile={{quantile}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "prep_processor_queue_time quantile=0.99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 134}, "hiddenSeries": false, "id": 156, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(prep_processor_queue_size_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} prep_processor_queue_size", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "prep_processor_queue_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "queued per second", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 142}, "hiddenSeries": false, "id": 160, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(prep_processor_request_queued{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} prep_processor_request_queued", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "prep_processor_request_queued", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "changes per second", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 142}, "hiddenSeries": false, "id": 92, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(outstanding_changes_queued{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} outstanding_changes_queued", "refId": "A"}, {"expr": "irate(outstanding_changes_removed{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} outstanding_changes_removed", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "outstanding_changes queued / removed ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 142}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "close_session_prep_time{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\", quantile=\"0.99\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} close_session_prep_time quantile={{quantile}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "close_session_prep_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3003", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3004", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 150}, "id": 2, "panels": [], "repeat": null, "title": "Sync_Processor", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "milliseconds per second", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 151}, "hiddenSeries": false, "id": 142, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(sync_process_time_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])/increase(sync_process_time_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_process_time", "refId": "C"}, {"expr": "sync_process_time{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_process_time {{quantile}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "sync_process_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3068", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3069", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 151}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sync_processor_queue_flush_time_ms{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_processor_queue_flush_time quantile={{quantile}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "sync_processor_queue_flush_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3099", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3100", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 151}, "hiddenSeries": false, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(sync_processor_queue_size_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(sync_processor_queue_size_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_processor_queue_size", "refId": "C"}, {"expr": "sync_processor_queue_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_processor_queue_size", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "sync_processor_queue_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3174", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3175", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "queued requests per second", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 159}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(sync_processor_request_queued{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_processor_request_queued", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "sync_processor_request_queued", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3238", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3239", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 159}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(sync_processor_batch_size_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(sync_processor_batch_size_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_processor_batch_size", "refId": "C"}, {"expr": "sync_processor_batch_size{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} sync_processor_batch_size quantile={{quantile}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "sync_processor_batch_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3291", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3292", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 167}, "id": 108, "panels": [], "title": "Commit_Processor", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 168}, "hiddenSeries": false, "id": 118, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(commit_process_time_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(commit_process_time_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} commit_process_time", "refId": "C"}, {"expr": "increase(read_commitproc_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(read_commitproc_time_ms_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} read_commitproc_time", "refId": "A"}, {"expr": "increase(write_commitproc_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(write_commitproc_time_ms_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} write_commitproc_time", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "commit_process_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3356", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3357", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 168}, "hiddenSeries": false, "id": 72, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(write_commit_proc_req_queued_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} write_commit_proc_req_queued", "refId": "C"}, {"expr": "irate(read_commit_proc_req_queued_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} read_commit_proc_req_queued", "refId": "A"}, {"expr": "irate(commit_commit_proc_req_queued_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} commit_commit_proc_req_queued", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "commit_proc_req_queued", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3493", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3494", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 168}, "hiddenSeries": false, "id": 44, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(requests_in_session_queue_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} requests_in_session_queue", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "requests_in_session_queue", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3546", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3547", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 176}, "hiddenSeries": false, "id": 58, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(read_commit_proc_issued_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} read_commit_proc_issued", "refId": "C"}, {"expr": "rate(write_commit_proc_issued_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} write_commit_proc_issued", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "commit_proc_issued", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 176}, "hiddenSeries": false, "id": 164, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(concurrent_request_processing_in_commit_processor_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} concurrent_request_processing_in_commit_processor", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "concurrent_request_processing_in_commit_processor", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 176}, "hiddenSeries": false, "id": 188, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(time_waiting_empty_pool_in_commit_processor_read_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} time_waiting_empty_pool_in_commit_processor_read", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "time_waiting_empty_pool_in_commit_processor_read", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 184}, "hiddenSeries": false, "id": 30, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(pending_session_queue_size_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} pending_session_queue_size", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "pending_session_queue_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 184}, "hiddenSeries": false, "id": 94, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(local_write_committed_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} local_write_committed_time_ms", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "local_write_committed_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 184}, "hiddenSeries": false, "id": 74, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(server_write_committed_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} server_write_committed_time", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "server_write_committed_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 192}, "hiddenSeries": false, "id": 106, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(write_batch_time_in_commit_processor_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} write_batch_time_in_commit_processor", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "write_batch_time_in_commit_processor", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 192}, "hiddenSeries": false, "id": 34, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(reads_after_write_in_session_queue_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} reads_after_write_in_session_queue", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "reads_after_write_in_session_queue", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 192}, "hiddenSeries": false, "id": 50, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(session_queues_drained_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} session_queues_drained", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "session_queues_drained", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 200}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(reads_issued_from_session_queue_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} reads_issued_from_session_queue", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "reads_issued_from_session_queue", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 200}, "hiddenSeries": false, "id": 152, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "request_commit_queued{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{pod_name}}.{{namespace}} request_commit_queued", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "request_commit_queued", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 208}, "id": 170, "panels": [], "title": "Final_Processor", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 209}, "hiddenSeries": false, "id": 172, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(write_final_proc_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} write_final_proc_time_ms", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "write_final_proc_time_ms", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 209}, "hiddenSeries": false, "id": 228, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(read_final_proc_time_ms_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} read_final_proc_time_ms", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "read_final_proc_time_ms", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 217}, "id": 80, "panels": [], "title": "Latency", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 218}, "hiddenSeries": false, "id": 198, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "readlatency{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\", quantile=\"0.99\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} readlatency quantile={{quantile}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "read_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3599", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3600", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 218}, "hiddenSeries": false, "id": 78, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:3652"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "updatelatency{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\", quantile=\"0.99\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} updatelatency quantile={{quantile}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "update_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3657", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3658", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "decimals": null, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 226}, "hiddenSeries": false, "id": 100, "interval": "", "legend": {"avg": true, "current": true, "max": true, "min": true, "show": false, "total": true, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "min_latency{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} min_latency", "refId": "B"}, {"expr": "max_latency{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} max_latency", "refId": "A"}, {"expr": "avg_latency{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} avg_latency", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "max_min_avg_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3710", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3711", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 226}, "hiddenSeries": false, "id": 134, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(proposal_latency_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(proposal_latency_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} proposal_latency", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "proposal_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3763", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3764", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 234}, "hiddenSeries": false, "id": 162, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(quorum_ack_latency_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(quorum_ack_latency_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} quorum_ack_latency", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "quorum_ack_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3816", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3817", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 234}, "hiddenSeries": false, "id": 126, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "ack_latency{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} ack_latency_sum", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ack_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3869", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3870", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "normalized latency by last minute", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 242}, "hiddenSeries": false, "id": 196, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(propagation_latency_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(propagation_latency_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} propagation_latency", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "propagation_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3922", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3923", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "normalized latency by last minute", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 242}, "hiddenSeries": false, "id": 182, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(commit_propagation_latency_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(commit_propagation_latency_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} commit_propagation_latency", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "commit_propagation_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3975", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3976", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "normalized latency by last minute", "fieldConfig": {"defaults": {"custom": {"align": null}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 250}, "hiddenSeries": false, "id": 42, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(proposal_ack_creation_latency_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m]) / increase(proposal_ack_creation_latency_count{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} proposal_ack_creation_latency-{{quantile}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "proposal_ack_creation_latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4028", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4029", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 258}, "id": 238, "panels": [], "title": "Security", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "ops per second", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 259}, "hiddenSeries": false, "id": 236, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(tls_handshake_exceeded{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} tls_handshake_exceeded", "refId": "C"}, {"expr": "irate(outstanding_tls_handshake{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} outstanding_tls_handshake", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "tls_handshake", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4246", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4247", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "ops per second", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 259}, "hiddenSeries": false, "id": 64, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(ensemble_auth_fail{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} ensemble_auth_fail", "refId": "A"}, {"expr": "irate(ensemble_auth_success{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} ensemble_auth_success", "refId": "B"}, {"expr": "irate(ensemble_auth_skip{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} ensemble_auth_skip", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ensemble_auth", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4299", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4300", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 267}, "id": 227, "panels": [], "title": "JVM", "type": "row"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#37872D", "#37872D", "#37872D"], "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 268}, "id": 204, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "max(jvm_classes_loaded{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "C"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "jvm_classes_loaded", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#1F60C4", "#1F60C4", "#1F60C4"], "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "short", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 268}, "id": 229, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "max(jvm_threads_current{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "C"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "jvm_threads_current", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#E0B400", "#E0B400", "#E0B400"], "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "short", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 268}, "id": 230, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "max(jvm_threads_deadlocked{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "C"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "jvm_threads_deadlocked", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 276}, "hiddenSeries": false, "id": 231, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_pause_time_ms{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} jvm_pause_time_ms quantile={{quantile}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "jvm_pause_time_ms", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4356", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4357", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 276}, "hiddenSeries": false, "id": 232, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(jvm_gc_collection_seconds_sum{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} gc:{{gc}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "jvm_gc_collection_seconds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4409", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4410", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 276}, "hiddenSeries": false, "id": 233, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_threads_state{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} state:{{state}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "jvm_threads_state", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4462", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4463", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 284}, "hiddenSeries": false, "id": 234, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_memory_pool_bytes_used{namespace=~\"$namespace\",pod_name=~\"$pod_name\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod_name}}.{{namespace}} pool:{{pool}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "jvm_memory_pool_bytes_used", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4515", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4516", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 26, "style": "dark", "tags": ["Altinity", "zookeeper"], "templating": {"list": [{"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "includeAll": false, "multi": false, "name": "ds_prometheus", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values(znode_count,job)", "hide": 0, "includeAll": true, "label": "Scraping Job", "multi": true, "name": "job", "options": [], "query": "label_values(znode_count,job)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values(znode_count{job=~\"$job\"}, namespace)", "hide": 0, "includeAll": true, "label": "K8S Namespace", "multi": true, "name": "namespace", "options": [], "query": "label_values(znode_count{job=~\"$job\"}, namespace)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values(znode_count{job=~\"$job\",namespace=~\"$namespace\"}, pod_name)", "hide": 0, "includeAll": true, "label": "Pod", "multi": true, "name": "pod_name", "options": [], "query": "label_values(znode_count{job=~\"$job\",namespace=~\"$namespace\"}, pod_name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "ZooKeeper Dashboard", "uid": "zookeeper", "version": 20220214}