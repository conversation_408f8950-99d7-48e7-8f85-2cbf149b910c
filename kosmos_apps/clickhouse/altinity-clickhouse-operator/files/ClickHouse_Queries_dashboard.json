{"__inputs": [{"name": "DS_PROMETHEUS", "label": "clickhouse-operator-prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.5.17"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "text", "name": "Text", "version": ""}, {"type": "datasource", "id": "vertamedia-clickhouse-datasource", "name": "Altinity plugin for ClickHouse", "version": "2.5.4"}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Helps to visualize most frequent, slowest, failed queries.\r\nShows queries rate per second, table with last queries", "editable": true, "gnetId": 2515, "graphTooltip": 1, "id": null, "iteration": 1694526851642, "links": [], "panels": [{"collapsed": false, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "repeat": null, "title": "Top charts", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 1}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "expr": "", "extrapolate": false, "format": "time_series", "interval": "", "intervalFactor": 2, "query": "SELECT\r\n    t,\r\n    arrayMap(a -> (a.1, a.2 / (t/1000 - lagInFrame(t/1000,1,0) OVER ()) ), groupArr)\r\nFROM (\r\n    SELECT t, groupArray((q, c)) AS groupArr\r\n    FROM (\r\n        SELECT\r\n            (intDiv(toUInt32(event_time), 2) * 2) * 1000 AS t,\r\n            normalizeQuery(query) AS q,\r\n            count() c\r\n        FROM cluster('all-sharded',system.query_log)\r\n        WHERE $timeFilter\r\n        AND( ('$type' = '1,2,3,4' AND type != 'QueryStart') OR ('$type' != '1,2,3,4' AND type IN ($type)))\r\n        $conditionalTest(AND query_kind IN ($query_kind), $query_kind)\r\n        $conditionalTest(AND initial_user IN ($user), $user)\r\n        $conditionalTest(AND query_duration_ms >= $min_duration_ms, $min_duration_ms)\r\n        $conditionalTest(AND query_duration_ms <= $max_duration_ms, $max_duration_ms)\r\n        AND normalized_query_hash GLOBAL IN (\r\n            SELECT normalized_query_hash AS h\r\n            FROM cluster('all-sharded',system.query_log)\r\n            WHERE $timeFilter\r\n                AND( ('$type' = '1,2,3,4' AND type != 'QueryStart') OR ('$type' != '1,2,3,4' AND type IN ($type)))\r\n                $conditionalTest(AND query_kind IN ($query_kind), $query_kind)\r\n                $conditionalTest(AND type IN ($type), $type)\r\n                $conditionalTest(AND initial_user IN ($user), $user)\r\n                $conditionalTest(AND query_duration_ms >= $min_duration_ms, $min_duration_ms)\r\n                $conditionalTest(AND query_duration_ms <= $max_duration_ms, $max_duration_ms)\r\n            GROUP BY h\r\n            ORDER BY count() DESC\r\n            LIMIT $top\r\n            SETTINGS skip_unavailable_shards=1\r\n        )\r\n        GROUP BY t, query\r\n        ORDER BY t\r\n    )\r\n    GROUP BY t\r\n    ORDER BY t\r\n) SETTINGS skip_unavailable_shards=1", "rawQuery": "SELECT\r\n    t,\r\n    arrayMap(a -> (a.1, a.2 / (t/1000 - lagInFrame(t/1000,1,0) OVER ()) ), groupArr)\r\nFROM (\r\n    SELECT t, groupArray((q, c)) AS groupArr\r\n    FROM (\r\n        SELECT\r\n            (intDiv(toUInt32(event_time), 2) * 2) * 1000 AS t,\r\n            normalizeQuery(query) AS q,\r\n            count() c\r\n        FROM cluster('all-sharded',system.query_log)\r\n        WHERE event_date >= toDate(1694531137) AND event_date <= toDate(1694534737) AND event_time >= toDateTime(1694531137) AND event_time <= toDateTime(1694534737)\r\n        AND( ('1,2,3,4' = '1,2,3,4' AND type != 'QueryStart') OR ('1,2,3,4' != '1,2,3,4' AND type IN (1,2,3,4)))\r\n         \r\n         \r\n         \r\n         \r\n        AND normalized_query_hash GLOBAL IN (\r\n            SELECT normalized_query_hash AS h\r\n            FROM cluster('all-sharded',system.query_log)\r\n            WHERE event_date >= toDate(1694531137) AND event_date <= toDate(1694534737) AND event_time >= toDateTime(1694531137) AND event_time <= toDateTime(1694534737)\r\n                AND( ('1,2,3,4' = '1,2,3,4' AND type != 'QueryStart') OR ('1,2,3,4' != '1,2,3,4' AND type IN (1,2,3,4)))\r\n                 \r\n                 \r\n                 \r\n                 \r\n                 \r\n            GROUP BY h\r\n            ORDER BY count() DESC\r\n            LIMIT 30\r\n            SETTINGS skip_unavailable_shards=1\r\n        )\r\n        GROUP BY t, query\r\n        ORDER BY t\r\n    )\r\n    GROUP BY t\r\n    ORDER BY t\r\n) SETTINGS skip_unavailable_shards=1", "refId": "A", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Top $top request's rate by type: $type; user: $user; query kind: $query_kind", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [], "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "fieldConfig": {"defaults": {"custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "duration_ms"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "fontSize": "100%", "gridPos": {"h": 10, "w": 8, "x": 0, "y": 8}, "height": "400px", "id": 18, "links": [], "options": {"showHeader": true, "sortBy": []}, "pageSize": null, "pluginVersion": "7.5.17", "scroll": true, "showHeader": true, "sort": {"col": 2, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "t", "type": "hidden"}, {"alias": "duration", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "duration", "thresholds": [], "type": "number", "unit": "ms"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "count", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "expr": "", "extrapolate": false, "format": "table", "interval": "", "intervalFactor": 2, "query": "SELECT\n    normalizeQuery(query) AS query,\n    avg(query_duration_ms) duration_ms,\n    count() count\nFROM cluster('all-sharded',system.query_log)\nWHERE\n    $timeFilter\n    AND( ('$type' = '1,2,3,4' AND type != 'QueryStart') OR ('$type' != '1,2,3,4' AND type IN ($type)))\n    $conditionalTest(AND query_kind IN ($query_kind), $query_kind)\n    $conditionalTest(AND initial_user IN ($user), $user)\n    $conditionalTest(AND query_duration_ms >= $min_duration_ms,$min_duration_ms)\n    $conditionalTest(AND query_duration_ms <= $max_duration_ms,$max_duration_ms)\nGROUP BY query\nORDER BY duration_ms DESC\nLIMIT $top", "rawQuery": "SELECT\n    normalizeQuery(query) AS query,\n    avg(query_duration_ms) duration_ms,\n    count() count\nFROM cluster('all-sharded',system.query_log)\nWHERE\n    event_date >= toDate(1694531150) AND event_date <= toDate(1694534750) AND event_time >= toDateTime(1694531150) AND event_time <= toDateTime(1694534750)\n    AND( ('1,2,3,4' = '1,2,3,4' AND type != 'QueryStart') OR ('1,2,3,4' != '1,2,3,4' AND type IN (1,2,3,4)))\n     \n     \n     \n     \nGROUP BY query\nORDER BY duration_ms DESC\nLIMIT 30", "refId": "A", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}], "title": "Top slow queries by type: $type; user: $user; query kind: $query_kind", "transform": "table", "type": "table"}, {"columns": [], "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "fieldConfig": {"defaults": {"custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "usage"}, "properties": [{"id": "unit", "value": "bytes"}, {"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "fontSize": "100%", "gridPos": {"h": 10, "w": 8, "x": 8, "y": 8}, "height": "400px", "id": 19, "links": [], "options": {"showHeader": true}, "pageSize": null, "pluginVersion": "7.5.17", "scroll": true, "showHeader": true, "sort": {"col": 2, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "t", "type": "hidden"}, {"alias": "usage", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "usage", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "count", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "expr": "", "extrapolate": false, "format": "table", "intervalFactor": 2, "query": "SELECT\n    normalizeQuery(query) AS query,\n    avg(memory_usage) usage,\n    count() count\nFROM cluster('all-sharded',system.query_log)\nWHERE $timeFilter\n  AND( ('$type' = '1,2,3,4' AND type != 'QueryStart') OR ('$type' != '1,2,3,4' AND type IN ($type)))\n  $conditionalTest(AND query_kind IN ($query_kind), $query_kind)\n  $conditionalTest(AND initial_user IN ($user), $user)\n  $conditionalTest(AND query_duration_ms >= $min_duration_ms, $min_duration_ms)\n  $conditionalTest(AND query_duration_ms <= $max_duration_ms, $max_duration_ms)\nGROUP BY query\nORDER BY usage DESC\nLIMIT $top", "rawQuery": "SELECT\n    normalizeQuery(query) AS query,\n    avg(memory_usage) usage,\n    count() count\nFROM cluster('all-sharded',system.query_log)\nWHERE event_date >= toDate(1694531173) AND event_date <= toDate(1694534773) AND event_time >= toDateTime(1694531173) AND event_time <= toDateTime(1694534773)\n  AND( ('1,2,3,4' = '1,2,3,4' AND type != 'QueryStart') OR ('1,2,3,4' != '1,2,3,4' AND type IN (1,2,3,4)))\n   \n   \n   \n   \nGROUP BY query\nORDER BY usage DESC\nLIMIT 30", "refId": "A", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}], "title": "Top memory consumers by type: $type; user: $user; query kind: $query_kind", "transform": "table", "type": "table"}, {"columns": [], "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "fieldConfig": {"defaults": {"custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "type"}, "properties": [{"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "fontSize": "100%", "gridPos": {"h": 10, "w": 8, "x": 16, "y": 8}, "height": "400px", "id": 20, "links": [], "options": {"showHeader": true}, "pageSize": null, "pluginVersion": "7.5.17", "scroll": true, "showHeader": true, "sort": {"col": 3, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "t", "type": "hidden"}, {"alias": "type", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "type", "thresholds": [], "type": "number", "unit": "none"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "count", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "expr": "", "extrapolate": false, "format": "table", "intervalFactor": 2, "query": "SELECT\n    normalizeQuery(query) AS query,\n    type,\n    count() count\nFROM cluster('all-sharded',system.query_log)\nWHERE $timeFilter\n  AND( ('$type' = '1,2,3,4' AND type != 'QueryStart') OR ('$type' != '1,2,3,4' AND type IN ($type)))\n  AND type NOT IN ('QueryStart','QueryFinish')\n  $conditionalTest(AND query_kind IN ($query_kind), $query_kind)\n  $conditionalTest(AND initial_user IN ($user), $user)\n  $conditionalTest(AND query_duration_ms >= $min_duration_ms, $min_duration_ms)\n  $conditionalTest(AND query_duration_ms <= $max_duration_ms, $max_duration_ms)\nGROUP BY\n    query,\n    type\nORDER BY count DESC\nLIMIT $top\nSETTINGS skip_unavailable_shards=1", "rawQuery": "SELECT\n    normalizeQuery(query) AS query,\n    type,\n    count() count\nFROM cluster('all-sharded',system.query_log)\nWHERE event_date >= toDate(1694531196) AND event_date <= toDate(1694534796) AND event_time >= toDateTime(1694531196) AND event_time <= toDateTime(1694534796)\n  AND( ('1,2,3,4' = '1,2,3,4' AND type != 'QueryStart') OR ('1,2,3,4' != '1,2,3,4' AND type IN (1,2,3,4)))\n  AND type NOT IN ('QueryStart','QueryFinish')\n   \n   \n   \n   \nGROUP BY\n    query,\n    type\nORDER BY count DESC\nLIMIT 30\nSETTINGS skip_unavailable_shards=1", "refId": "A", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}], "title": "Top failed queries by user: $user; query kind: $query_kind", "transform": "table", "type": "table"}, {"collapsed": false, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 23, "panels": [], "repeat": null, "title": "Request charts", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 19}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "expr": "", "extrapolate": false, "format": "time_series", "formattedQuery": "<font color=\"darkcyan\">$rate</font>(<font color=\"navajowhite\">count</font>() c)<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">cluster('all-sharded',system.query_log)</font><br /><font color=\"darkorange\">where</font>  <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />    <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />    <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_kind'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,  <font color=\"lightgreen\">'$query_kind'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))", "interval": "", "intervalFactor": 2, "query": "$rate(count() c)\nFROM cluster('all-sharded',system.query_log)\nWHERE $timeFilter\n    AND( ('$type' = '1,2,3,4' AND type != 'QueryStart') OR ('$type' != '1,2,3,4' AND type IN ($type)))\n    $conditionalTest(AND query_kind IN ($query_kind), $query_kind)\n    $conditionalTest(AND initial_user IN ($user), $user)\n    $conditionalTest(AND query_duration_ms >= $min_duration_ms,$min_duration_ms)\n    $conditionalTest(AND query_duration_ms <= $max_duration_ms,$max_duration_ms)\n", "rawQuery": "SELECT t, c/(t/1000 - lagInFrame(t/1000,1,0) OVER ()) cRate FROM ( SELECT (intDiv(toUInt32(event_time), 4) * 4) * 1000 AS t, count() c FROM cluster('all-sharded',system.query_log)\nWHERE event_date >= toDate(1694531229) AND event_date <= toDate(1694534829) AND event_time >= toDateTime(1694531229) AND event_time <= toDateTime(1694534829) AND event_date >= toDate(1694531229) AND event_date <= toDate(1694534829) AND event_time >= toDateTime(1694531229) AND event_time <= toDateTime(1694534829)\n    AND( ('1,2,3,4' = '1,2,3,4' AND type != 'QueryStart') OR ('1,2,3,4' != '1,2,3,4' AND type IN (1,2,3,4)))\n     \n     \n     \n      GROUP BY t ORDER BY t)", "refId": "A", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Reqs/s  type: $type; user: $user; query kind: $query_kind", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 19}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/insert.+/", "yaxis": 2}, {"alias": "select_quantile_50", "color": "#FADE2A"}, {"alias": "select_quantile_90", "color": "#73BF69"}, {"alias": "insert_quantile_50", "color": "#FA6400"}, {"alias": "insert_quantile_90", "color": "#C4162A"}], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "expr": "", "extrapolate": false, "format": "time_series", "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />    <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />    <font color=\"navajowhite\">avg</font>(query_duration_ms)  select_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">cluster('all-sharded',system.query_log)</font><br /><font color=\"darkorange\">WHERE</font><br />    <font color=\"darkcyan\">$timeFilter</font><br />    <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br />    <font color=\"yellow\">and</font> positionCaseInsensitive(query,  <font color=\"lightgreen\">'select'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />    <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t", "interval": "", "intervalFactor": 2, "query": "SELECT\n    $timeSeries as t,\n    quantile(0.5)(query_duration_ms)  select_quantile_50,\n    quantile(0.9)(query_duration_ms)  select_quantile_90\nFROM cluster('all-sharded',system.query_log)\nWHERE\n    $timeFilter\n    AND type != 1\n    AND query_kind ='Select'\n    AND query_duration_ms > 0\n    $conditionalTest(AND initial_user IN ($user), $user)\n    $conditionalTest(AND query_duration_ms <= $max_duration_ms,$max_duration_ms)\nGROUP BY t\nORDER BY t", "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 4) * 4) * 1000 as t,\n    quantile(0.5)(query_duration_ms)  select_quantile_50,\n    quantile(0.9)(query_duration_ms)  select_quantile_90\nFROM cluster('all-sharded',system.query_log)\nWHERE\n    event_date >= toDate(1694526375) AND event_date <= toDate(1694529975) AND event_time >= toDateTime(1694526375) AND event_time <= toDateTime(1694529975)\n    AND type != 1\n    AND query_kind ='Select'\n    AND query_duration_ms > 0\n     \n     \nGROUP BY t\nORDER BY t", "refId": "A", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}, {"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "expr": "", "extrapolate": false, "format": "time_series", "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />    <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />    <font color=\"navajowhite\">avg</font>(query_duration_ms) insert_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">cluster('all-sharded',system.query_log)</font><br /><font color=\"darkorange\">WHERE</font><br />    <font color=\"darkcyan\">$timeFilter</font><br />    <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br /><font color=\"yellow\">and</font> positionCaseInsensitive(query,  <font color=\"lightgreen\">'insert into'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />    <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t", "intervalFactor": 2, "query": "SELECT\n    $timeSeries as t,\n    quantile(0.5)(query_duration_ms) insert_quantile_50,\n    quantile(0.9)(query_duration_ms) insert_quantile_90\nFROM cluster('all-sharded',system.query_log)\nWHERE\n    $timeFilter\n    AND type = 2\n    AND query_kind = 'Insert'\n    AND query_duration_ms > 0\n    $conditionalTest(AND initial_user IN ($user), $user)\n    $conditionalTest(AND query_duration_ms <= $max_duration_ms,$max_duration_ms)\nGROUP BY t\nORDER BY t", "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 4) * 4) * 1000 as t,\n    quantile(0.5)(query_duration_ms) insert_quantile_50,\n    quantile(0.9)(query_duration_ms) insert_quantile_90\nFROM cluster('all-sharded',system.query_log)\nWHERE\n    event_date >= toDate(1694526375) AND event_date <= toDate(1694529975) AND event_time >= toDateTime(1694526375) AND event_time <= toDateTime(1694529975)\n    AND type = 2\n    AND query_kind = 'Insert'\n    AND query_duration_ms > 0\n     \n     \nGROUP BY t\nORDER BY t", "refId": "B", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Query duration by type: $type; user: $user", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 24, "panels": [], "repeat": null, "title": "Query log table", "type": "row"}, {"columns": [], "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "fieldConfig": {"defaults": {"custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "result"}, "properties": [{"id": "custom.width", "value": 106}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "memory"}, "properties": [{"id": "custom.width", "value": 108}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "duration"}, "properties": [{"id": "custom.width", "value": 117}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user"}, "properties": [{"id": "custom.width", "value": 114}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 149}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "host"}, "properties": [{"id": "custom.width", "value": 223}]}]}, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 21, "links": [], "options": {"showHeader": true, "sortBy": []}, "pageSize": null, "pluginVersion": "7.5.17", "scroll": true, "showHeader": true, "sort": {"col": 2, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "pattern": "duration", "thresholds": [], "type": "number", "unit": "ms"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "memory", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"database": "system", "dateColDataType": "event_date", "dateLoading": false, "dateTimeColDataType": "event_time", "dateTimeType": "DATETIME", "datetimeLoading": false, "extrapolate": false, "format": "time_series", "interval": "", "intervalFactor": 1, "query": "SELECT\n    event_time,\n    hostName() host,\n    user,\n    query_duration_ms duration,\n    memory_usage memory,\n    if(exception!='', 'fail', 'success') result,\n    normalizeQuery(query) query\nFROM cluster('all-sharded',system.query_log)\nWHERE $timeFilter\n    AND type in ($type)\n    $conditionalTest(AND query_kind IN ($query_kind), $query_kind)\n    $conditionalTest(AND initial_user IN ($user), $user)\n    $conditionalTest(AND query_duration_ms >= $min_duration_ms,$min_duration_ms)\n    $conditionalTest(AND query_duration_ms <= $max_duration_ms,$max_duration_ms)\nORDER BY event_time DESC\nLIMIT 1000", "rawQuery": "SELECT\n    event_time,\n    hostName() host,\n    user,\n    query_duration_ms duration,\n    memory_usage memory,\n    if(exception!='', 'fail', 'success') result,\n    normalizeQuery(query) query\nFROM cluster('all-sharded',system.query_log)\nWHERE event_date >= toDate(1694531300) AND event_date <= toDate(1694534900) AND event_time >= toDateTime(1694531300) AND event_time <= toDateTime(1694534900)\n    AND type in (1,2,3,4)\n     \n     \n     \n     \nORDER BY event_time DESC\nLIMIT 1000", "refId": "A", "resultFormat": "time_series", "round": "0s", "skip_comments": true, "table": "query_log", "tableLoading": false}], "title": "Query log by type: $type; user: $user; query kind: $query_kind", "transform": "timeseries_to_columns", "transformations": [{"id": "prepareTimeSeries", "options": {}}], "type": "table"}], "refresh": "1m", "schemaVersion": 27, "style": "dark", "tags": ["Altinity", "clickhouse", "queries", "performance"], "templating": {"list": [{"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "includeAll": false, "multi": false, "name": "ds_prometheus", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "k8s-test-test-001", "value": "k8s-test-test-001"}, "description": null, "error": null, "hide": 2, "includeAll": false, "label": "K8S ClickHouse installation", "multi": false, "name": "db", "options": [], "query": "vertamedia-clickhouse-datasource", "refresh": 1, "regex": "k8s-$exported_namespace-$chi", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\"}, exported_namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "K8S Namespace", "multi": false, "name": "exported_namespace", "options": [], "query": {"query": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\"}, exported_namespace)", "refId": "clickhouse-operator-prometheus-exported_namespace-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\", exported_namespace=\"$exported_namespace\"}, chi)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "K8S Clickhouse Installation", "multi": false, "name": "chi", "options": [], "query": {"query": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\",exported_namespace=\"$exported_namespace\"}, chi)", "refId": "clickhouse-operator-prometheus-chi-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {}, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "definition": "SELECT\n values[2] value,\n keys[1] text\nFROM (\n SELECT\n   arrayMap(x -> splitByString('\\' = ', x), splitByString(', \\'',extract(type,'Enum8\\(\\'(.+)\\)'))) AS enum_arr\n FROM system.columns\n WHERE database='system' and table='query_log' AND name='type'\n)\nARRAY JOIN enum_arr as keys, enum_arr as values", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "type", "multi": true, "name": "type", "options": [], "query": "SELECT\n values[2] value,\n keys[1] text\nFROM (\n SELECT\n   arrayMap(x -> splitByString('\\' = ', x), splitByString(', \\'',extract(type,'Enum8\\(\\'(.+)\\)'))) AS enum_arr\n FROM system.columns\n WHERE database='system' and table='query_log' AND name='type'\n)\nARRAY JOIN enum_arr as keys, enum_arr as values", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "tags": [], "text": "5", "value": "5"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "top elements", "multi": false, "name": "top", "options": [{"selected": true, "text": "5", "value": "5"}, {"selected": false, "text": "10", "value": "10"}, {"selected": false, "text": "15", "value": "15"}, {"selected": false, "text": "20", "value": "20"}, {"selected": false, "text": "25", "value": "25"}, {"selected": false, "text": "30", "value": "30"}], "query": "5,10,15,20,25,30", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"allValue": "", "current": {}, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "definition": "SELECT DISTINCT initial_user FROM cluster('all-sharded',system.query_log) WHERE event_date BETWEEN toDate($__from / 1000) AND toDate($__to / 1000) AND event_time BETWEEN toDateTime($__from / 1000) AND  toDateTime($__to / 1000) SETTINGS skip_unavailable_shards=1", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "initial user", "multi": true, "name": "user", "options": [], "query": "SELECT DISTINCT initial_user FROM cluster('all-sharded',system.query_log) WHERE event_date BETWEEN toDate($__from / 1000) AND toDate($__to / 1000) AND event_time BETWEEN toDateTime($__from / 1000) AND  toDateTime($__to / 1000) SETTINGS skip_unavailable_shards=1", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {}, "datasource": {"type": "vertamedia-clickhouse-datasource", "uid": "${db}"}, "definition": "SELECT DISTINCT query_kind FROM cluster('all-sharded',system.query_log) WHERE event_date BETWEEN toDate($__from / 1000) AND toDate($__to / 1000) AND event_time BETWEEN toDateTime($__from / 1000) AND  toDateTime($__to / 1000) SETTINGS skip_unavailable_shards=1", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "query_kind", "multi": true, "name": "query_kind", "options": [], "query": "SELECT DISTINCT query_kind FROM cluster('all-sharded',system.query_log) WHERE event_date BETWEEN toDate($__from / 1000) AND toDate($__to / 1000) AND event_time BETWEEN toDateTime($__from / 1000) AND  toDateTime($__to / 1000) SETTINGS skip_unavailable_shards=1", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "", "value": ""}, "description": null, "error": null, "hide": 0, "label": null, "name": "min_duration_ms", "options": [{"selected": false, "text": "0", "value": "0"}], "query": "", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": true, "text": "", "value": ""}, "description": null, "error": null, "hide": 0, "label": null, "name": "max_duration_ms", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "ClickHouse in Kubernetes Queries", "uid": "clickhouse-queries", "version": 20230912}