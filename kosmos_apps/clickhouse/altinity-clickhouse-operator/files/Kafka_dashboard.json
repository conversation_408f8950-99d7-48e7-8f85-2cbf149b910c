{"__inputs": [{"name": "DS_PROMETHEUS", "label": "clickhouse-operator-prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.5.17"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "enable": true, "expr": "ALERTS{app=~\"clickhouse-operator|zookeeper\"}", "hide": false, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "prometheus alerts", "showIn": 0, "step": "30s", "tagKeys": "chi,pod_name,hostname,exported_namespace,namespace", "tags": [], "textFormat": "{{alertstate}}", "titleFormat": "{{alertname}}", "type": "tags"}, {"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Alitinity Clickhouse Operator metrics exported by Monitoring Agent", "editable": true, "gnetId": 882, "graphTooltip": 1, "id": null, "iteration": 1685040968955, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of Kafka active consumers", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 0}, "hiddenSeries": false, "id": 8, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaConsumers{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 1, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka active consumers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of active Kafka consumers which have some partitions assigned. ", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0}, "hiddenSeries": false, "id": 9, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaConsumersWithAssignment{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka consumers with assignments", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of consumers which are currently used by direct or background reads (MVs)", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0}, "hiddenSeries": false, "id": 12, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaConsumersInUse{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka consumers in use", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of partitions Kafka tables currently assigned to", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 7}, "hiddenSeries": false, "id": 16, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaAssignedPartitions{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka Assigned Partitions", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaConsumerErrors, \"Number of errors reported by librdkafka during polls\"\n\nKafkaProducerFlushes, \"Number of explicit flushes to Kafka producer\"\n", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 16, "x": 8, "y": 7}, "hiddenSeries": false, "id": 24, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaConsumerErrors{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka consumer Errors", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of partition assignments (the first stage of consumer group rebalance)", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 14}, "hiddenSeries": false, "id": 17, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRebalanceAssignments{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka rebalance assignments", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of partition revocations (the first stage of consumer group rebalance)", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 14}, "hiddenSeries": false, "id": 29, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRebalanceRevocations{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka rebalance revocations", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of partition relabance errors (the first stage of consumer group rebalance)", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 14}, "hiddenSeries": false, "id": 30, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRebalanceRevocations{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}, {"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRebalanceAssignments{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}, {"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRebalanceErrors{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka rebalance  errors", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaCommits, \"Number of successful commits of consumed offsets to Kafka (normally should be the same as KafkaBackgroundReads)\"", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 21}, "hiddenSeries": false, "id": 22, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaCommits{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON><PERSON> commits", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaCommitFailures, \"Number of failed commits of consumed offsets to Kafka (usually is a sign of some data duplication)\"(due to rebalances / errors or similar reasons). Those rows will be consumed again after the rebalance.", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 21}, "hiddenSeries": false, "id": 31, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaCommitFailures{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka commit failures", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of background reads currently working (populating materialized views from Kafka)", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 21}, "hiddenSeries": false, "id": 13, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaBackgroundReads{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ka<PERSON>ka Background Reads", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of kafka read messages from librdkafka to ClickHouse", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 28}, "hiddenSeries": false, "id": 10, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaMessagesRead{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka messages read", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of Kafka messages polled  from librdkafka to ClickHouse", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 28}, "hiddenSeries": false, "id": 28, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaMessagesPolled{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka messages polled", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of Kafka messages failed from librdkafka to ClickHouse", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 28}, "hiddenSeries": false, "id": 27, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaMessagesFailed{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka messages failed", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "│ KafkaLibrdkafkaThreads       │     0 │ Number of active librdkafka threads                                                     │\n", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 35}, "hiddenSeries": false, "id": 19, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaLibrdkafkaThreads{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka LibrdKafka Threads", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of rows parsed from Kafka messages", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 35}, "hiddenSeries": false, "id": 20, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRowsRead{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka rows read", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of parsed rows which were later rejected (due to rebalances / errors or similar reasons). Those rows will be consumed again after the rebalance.", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 35}, "hiddenSeries": false, "id": 33, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRowsRejected{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka rows rejected", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaBackgroundReads, \"Number of background reads populating materialized views from Kafka since server start\"", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 42}, "hiddenSeries": false, "id": 32, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaBackgroundReads{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka background reads", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Number of currently running inserts to Kafka  ", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 49}, "hiddenSeries": false, "id": 15, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaWrites{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka Writes by producers (Current INSERTs)", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaWrites, \"Number of writes (inserts) to Kafka tables \"\n", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 49}, "hiddenSeries": false, "id": 23, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaWrites{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON><PERSON> writes (INSERTs) by producers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaProducerErrors, \"Number of errors during producing the messages to <PERSON><PERSON><PERSON>\"\n", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 49}, "hiddenSeries": false, "id": 34, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaProducerErrors{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka producer errors", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "Kafka Producers active", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 56}, "hiddenSeries": false, "id": 18, "isNew": true, "legend": {"avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:100", "alias": "/^absolute.+/", "color": "#F2495C"}, {"$$hashKey": "object:101", "alias": "/^relative.+/", "color": "#FADE2A"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "chi_clickhouse_metric_KafkaProducers{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka active producers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:54", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:55", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaMessagesProduced, \"Number of messages produced to Kafka\"\n", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 56}, "hiddenSeries": false, "id": 35, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaMessagesProduced{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka messages produced", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "description": "KafkaRowsWritten, \"Number of rows inserted into Kafka tables\"\n\n", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 56}, "hiddenSeries": false, "id": 26, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rate(chi_clickhouse_event_KafkaRowsWritten{exported_namespace=~\"$exported_namespace\",chi=~\"$chi\",hostname=~\"$hostname\"}[1m])", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kafka rows written by producers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:148", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "5m", "schemaVersion": 27, "style": "dark", "tags": ["Altinity", "clickhouse", "operator"], "templating": {"list": [{"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "includeAll": false, "multi": false, "name": "ds_prometheus", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\"}, exported_namespace)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "K8S Namespace", "multi": true, "name": "exported_namespace", "options": [], "query": {"query": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\"}, exported_namespace)", "refId": "clickhouse-operator-prometheus-exported_namespace-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\", exported_namespace=~\"$exported_namespace\"}, chi)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "K8S Clickhouse Installation", "multi": true, "name": "chi", "options": [], "query": {"query": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\", exported_namespace=~\"$exported_namespace\"}, chi)", "refId": "clickhouse-operator-prometheus-chi-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": {"type": "prometheus", "uid": "${ds_prometheus}"}, "definition": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\",exported_namespace=~\"$exported_namespace\",chi=~\"$chi\"}, hostname)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Server", "multi": true, "name": "hostname", "options": [], "query": {"query": "label_values({__name__ =~ \"chi_clickhouse_metric_Uptime|chi_clickhouse_metric_fetch_errors\",exported_namespace=~\"$exported_namespace\",chi=~\"$chi\"}, hostname)", "refId": "clickhouse-operator-prometheus-hostname-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Kafka Dashboard", "uid": "Y9Jtz7y4k", "version": 81}