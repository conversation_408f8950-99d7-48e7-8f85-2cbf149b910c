# Template Parameters:
#
# KIND=ClickHouseInstallationTemplate
# SINGULAR=clickhouseinstallationtemplate
# PLURAL=clickhouseinstallationtemplates
# SHORT=chit
# OPERATOR_VERSION=0.25.2
#
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: clickhouseinstallationtemplates.clickhouse.altinity.com
  labels:
    clickhouse.altinity.com/chop: 0.25.2
spec:
  group: clickhouse.altinity.com
  scope: Namespaced
  names:
    kind: ClickHouseInstallationTemplate
    singular: clickhouseinstallationtemplate
    plural: clickhouseinstallationtemplates
    shortNames:
      - chit
  versions:
    - name: v1
      served: true
      storage: true
      additionalPrinterColumns:
        - name: version
          type: string
          description: Operator version
          priority: 1 # show in wide view
          jsonPath: .status.chop-version
        - name: clusters
          type: integer
          description: Clusters count
          jsonPath: .status.clusters
        - name: shards
          type: integer
          description: Shards count
          priority: 1 # show in wide view
          jsonPath: .status.shards
        - name: hosts
          type: integer
          description: Hosts count
          jsonPath: .status.hosts
        - name: taskID
          type: string
          description: TaskID
          priority: 1 # show in wide view
          jsonPath: .status.taskID
        - name: status
          type: string
          description: Resource status
          jsonPath: .status.status
        - name: hosts-completed
          type: integer
          description: Completed hosts count
          jsonPath: .status.hostsCompleted
        - name: hosts-updated
          type: integer
          description: Updated hosts count
          priority: 1 # show in wide view
          jsonPath: .status.hostsUpdated
        - name: hosts-added
          type: integer
          description: Added hosts count
          priority: 1 # show in wide view
          jsonPath: .status.hostsAdded
        - name: hosts-deleted
          type: integer
          description: Hosts deleted count
          priority: 1 # show in wide view
          jsonPath: .status.hostsDeleted
        - name: endpoint
          type: string
          description: Client access endpoint
          priority: 1 # show in wide view
          jsonPath: .status.endpoint
        - name: age
          type: date
          description: Age of the resource
          # Displayed in all priorities
          jsonPath: .metadata.creationTimestamp
        - name: suspend
          type: string
          description: Suspend reconciliation
          # Displayed in all priorities
          jsonPath: .spec.suspend
      subresources:
        status: {}
      schema:
        openAPIV3Schema:
          description: "define a set of Kubernetes resources (StatefulSet, PVC, Service, ConfigMap) which describe behavior one or more clusters"
          type: object
          required:
            - spec
          properties:
            apiVersion:
              description: |
                APIVersion defines the versioned schema of this representation
                of an object. Servers should convert recognized schemas to the latest
                internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
              type: string
            kind:
              description: |
                Kind is a string value representing the REST resource this
                object represents. Servers may infer this from the endpoint the client
                submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
              type: string
            metadata:
              type: object
            status:
              type: object
              description: |
                Status contains many fields like a normalized configuration, clickhouse-operator version, current action and all applied action list, current taskID and all applied taskIDs and other
              properties:
                chop-version:
                  type: string
                  description: "Operator version"
                chop-commit:
                  type: string
                  description: "Operator git commit SHA"
                chop-date:
                  type: string
                  description: "Operator build date"
                chop-ip:
                  type: string
                  description: "IP address of the operator's pod which managed this resource"
                clusters:
                  type: integer
                  minimum: 0
                  description: "Clusters count"
                shards:
                  type: integer
                  minimum: 0
                  description: "Shards count"
                replicas:
                  type: integer
                  minimum: 0
                  description: "Replicas count"
                hosts:
                  type: integer
                  minimum: 0
                  description: "Hosts count"
                status:
                  type: string
                  description: "Status"
                taskID:
                  type: string
                  description: "Current task id"
                taskIDsStarted:
                  type: array
                  description: "Started task ids"
                  nullable: true
                  items:
                    type: string
                taskIDsCompleted:
                  type: array
                  description: "Completed task ids"
                  nullable: true
                  items:
                    type: string
                action:
                  type: string
                  description: "Action"
                actions:
                  type: array
                  description: "Actions"
                  nullable: true
                  items:
                    type: string
                error:
                  type: string
                  description: "Last error"
                errors:
                  type: array
                  description: "Errors"
                  nullable: true
                  items:
                    type: string
                hostsUnchanged:
                  type: integer
                  minimum: 0
                  description: "Unchanged Hosts count"
                hostsUpdated:
                  type: integer
                  minimum: 0
                  description: "Updated Hosts count"
                hostsAdded:
                  type: integer
                  minimum: 0
                  description: "Added Hosts count"
                hostsCompleted:
                  type: integer
                  minimum: 0
                  description: "Completed Hosts count"
                hostsDeleted:
                  type: integer
                  minimum: 0
                  description: "Deleted Hosts count"
                hostsDelete:
                  type: integer
                  minimum: 0
                  description: "About to delete Hosts count"
                pods:
                  type: array
                  description: "Pods"
                  nullable: true
                  items:
                    type: string
                pod-ips:
                  type: array
                  description: "Pod IPs"
                  nullable: true
                  items:
                    type: string
                fqdns:
                  type: array
                  description: "Pods FQDNs"
                  nullable: true
                  items:
                    type: string
                endpoint:
                  type: string
                  description: "Endpoint"
                endpoints:
                  type: array
                  description: "All endpoints"
                  nullable: true
                  items:
                    type: string
                generation:
                  type: integer
                  minimum: 0
                  description: "Generation"
                normalized:
                  type: object
                  description: "Normalized resource requested"
                  x-kubernetes-preserve-unknown-fields: true
                normalizedCompleted:
                  type: object
                  description: "Normalized resource completed"
                  x-kubernetes-preserve-unknown-fields: true
                hostsWithTablesCreated:
                  type: array
                  description: "List of hosts with tables created by the operator"
                  nullable: true
                  items:
                    type: string
                hostsWithReplicaCaughtUp:
                  type: array
                  description: "List of hosts with replica caught up"
                  nullable: true
                  items:
                    type: string
                usedTemplates:
                  type: array
                  description: "List of templates used to build this CHI"
                  nullable: true
                  x-kubernetes-preserve-unknown-fields: true
                  items:
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
            spec:
              type: object
              # x-kubernetes-preserve-unknown-fields: true
              description: |
                Specification of the desired behavior of one or more ClickHouse clusters
                More info: https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md
              properties:
                taskID:
                  type: string
                  description: |
                    Allows to define custom taskID for CHI update and watch status of this update execution.
                    Displayed in all .status.taskID* fields.
                    By default (if not filled) every update of CHI manifest will generate random taskID
                stop: &TypeStringBool
                  type: string
                  description: |
                    Allows to stop all ClickHouse clusters defined in a CHI.
                    Works as the following:
                     - When `stop` is `1` operator sets `Replicas: 0` in each StatefulSet. Thie leads to having all `Pods` and `Service` deleted. All PVCs are kept intact.
                     - When `stop` is `0` operator sets `Replicas: 1` and `Pod`s and `Service`s will created again and all retained PVCs will be attached to `Pod`s.
                  enum:
                    # List StringBoolXXX constants from model
                    - ""
                    - "0"
                    - "1"
                    - "False"
                    - "false"
                    - "True"
                    - "true"
                    - "No"
                    - "no"
                    - "Yes"
                    - "yes"
                    - "Off"
                    - "off"
                    - "On"
                    - "on"
                    - "Disable"
                    - "disable"
                    - "Enable"
                    - "enable"
                    - "Disabled"
                    - "disabled"
                    - "Enabled"
                    - "enabled"
                restart:
                  type: string
                  description: |
                    In case 'RollingUpdate' specified, the operator will always restart ClickHouse pods during reconcile.
                    This options is used in rare cases when force restart is required and is typically removed after the use in order to avoid unneeded restarts.
                  enum:
                    - ""
                    - "RollingUpdate"
                suspend:
                  !!merge <<: *TypeStringBool
                  description: |
                    Suspend reconciliation of resources managed by a ClickHouse Installation.
                    Works as the following:
                     - When `suspend` is `true` operator stops reconciling all resources.
                     - When `suspend` is `false` or not set, operator reconciles all resources.
                troubleshoot:
                  !!merge <<: *TypeStringBool
                  description: |
                    Allows to troubleshoot Pods during CrashLoopBack state.
                    This may happen when wrong configuration applied, in this case `clickhouse-server` wouldn't start.
                    Command within ClickHouse container is modified with `sleep` in order to avoid quick restarts
                    and give time to troubleshoot via CLI.
                    Liveness and Readiness probes are disabled as well.
                namespaceDomainPattern:
                  type: string
                  description: |
                    Custom domain pattern which will be used for DNS names of `Service` or `Pod`.
                    Typical use scenario - custom cluster domain in Kubernetes cluster
                    Example: %s.svc.my.test
                templating:
                  type: object
                  # nullable: true
                  description: |
                    Optional, applicable inside ClickHouseInstallationTemplate only.
                    Defines current ClickHouseInstallationTemplate application options to target ClickHouseInstallation(s)."
                  properties:
                    policy:
                      type: string
                      description: |
                        When defined as `auto` inside ClickhouseInstallationTemplate, this ClickhouseInstallationTemplate
                        will be auto-added into ClickHouseInstallation, selectable by `chiSelector`.
                        Default value is `manual`, meaning ClickHouseInstallation should request this ClickhouseInstallationTemplate explicitly.
                      enum:
                        - ""
                        - "auto"
                        - "manual"
                    chiSelector:
                      type: object
                      description: "Optional, defines selector for ClickHouseInstallation(s) to be templated with ClickhouseInstallationTemplate"
                      # nullable: true
                      x-kubernetes-preserve-unknown-fields: true
                reconciling:
                  type: object
                  description: "Optional, allows tuning reconciling cycle for ClickhouseInstallation from clickhouse-operator side"
                  # nullable: true
                  properties:
                    policy:
                      type: string
                      description: |
                        DISCUSSED TO BE DEPRECATED
                        Syntax sugar
                        Overrides all three 'reconcile.host.wait.{exclude, queries, include}' values from the operator's config
                        Possible values:
                         - wait - should wait to exclude host, complete queries and include host back into the cluster
                         - nowait - should NOT wait to exclude host, complete queries and include host back into the cluster
                      enum:
                        - ""
                        - "wait"
                        - "nowait"
                    configMapPropagationTimeout:
                      type: integer
                      description: |
                        Timeout in seconds for `clickhouse-operator` to wait for modified `ConfigMap` to propagate into the `Pod`
                        More details: https://kubernetes.io/docs/concepts/configuration/configmap/#mounted-configmaps-are-updated-automatically
                      minimum: 0
                      maximum: 3600
                    cleanup:
                      type: object
                      description: "Optional, defines behavior for cleanup Kubernetes resources during reconcile cycle"
                      # nullable: true
                      properties:
                        unknownObjects:
                          type: object
                          description: |
                            Describes what clickhouse-operator should do with found Kubernetes resources which should be managed by clickhouse-operator,
                            but do not have `ownerReference` to any currently managed `ClickHouseInstallation` resource.
                            Default behavior is `Delete`"
                          # nullable: true
                          properties:
                            statefulSet: &TypeObjectsCleanup
                              type: string
                              description: "Behavior policy for unknown StatefulSet, `Delete` by default"
                              enum:
                                # List ObjectsCleanupXXX constants from model
                                - ""
                                - "Retain"
                                - "Delete"
                            pvc:
                              type: string
                              !!merge <<: *TypeObjectsCleanup
                              description: "Behavior policy for unknown PVC, `Delete` by default"
                            configMap:
                              !!merge <<: *TypeObjectsCleanup
                              description: "Behavior policy for unknown ConfigMap, `Delete` by default"
                            service:
                              !!merge <<: *TypeObjectsCleanup
                              description: "Behavior policy for unknown Service, `Delete` by default"
                        reconcileFailedObjects:
                          type: object
                          description: |
                            Describes what clickhouse-operator should do with Kubernetes resources which are failed during reconcile.
                            Default behavior is `Retain`"
                          # nullable: true
                          properties:
                            statefulSet:
                              !!merge <<: *TypeObjectsCleanup
                              description: "Behavior policy for failed StatefulSet, `Retain` by default"
                            pvc:
                              !!merge <<: *TypeObjectsCleanup
                              description: "Behavior policy for failed PVC, `Retain` by default"
                            configMap:
                              !!merge <<: *TypeObjectsCleanup
                              description: "Behavior policy for failed ConfigMap, `Retain` by default"
                            service:
                              !!merge <<: *TypeObjectsCleanup
                              description: "Behavior policy for failed Service, `Retain` by default"
                    runtime:
                      type: object
                      description: "runtime parameters for clickhouse-operator process which are used during reconcile cycle"
                      properties:
                        reconcileShardsThreadsNumber:
                          type: integer
                          minimum: 1
                          maximum: 65535
                          description: "How many goroutines will be used to reconcile shards of a cluster in parallel, 1 by default"
                        reconcileShardsMaxConcurrencyPercent:
                          type: integer
                          minimum: 0
                          maximum: 100
                          description: "The maximum percentage of cluster shards that may be reconciled in parallel, 50 percent by default."
                    macros:
                      type: object
                      description: "macros parameters"
                      properties:
                        sections:
                          type: object
                          description: "sections behaviour for macros"
                          properties:
                            users:
                              type: object
                              description: "sections behaviour for macros on users"
                              properties:
                                enabled:
                                  !!merge <<: *TypeStringBool
                                  description: "enabled or not"
                            profiles:
                              type: object
                              description: "sections behaviour for macros on profiles"
                              properties:
                                enabled:
                                  !!merge <<: *TypeStringBool
                                  description: "enabled or not"
                            quotas:
                              type: object
                              description: "sections behaviour for macros on quotas"
                              properties:
                                enabled:
                                  !!merge <<: *TypeStringBool
                                  description: "enabled or not"
                            settings:
                              type: object
                              description: "sections behaviour for macros on settings"
                              properties:
                                enabled:
                                  !!merge <<: *TypeStringBool
                                  description: "enabled or not"
                            files:
                              type: object
                              description: "sections behaviour for macros on files"
                              properties:
                                enabled:
                                  !!merge <<: *TypeStringBool
                                  description: "enabled or not"
                defaults:
                  type: object
                  description: |
                    define default behavior for whole ClickHouseInstallation, some behavior can be re-define on cluster, shard and replica level
                    More info: https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#specdefaults
                  # nullable: true
                  properties:
                    replicasUseFQDN:
                      !!merge <<: *TypeStringBool
                      description: |
                        define should replicas be specified by FQDN in `<host></host>`.
                        In case of "no" will use short hostname and clickhouse-server will use kubernetes default suffixes for DNS lookup
                        "no" by default
                    distributedDDL:
                      type: object
                      description: |
                        allows change `<yandex><distributed_ddl></distributed_ddl></yandex>` settings
                        More info: https://clickhouse.tech/docs/en/operations/server-configuration-parameters/settings/#server-settings-distributed_ddl
                      # nullable: true
                      properties:
                        profile:
                          type: string
                          description: "Settings from this profile will be used to execute DDL queries"
                    storageManagement:
                      type: object
                      description: default storage management options
                      properties:
                        provisioner: &TypePVCProvisioner
                          type: string
                          description: "defines `PVC` provisioner - be it StatefulSet or the Operator"
                          enum:
                            - ""
                            - "StatefulSet"
                            - "Operator"
                        reclaimPolicy: &TypePVCReclaimPolicy
                          type: string
                          description: |
                            defines behavior of `PVC` deletion.
                            `Delete` by default, if `Retain` specified then `PVC` will be kept when deleting StatefulSet
                          enum:
                            - ""
                            - "Retain"
                            - "Delete"
                    templates: &TypeTemplateNames
                      type: object
                      description: "optional, configuration of the templates names which will use for generate Kubernetes resources according to one or more ClickHouse clusters described in current ClickHouseInstallation (chi) resource"
                      # nullable: true
                      properties:
                        hostTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.hostTemplates, which will apply to configure every `clickhouse-server` instance during render ConfigMap resources which will mount into `Pod`"
                        podTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.podTemplates, allows customization each `Pod` resource during render and reconcile each StatefulSet.spec resource described in `chi.spec.configuration.clusters`"
                        dataVolumeClaimTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.volumeClaimTemplates, allows customization each `PVC` which will mount for clickhouse data directory in each `Pod` during render and reconcile every StatefulSet.spec resource described in `chi.spec.configuration.clusters`"
                        logVolumeClaimTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.volumeClaimTemplates, allows customization each `PVC` which will mount for clickhouse log directory in each `Pod` during render and reconcile every StatefulSet.spec resource described in `chi.spec.configuration.clusters`"
                        serviceTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.serviceTemplates. used for customization of the `Service` resource, created by `clickhouse-operator` to cover all clusters in whole `chi` resource"
                        serviceTemplates:
                          type: array
                          description: "optional, template names from chi.spec.templates.serviceTemplates. used for customization of the `Service` resources, created by `clickhouse-operator` to cover all clusters in whole `chi` resource"
                          nullable: true
                          items:
                            type: string
                        clusterServiceTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.serviceTemplates, allows customization for each `Service` resource which will created by `clickhouse-operator` which cover each clickhouse cluster described in `chi.spec.configuration.clusters`"
                        shardServiceTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.serviceTemplates, allows customization for each `Service` resource which will created by `clickhouse-operator` which cover each shard inside clickhouse cluster described in `chi.spec.configuration.clusters`"
                        replicaServiceTemplate:
                          type: string
                          description: "optional, template name from chi.spec.templates.serviceTemplates, allows customization for each `Service` resource which will created by `clickhouse-operator` which cover each replica inside each shard inside each clickhouse cluster described in `chi.spec.configuration.clusters`"
                        volumeClaimTemplate:
                          type: string
                          description: "optional, alias for dataVolumeClaimTemplate, template name from chi.spec.templates.volumeClaimTemplates, allows customization each `PVC` which will mount for clickhouse data directory in each `Pod` during render and reconcile every StatefulSet.spec resource described in `chi.spec.configuration.clusters`"
                configuration:
                  type: object
                  description: "allows configure multiple aspects and behavior for `clickhouse-server` instance and also allows describe multiple `clickhouse-server` clusters inside one `chi` resource"
                  # nullable: true
                  properties:
                    zookeeper: &TypeZookeeperConfig
                      type: object
                      description: |
                        allows configure <yandex><zookeeper>..</zookeeper></yandex> section in each `Pod` during generate `ConfigMap` which will mounted in `/etc/clickhouse-server/config.d/`
                        `clickhouse-operator` itself doesn't manage Zookeeper, please install Zookeeper separatelly look examples on https://github.com/Altinity/clickhouse-operator/tree/master/deploy/zookeeper/
                        currently, zookeeper (or clickhouse-keeper replacement) used for *ReplicatedMergeTree table engines and for `distributed_ddl`
                        More details: https://clickhouse.tech/docs/en/operations/server-configuration-parameters/settings/#server-settings_zookeeper
                      # nullable: true
                      properties:
                        nodes:
                          type: array
                          description: "describe every available zookeeper cluster node for interaction"
                          # nullable: true
                          items:
                            type: object
                            #required:
                            #  - host
                            properties:
                              host:
                                type: string
                                description: "dns name or ip address for Zookeeper node"
                              port:
                                type: integer
                                description: "TCP port which used to connect to Zookeeper node"
                                minimum: 0
                                maximum: 65535
                              secure:
                                !!merge <<: *TypeStringBool
                                description: "if a secure connection to Zookeeper is required"
                              availabilityZone:
                                type: string
                                description: "availability zone for Zookeeper node"
                        session_timeout_ms:
                          type: integer
                          description: "session timeout during connect to Zookeeper"
                        operation_timeout_ms:
                          type: integer
                          description: "one operation timeout during Zookeeper transactions"
                        root:
                          type: string
                          description: "optional root znode path inside zookeeper to store ClickHouse related data (replication queue or distributed DDL)"
                        identity:
                          type: string
                          description: "optional access credentials string with `user:password` format used when use digest authorization in Zookeeper"
                    users:
                      type: object
                      description: |
                        allows configure <yandex><users>..</users></yandex> section in each `Pod` during generate `ConfigMap` which will mount in `/etc/clickhouse-server/users.d/`
                        you can configure password hashed, authorization restrictions, database level security row filters etc.
                        More details: https://clickhouse.tech/docs/en/operations/settings/settings-users/
                        Your yaml code will convert to XML, see examples https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#specconfigurationusers

                        any key could contains `valueFrom` with `secretKeyRef` which allow pass password from kubernetes secrets
                        secret value will pass in `pod.spec.containers.evn`, and generate with from_env=XXX in XML in /etc/clickhouse-server/users.d/chop-generated-users.xml
                        it not allow automatically updates when updates `secret`, change spec.taskID for manually trigger reconcile cycle

                        look into https://github.com/Altinity/clickhouse-operator/blob/master/docs/chi-examples/05-settings-01-overview.yaml for examples

                        any key with prefix `k8s_secret_` shall has value with format namespace/secret/key or secret/key
                        in this case value from secret will write directly into XML tag during render *-usersd ConfigMap

                        any key with prefix `k8s_secret_env` shall has value with format namespace/secret/key or secret/key
                        in this case value from secret will write into environment variable and write to XML tag via from_env=XXX

                        look into https://github.com/Altinity/clickhouse-operator/blob/master/docs/chi-examples/05-settings-01-overview.yaml for examples
                      # nullable: true
                      x-kubernetes-preserve-unknown-fields: true
                    profiles:
                      type: object
                      description: |
                        allows configure <yandex><profiles>..</profiles></yandex> section in each `Pod` during generate `ConfigMap` which will mount in `/etc/clickhouse-server/users.d/`
                        you can configure any aspect of settings profile
                        More details: https://clickhouse.tech/docs/en/operations/settings/settings-profiles/
                        Your yaml code will convert to XML, see examples https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#specconfigurationprofiles
                      # nullable: true
                      x-kubernetes-preserve-unknown-fields: true
                    quotas:
                      type: object
                      description: |
                        allows configure <yandex><quotas>..</quotas></yandex> section in each `Pod` during generate `ConfigMap` which will mount in `/etc/clickhouse-server/users.d/`
                        you can configure any aspect of resource quotas
                        More details: https://clickhouse.tech/docs/en/operations/quotas/
                        Your yaml code will convert to XML, see examples https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#specconfigurationquotas
                      # nullable: true
                      x-kubernetes-preserve-unknown-fields: true
                    settings: &TypeSettings
                      type: object
                      description: |
                        allows configure `clickhouse-server` settings inside <yandex>...</yandex> tag in each `Pod` during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/`
                        More details: https://clickhouse.tech/docs/en/operations/settings/settings/
                        Your yaml code will convert to XML, see examples https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#specconfigurationsettings

                        any key could contains `valueFrom` with `secretKeyRef` which allow pass password from kubernetes secrets
                        look into https://github.com/Altinity/clickhouse-operator/blob/master/docs/chi-examples/05-settings-01-overview.yaml for examples

                        secret value will pass in `pod.spec.env`, and generate with from_env=XXX in XML in /etc/clickhouse-server/config.d/chop-generated-settings.xml
                        it not allow automatically updates when updates `secret`, change spec.taskID for manually trigger reconcile cycle
                      # nullable: true
                      x-kubernetes-preserve-unknown-fields: true
                    files: &TypeFiles
                      type: object
                      description: |
                        allows define content of any setting file inside each `Pod` during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/` or `/etc/clickhouse-server/conf.d/` or `/etc/clickhouse-server/users.d/`
                        every key in this object is the file name
                        every value in this object is the file content
                        you can use `!!binary |` and base64 for binary files, see details here https://yaml.org/type/binary.html
                        each key could contains prefix like {common}, {users}, {hosts} or config.d, users.d, conf.d, wrong prefixes will be ignored, subfolders also will be ignored
                        More details: https://github.com/Altinity/clickhouse-operator/blob/master/docs/chi-examples/05-settings-05-files-nested.yaml

                        any key could contains `valueFrom` with `secretKeyRef` which allow pass values from kubernetes secrets
                        secrets will mounted into pod as separate volume in /etc/clickhouse-server/secrets.d/
                        and will automatically update when update secret
                        it useful for pass SSL certificates from cert-manager or similar tool
                        look into https://github.com/Altinity/clickhouse-operator/blob/master/docs/chi-examples/05-settings-01-overview.yaml for examples
                      # nullable: true
                      x-kubernetes-preserve-unknown-fields: true
                    clusters:
                      type: array
                      description: |
                        describes clusters layout and allows change settings on cluster-level, shard-level and replica-level
                        every cluster is a set of StatefulSet, one StatefulSet contains only one Pod with `clickhouse-server`
                        all Pods will rendered in <remote_server> part of ClickHouse configs, mounted from ConfigMap as `/etc/clickhouse-server/config.d/chop-generated-remote_servers.xml`
                        Clusters will use for Distributed table engine, more details: https://clickhouse.tech/docs/en/engines/table-engines/special/distributed/
                        If `cluster` contains zookeeper settings (could be inherited from top `chi` level), when you can create *ReplicatedMergeTree tables
                      # nullable: true
                      items:
                        type: object
                        #required:
                        #  - name
                        properties:
                          name:
                            type: string
                            description: "cluster name, used to identify set of servers and wide used during generate names of related Kubernetes resources"
                            minLength: 1
                            # See namePartClusterMaxLen const
                            maxLength: 15
                            pattern: "^[a-zA-Z0-9-]{0,15}$"
                          zookeeper:
                            !!merge <<: *TypeZookeeperConfig
                            description: |
                              optional, allows configure <yandex><zookeeper>..</zookeeper></yandex> section in each `Pod` only in current ClickHouse cluster, during generate `ConfigMap` which will mounted in `/etc/clickhouse-server/config.d/`
                              override top-level `chi.spec.configuration.zookeeper` settings
                          settings:
                            !!merge <<: *TypeSettings
                            description: |
                              optional, allows configure `clickhouse-server` settings inside <yandex>...</yandex> tag in each `Pod` only in one cluster during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/`
                              override top-level `chi.spec.configuration.settings`
                              More details: https://clickhouse.tech/docs/en/operations/settings/settings/
                          files:
                            !!merge <<: *TypeFiles
                            description: |
                              optional, allows define content of any setting file inside each `Pod` on current cluster during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/` or `/etc/clickhouse-server/conf.d/` or `/etc/clickhouse-server/users.d/`
                              override top-level `chi.spec.configuration.files`
                          templates:
                            !!merge <<: *TypeTemplateNames
                            description: |
                              optional, configuration of the templates names which will use for generate Kubernetes resources according to selected cluster
                              override top-level `chi.spec.configuration.templates`
                          schemaPolicy:
                            type: object
                            description: |
                              describes how schema is propagated within replicas and shards
                            properties:
                              replica:
                                type: string
                                description: "how schema is propagated within a replica"
                                enum:
                                  # List SchemaPolicyReplicaXXX constants from model
                                  - ""
                                  - "None"
                                  - "All"
                              shard:
                                type: string
                                description: "how schema is propagated between shards"
                                enum:
                                  # List SchemaPolicyShardXXX constants from model
                                  - ""
                                  - "None"
                                  - "All"
                                  - "DistributedTablesOnly"
                          insecure:
                            !!merge <<: *TypeStringBool
                            description: optional, open insecure ports for cluster, defaults to "yes"
                          secure:
                            !!merge <<: *TypeStringBool
                            description: optional, open secure ports for cluster
                          secret:
                            type: object
                            description: "optional, shared secret value to secure cluster communications"
                            properties:
                              auto:
                                !!merge <<: *TypeStringBool
                                description: "Auto-generate shared secret value to secure cluster communications"
                              value:
                                description: "Cluster shared secret value in plain text"
                                type: string
                              valueFrom:
                                description: "Cluster shared secret source"
                                type: object
                                properties:
                                  secretKeyRef:
                                    description: |
                                      Selects a key of a secret in the clickhouse installation namespace.
                                      Should not be used if value is not empty.
                                    type: object
                                    properties:
                                      name:
                                        description: |
                                          Name of the referent. More info:
                                          https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      key:
                                        description: The key of the secret to select from. Must be a valid secret key.
                                        type: string
                                      optional:
                                        description: Specify whether the Secret or its key must be defined
                                        type: boolean
                                    required:
                                      - name
                                      - key
                          pdbMaxUnavailable:
                            type: integer
                            description: |
                              Pod eviction is allowed if at most "pdbMaxUnavailable" pods are unavailable after the eviction,
                              i.e. even in absence of the evicted pod. For example, one can prevent all voluntary evictions
                              by specifying 0. This is a mutually exclusive setting with "minAvailable".
                            minimum: 0
                            maximum: 65535
                          reconcile:
                            type: object
                            description: "allow tuning reconciling process"
                            properties:
                              runtime:
                                type: object
                                description: "runtime parameters for clickhouse-operator process which are used during reconcile cycle"
                                properties:
                                  reconcileShardsThreadsNumber:
                                    type: integer
                                    minimum: 1
                                    maximum: 65535
                                    description: "How many goroutines will be used to reconcile shards of a cluster in parallel, 1 by default"
                                  reconcileShardsMaxConcurrencyPercent:
                                    type: integer
                                    minimum: 0
                                    maximum: 100
                                    description: "The maximum percentage of cluster shards that may be reconciled in parallel, 50 percent by default."
                          layout:
                            type: object
                            description: |
                              describe current cluster layout, how much shards in cluster, how much replica in shard
                              allows override settings on each shard and replica separatelly
                            # nullable: true
                            properties:
                              shardsCount:
                                type: integer
                                description: |
                                  how much shards for current ClickHouse cluster will run in Kubernetes,
                                  each shard contains shared-nothing part of data and contains set of replicas,
                                  cluster contains 1 shard by default"
                              replicasCount:
                                type: integer
                                description: |
                                  how much replicas in each shards for current cluster will run in Kubernetes,
                                  each replica is a separate `StatefulSet` which contains only one `Pod` with `clickhouse-server` instance,
                                  every shard contains 1 replica by default"
                              shards:
                                type: array
                                description: |
                                  optional, allows override top-level `chi.spec.configuration`, cluster-level
                                  `chi.spec.configuration.clusters` settings for each shard separately,
                                  use it only if you fully understand what you do"
                                # nullable: true
                                items:
                                  type: object
                                  properties:
                                    name:
                                      type: string
                                      description: "optional, by default shard name is generated, but you can override it and setup custom name"
                                      minLength: 1
                                      # See namePartShardMaxLen const
                                      maxLength: 15
                                      pattern: "^[a-zA-Z0-9-]{0,15}$"
                                    definitionType:
                                      type: string
                                      description: "DEPRECATED - to be removed soon"
                                    weight:
                                      type: integer
                                      description: |
                                        optional, 1 by default, allows setup shard <weight> setting which will use during insert into tables with `Distributed` engine,
                                        will apply in <remote_servers> inside ConfigMap which will mount in /etc/clickhouse-server/config.d/chop-generated-remote_servers.xml
                                        More details: https://clickhouse.tech/docs/en/engines/table-engines/special/distributed/
                                    internalReplication:
                                      !!merge <<: *TypeStringBool
                                      description: |
                                        optional, `true` by default when `chi.spec.configuration.clusters[].layout.ReplicaCount` > 1 and 0 otherwise
                                        allows setup <internal_replication> setting which will use during insert into tables with `Distributed` engine for insert only in one live replica and other replicas will download inserted data during replication,
                                        will apply in <remote_servers> inside ConfigMap which will mount in /etc/clickhouse-server/config.d/chop-generated-remote_servers.xml
                                        More details: https://clickhouse.tech/docs/en/engines/table-engines/special/distributed/
                                    settings:
                                      !!merge <<: *TypeSettings
                                      description: |
                                        optional, allows configure `clickhouse-server` settings inside <yandex>...</yandex> tag in each `Pod` only in one shard during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/`
                                        override top-level `chi.spec.configuration.settings` and cluster-level `chi.spec.configuration.clusters.settings`
                                        More details: https://clickhouse.tech/docs/en/operations/settings/settings/
                                    files:
                                      !!merge <<: *TypeFiles
                                      description: |
                                        optional, allows define content of any setting file inside each `Pod` only in one shard during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/` or `/etc/clickhouse-server/conf.d/` or `/etc/clickhouse-server/users.d/`
                                        override top-level `chi.spec.configuration.files` and cluster-level `chi.spec.configuration.clusters.files`
                                    templates:
                                      !!merge <<: *TypeTemplateNames
                                      description: |
                                        optional, configuration of the templates names which will use for generate Kubernetes resources according to selected shard
                                        override top-level `chi.spec.configuration.templates` and cluster-level `chi.spec.configuration.clusters.templates`
                                    replicasCount:
                                      type: integer
                                      description: |
                                        optional, how much replicas in selected shard for selected ClickHouse cluster will run in Kubernetes, each replica is a separate `StatefulSet` which contains only one `Pod` with `clickhouse-server` instance,
                                        shard contains 1 replica by default
                                        override cluster-level `chi.spec.configuration.clusters.layout.replicasCount`
                                      minimum: 1
                                    replicas:
                                      type: array
                                      description: |
                                        optional, allows override behavior for selected replicas from cluster-level `chi.spec.configuration.clusters` and shard-level `chi.spec.configuration.clusters.layout.shards`
                                      # nullable: true
                                      items:
                                        # Host
                                        type: object
                                        properties:
                                          name:
                                            type: string
                                            description: "optional, by default replica name is generated, but you can override it and setup custom name"
                                            minLength: 1
                                            # See namePartReplicaMaxLen const
                                            maxLength: 15
                                            pattern: "^[a-zA-Z0-9-]{0,15}$"
                                          insecure:
                                            !!merge <<: *TypeStringBool
                                            description: |
                                              optional, open insecure ports for cluster, defaults to "yes"
                                          secure:
                                            !!merge <<: *TypeStringBool
                                            description: |
                                              optional, open secure ports
                                          tcpPort:
                                            type: integer
                                            description: |
                                              optional, setup `Pod.spec.containers.ports` with name `tcp` for selected replica, override `chi.spec.templates.hostTemplates.spec.tcpPort`
                                              allows connect to `clickhouse-server` via TCP Native protocol via kubernetes `Service`
                                            minimum: 1
                                            maximum: 65535
                                          tlsPort:
                                            type: integer
                                            minimum: 1
                                            maximum: 65535
                                          httpPort:
                                            type: integer
                                            description: |
                                              optional, setup `Pod.spec.containers.ports` with name `http` for selected replica, override `chi.spec.templates.hostTemplates.spec.httpPort`
                                              allows connect to `clickhouse-server` via HTTP protocol via kubernetes `Service`
                                            minimum: 1
                                            maximum: 65535
                                          httpsPort:
                                            type: integer
                                            minimum: 1
                                            maximum: 65535
                                          interserverHTTPPort:
                                            type: integer
                                            description: |
                                              optional, setup `Pod.spec.containers.ports` with name `interserver` for selected replica, override `chi.spec.templates.hostTemplates.spec.interserverHTTPPort`
                                              allows connect between replicas inside same shard during fetch replicated data parts HTTP protocol
                                            minimum: 1
                                            maximum: 65535
                                          settings:
                                            !!merge <<: *TypeSettings
                                            description: |
                                              optional, allows configure `clickhouse-server` settings inside <yandex>...</yandex> tag in `Pod` only in one replica during generate `ConfigMap` which will mount in `/etc/clickhouse-server/conf.d/`
                                              override top-level `chi.spec.configuration.settings`, cluster-level `chi.spec.configuration.clusters.settings` and shard-level `chi.spec.configuration.clusters.layout.shards.settings`
                                              More details: https://clickhouse.tech/docs/en/operations/settings/settings/
                                          files:
                                            !!merge <<: *TypeFiles
                                            description: |
                                              optional, allows define content of any setting file inside `Pod` only in one replica during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/` or `/etc/clickhouse-server/conf.d/` or `/etc/clickhouse-server/users.d/`
                                              override top-level `chi.spec.configuration.files`, cluster-level `chi.spec.configuration.clusters.files` and shard-level `chi.spec.configuration.clusters.layout.shards.files`
                                          templates:
                                            !!merge <<: *TypeTemplateNames
                                            description: |
                                              optional, configuration of the templates names which will use for generate Kubernetes resources according to selected replica
                                              override top-level `chi.spec.configuration.templates`, cluster-level `chi.spec.configuration.clusters.templates` and shard-level `chi.spec.configuration.clusters.layout.shards.templates`
                              replicas:
                                type: array
                                description: "optional, allows override top-level `chi.spec.configuration` and cluster-level `chi.spec.configuration.clusters` configuration for each replica and each shard relates to selected replica, use it only if you fully understand what you do"
                                # nullable: true
                                items:
                                  type: object
                                  properties:
                                    name:
                                      type: string
                                      description: "optional, by default replica name is generated, but you can override it and setup custom name"
                                      minLength: 1
                                      # See namePartShardMaxLen const
                                      maxLength: 15
                                      pattern: "^[a-zA-Z0-9-]{0,15}$"
                                    settings:
                                      !!merge <<: *TypeSettings
                                      description: |
                                        optional, allows configure `clickhouse-server` settings inside <yandex>...</yandex> tag in `Pod` only in one replica during generate `ConfigMap` which will mount in `/etc/clickhouse-server/conf.d/`
                                        override top-level `chi.spec.configuration.settings`, cluster-level `chi.spec.configuration.clusters.settings` and will ignore if shard-level `chi.spec.configuration.clusters.layout.shards` present
                                        More details: https://clickhouse.tech/docs/en/operations/settings/settings/
                                    files:
                                      !!merge <<: *TypeFiles
                                      description: |
                                        optional, allows define content of any setting file inside each `Pod` only in one replica during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/` or `/etc/clickhouse-server/conf.d/` or `/etc/clickhouse-server/users.d/`
                                        override top-level `chi.spec.configuration.files` and cluster-level `chi.spec.configuration.clusters.files`, will ignore if `chi.spec.configuration.clusters.layout.shards` presents
                                    templates:
                                      !!merge <<: *TypeTemplateNames
                                      description: |
                                        optional, configuration of the templates names which will use for generate Kubernetes resources according to selected replica
                                        override top-level `chi.spec.configuration.templates`, cluster-level `chi.spec.configuration.clusters.templates`
                                    shardsCount:
                                      type: integer
                                      description: "optional, count of shards related to current replica, you can override each shard behavior on low-level `chi.spec.configuration.clusters.layout.replicas.shards`"
                                      minimum: 1
                                    shards:
                                      type: array
                                      description: "optional, list of shards related to current replica, will ignore if `chi.spec.configuration.clusters.layout.shards` presents"
                                      # nullable: true
                                      items:
                                        # Host
                                        type: object
                                        properties:
                                          name:
                                            type: string
                                            description: "optional, by default shard name is generated, but you can override it and setup custom name"
                                            minLength: 1
                                            # See namePartReplicaMaxLen const
                                            maxLength: 15
                                            pattern: "^[a-zA-Z0-9-]{0,15}$"
                                          insecure:
                                            !!merge <<: *TypeStringBool
                                            description: |
                                              optional, open insecure ports for cluster, defaults to "yes"
                                          secure:
                                            !!merge <<: *TypeStringBool
                                            description: |
                                              optional, open secure ports
                                          tcpPort:
                                            type: integer
                                            description: |
                                              optional, setup `Pod.spec.containers.ports` with name `tcp` for selected shard, override `chi.spec.templates.hostTemplates.spec.tcpPort`
                                              allows connect to `clickhouse-server` via TCP Native protocol via kubernetes `Service`
                                            minimum: 1
                                            maximum: 65535
                                          tlsPort:
                                            type: integer
                                            minimum: 1
                                            maximum: 65535
                                          httpPort:
                                            type: integer
                                            description: |
                                              optional, setup `Pod.spec.containers.ports` with name `http` for selected shard, override `chi.spec.templates.hostTemplates.spec.httpPort`
                                              allows connect to `clickhouse-server` via HTTP protocol via kubernetes `Service`
                                            minimum: 1
                                            maximum: 65535
                                          httpsPort:
                                            type: integer
                                            minimum: 1
                                            maximum: 65535
                                          interserverHTTPPort:
                                            type: integer
                                            description: |
                                              optional, setup `Pod.spec.containers.ports` with name `interserver` for selected shard, override `chi.spec.templates.hostTemplates.spec.interserverHTTPPort`
                                              allows connect between replicas inside same shard during fetch replicated data parts HTTP protocol
                                            minimum: 1
                                            maximum: 65535
                                          settings:
                                            !!merge <<: *TypeSettings
                                            description: |
                                              optional, allows configure `clickhouse-server` settings inside <yandex>...</yandex> tag in `Pod` only in one shard related to current replica during generate `ConfigMap` which will mount in `/etc/clickhouse-server/conf.d/`
                                              override top-level `chi.spec.configuration.settings`, cluster-level `chi.spec.configuration.clusters.settings` and replica-level `chi.spec.configuration.clusters.layout.replicas.settings`
                                              More details: https://clickhouse.tech/docs/en/operations/settings/settings/
                                          files:
                                            !!merge <<: *TypeFiles
                                            description: |
                                              optional, allows define content of any setting file inside each `Pod` only in one shard related to current replica during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/` or `/etc/clickhouse-server/conf.d/` or `/etc/clickhouse-server/users.d/`
                                              override top-level `chi.spec.configuration.files` and cluster-level `chi.spec.configuration.clusters.files`, will ignore if `chi.spec.configuration.clusters.layout.shards` presents
                                          templates:
                                            !!merge <<: *TypeTemplateNames
                                            description: |
                                              optional, configuration of the templates names which will use for generate Kubernetes resources according to selected replica
                                              override top-level `chi.spec.configuration.templates`, cluster-level `chi.spec.configuration.clusters.templates`, replica-level `chi.spec.configuration.clusters.layout.replicas.templates`
                templates:
                  type: object
                  description: "allows define templates which will use for render Kubernetes resources like StatefulSet, ConfigMap, Service, PVC, by default, clickhouse-operator have own templates, but you can override it"
                  # nullable: true
                  properties:
                    hostTemplates:
                      type: array
                      description: "hostTemplate will use during apply to generate `clickhose-server` config files"
                      # nullable: true
                      items:
                        type: object
                        #required:
                        #  - name
                        properties:
                          name:
                            description: "template name, could use to link inside top-level `chi.spec.defaults.templates.hostTemplate`, cluster-level `chi.spec.configuration.clusters.templates.hostTemplate`, shard-level `chi.spec.configuration.clusters.layout.shards.temlates.hostTemplate`, replica-level `chi.spec.configuration.clusters.layout.replicas.templates.hostTemplate`"
                            type: string
                          portDistribution:
                            type: array
                            description: "define how will distribute numeric values of named ports in `Pod.spec.containers.ports` and clickhouse-server configs"
                            # nullable: true
                            items:
                              type: object
                              #required:
                              #  - type
                              properties:
                                type:
                                  type: string
                                  description: "type of distribution, when `Unspecified` (default value) then all listen ports on clickhouse-server configuration in all Pods will have the same value, when `ClusterScopeIndex` then ports will increment to offset from base value depends on shard and replica index inside cluster with combination of `chi.spec.templates.podTemlates.spec.HostNetwork` it allows setup ClickHouse cluster inside Kubernetes and provide access via external network bypass Kubernetes internal network"
                                  enum:
                                    # List PortDistributionXXX constants
                                    - ""
                                    - "Unspecified"
                                    - "ClusterScopeIndex"
                          spec:
                            # Host
                            type: object
                            properties:
                              name:
                                type: string
                                description: "by default, hostname will generate, but this allows define custom name for each `clickhuse-server`"
                                minLength: 1
                                # See namePartReplicaMaxLen const
                                maxLength: 15
                                pattern: "^[a-zA-Z0-9-]{0,15}$"
                              insecure:
                                !!merge <<: *TypeStringBool
                                description: |
                                  optional, open insecure ports for cluster, defaults to "yes"
                              secure:
                                !!merge <<: *TypeStringBool
                                description: |
                                  optional, open secure ports
                              tcpPort:
                                type: integer
                                description: |
                                  optional, setup `tcp_port` inside `clickhouse-server` settings for each Pod where current template will apply
                                  if specified, should have equal value with `chi.spec.templates.podTemplates.spec.containers.ports[name=tcp]`
                                  More info: https://clickhouse.tech/docs/en/interfaces/tcp/
                                minimum: 1
                                maximum: 65535
                              tlsPort:
                                type: integer
                                minimum: 1
                                maximum: 65535
                              httpPort:
                                type: integer
                                description: |
                                  optional, setup `http_port` inside `clickhouse-server` settings for each Pod where current template will apply
                                  if specified, should have equal value with `chi.spec.templates.podTemplates.spec.containers.ports[name=http]`
                                  More info: https://clickhouse.tech/docs/en/interfaces/http/
                                minimum: 1
                                maximum: 65535
                              httpsPort:
                                type: integer
                                minimum: 1
                                maximum: 65535
                              interserverHTTPPort:
                                type: integer
                                description: |
                                  optional, setup `interserver_http_port` inside `clickhouse-server` settings for each Pod where current template will apply
                                  if specified, should have equal value with `chi.spec.templates.podTemplates.spec.containers.ports[name=interserver]`
                                  More info: https://clickhouse.tech/docs/en/operations/server-configuration-parameters/settings/#interserver-http-port
                                minimum: 1
                                maximum: 65535
                              settings:
                                !!merge <<: *TypeSettings
                                description: |
                                  optional, allows configure `clickhouse-server` settings inside <yandex>...</yandex> tag in each `Pod` where this template will apply during generate `ConfigMap` which will mount in `/etc/clickhouse-server/conf.d/`
                                  More details: https://clickhouse.tech/docs/en/operations/settings/settings/
                              files:
                                !!merge <<: *TypeFiles
                                description: |
                                  optional, allows define content of any setting file inside each `Pod` where this template will apply during generate `ConfigMap` which will mount in `/etc/clickhouse-server/config.d/` or `/etc/clickhouse-server/conf.d/` or `/etc/clickhouse-server/users.d/`
                              templates:
                                !!merge <<: *TypeTemplateNames
                                description: "be careful, this part of CRD allows override template inside template, don't use it if you don't understand what you do"
                    podTemplates:
                      type: array
                      description: |
                        podTemplate will use during render `Pod` inside `StatefulSet.spec` and allows define rendered `Pod.spec`, pod scheduling distribution and pod zone
                        More information: https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#spectemplatespodtemplates
                      # nullable: true
                      items:
                        type: object
                        #required:
                        #  - name
                        properties:
                          name:
                            type: string
                            description: "template name, could use to link inside top-level `chi.spec.defaults.templates.podTemplate`, cluster-level `chi.spec.configuration.clusters.templates.podTemplate`, shard-level `chi.spec.configuration.clusters.layout.shards.temlates.podTemplate`, replica-level `chi.spec.configuration.clusters.layout.replicas.templates.podTemplate`"
                          generateName:
                            type: string
                            description: "allows define format for generated `Pod` name, look to https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#spectemplatesservicetemplates for details about available template variables"
                          zone:
                            type: object
                            description: "allows define custom zone name and will separate ClickHouse `Pods` between nodes, shortcut for `chi.spec.templates.podTemplates.spec.affinity.podAntiAffinity`"
                            #required:
                            #  - values
                            properties:
                              key:
                                type: string
                                description: "optional, if defined, allows select kubernetes nodes by label with `name` equal `key`"
                              values:
                                type: array
                                description: "optional, if defined, allows select kubernetes nodes by label with `value` in `values`"
                                # nullable: true
                                items:
                                  type: string
                          distribution:
                            type: string
                            description: "DEPRECATED, shortcut for `chi.spec.templates.podTemplates.spec.affinity.podAntiAffinity`"
                            enum:
                              - ""
                              - "Unspecified"
                              - "OnePerHost"
                          podDistribution:
                            type: array
                            description: "define ClickHouse Pod distribution policy between Kubernetes Nodes inside Shard, Replica, Namespace, CHI, another ClickHouse cluster"
                            # nullable: true
                            items:
                              type: object
                              #required:
                              #  - type
                              properties:
                                type:
                                  type: string
                                  description: "you can define multiple affinity policy types"
                                  enum:
                                    # List PodDistributionXXX constants
                                    - ""
                                    - "Unspecified"
                                    - "ClickHouseAntiAffinity"
                                    - "ShardAntiAffinity"
                                    - "ReplicaAntiAffinity"
                                    - "AnotherNamespaceAntiAffinity"
                                    - "AnotherClickHouseInstallationAntiAffinity"
                                    - "AnotherClusterAntiAffinity"
                                    - "MaxNumberPerNode"
                                    - "NamespaceAffinity"
                                    - "ClickHouseInstallationAffinity"
                                    - "ClusterAffinity"
                                    - "ShardAffinity"
                                    - "ReplicaAffinity"
                                    - "PreviousTailAffinity"
                                    - "CircularReplication"
                                scope:
                                  type: string
                                  description: "scope for apply each podDistribution"
                                  enum:
                                    # list PodDistributionScopeXXX constants
                                    - ""
                                    - "Unspecified"
                                    - "Shard"
                                    - "Replica"
                                    - "Cluster"
                                    - "ClickHouseInstallation"
                                    - "Namespace"
                                number:
                                  type: integer
                                  description: "define, how much ClickHouse Pods could be inside selected scope with selected distribution type"
                                  minimum: 0
                                  maximum: 65535
                                topologyKey:
                                  type: string
                                  description: |
                                    use for inter-pod affinity look to `pod.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.podAffinityTerm.topologyKey`,
                                    more info: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity"
                          metadata:
                            type: object
                            description: |
                              allows pass standard object's metadata from template to Pod
                              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
                            # nullable: true
                            x-kubernetes-preserve-unknown-fields: true
                          spec:
                            # TODO specify PodSpec
                            type: object
                            description: "allows define whole Pod.spec inside StaefulSet.spec, look to https://kubernetes.io/docs/concepts/workloads/pods/#pod-templates for details"
                            # nullable: true
                            x-kubernetes-preserve-unknown-fields: true
                    volumeClaimTemplates:
                      type: array
                      description: |
                        allows define template for rendering `PVC` kubernetes resource, which would use inside `Pod` for mount clickhouse `data`, clickhouse `logs` or something else
                      # nullable: true
                      items:
                        type: object
                        #required:
                        #  - name
                        #  - spec
                        properties:
                          name:
                            type: string
                            description: |
                              template name, could use to link inside
                              top-level `chi.spec.defaults.templates.dataVolumeClaimTemplate` or `chi.spec.defaults.templates.logVolumeClaimTemplate`,
                              cluster-level `chi.spec.configuration.clusters.templates.dataVolumeClaimTemplate` or `chi.spec.configuration.clusters.templates.logVolumeClaimTemplate`,
                              shard-level `chi.spec.configuration.clusters.layout.shards.temlates.dataVolumeClaimTemplate` or `chi.spec.configuration.clusters.layout.shards.temlates.logVolumeClaimTemplate`
                              replica-level `chi.spec.configuration.clusters.layout.replicas.templates.dataVolumeClaimTemplate` or `chi.spec.configuration.clusters.layout.replicas.templates.logVolumeClaimTemplate`
                          provisioner: *TypePVCProvisioner
                          reclaimPolicy: *TypePVCReclaimPolicy
                          metadata:
                            type: object
                            description: |
                              allows to pass standard object's metadata from template to PVC
                              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
                            # nullable: true
                            x-kubernetes-preserve-unknown-fields: true
                          spec:
                            type: object
                            description: |
                              allows define all aspects of `PVC` resource
                              More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes/#persistentvolumeclaims
                            # nullable: true
                            x-kubernetes-preserve-unknown-fields: true
                    serviceTemplates:
                      type: array
                      description: |
                        allows define template for rendering `Service` which would get endpoint from Pods which scoped chi-wide, cluster-wide, shard-wide, replica-wide level
                      # nullable: true
                      items:
                        type: object
                        #required:
                        #  - name
                        #  - spec
                        properties:
                          name:
                            type: string
                            description: |
                              template name, could use to link inside
                              chi-level `chi.spec.defaults.templates.serviceTemplate`
                              cluster-level `chi.spec.configuration.clusters.templates.clusterServiceTemplate`
                              shard-level `chi.spec.configuration.clusters.layout.shards.temlates.shardServiceTemplate`
                              replica-level `chi.spec.configuration.clusters.layout.replicas.templates.replicaServiceTemplate` or `chi.spec.configuration.clusters.layout.shards.replicas.replicaServiceTemplate`
                          generateName:
                            type: string
                            description: |
                              allows define format for generated `Service` name,
                              look to https://github.com/Altinity/clickhouse-operator/blob/master/docs/custom_resource_explained.md#spectemplatesservicetemplates
                              for details about available template variables"
                          metadata:
                            # TODO specify ObjectMeta
                            type: object
                            description: |
                              allows pass standard object's metadata from template to Service
                              Could be use for define specificly for Cloud Provider metadata which impact to behavior of service
                              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
                            # nullable: true
                            x-kubernetes-preserve-unknown-fields: true
                          spec:
                            # TODO specify ServiceSpec
                            type: object
                            description: |
                              describe behavior of generated Service
                              More info: https://kubernetes.io/docs/concepts/services-networking/service/
                            # nullable: true
                            x-kubernetes-preserve-unknown-fields: true
                useTemplates:
                  type: array
                  description: |
                    list of `ClickHouseInstallationTemplate` (chit) resource names which will merge with current `CHI`
                    manifest during render Kubernetes resources to create related ClickHouse clusters"
                  # nullable: true
                  items:
                    type: object
                    #required:
                    #  - name
                    properties:
                      name:
                        type: string
                        description: "name of `ClickHouseInstallationTemplate` (chit) resource"
                      namespace:
                        type: string
                        description: "Kubernetes namespace where need search `chit` resource, depending on `watchNamespaces` settings in `clichouse-operator`"
                      useType:
                        type: string
                        description: "optional, current strategy is only merge, and current `chi` settings have more priority than merged template `chit`"
                        enum:
                          # List useTypeXXX constants from model
                          - ""
                          - "merge"
