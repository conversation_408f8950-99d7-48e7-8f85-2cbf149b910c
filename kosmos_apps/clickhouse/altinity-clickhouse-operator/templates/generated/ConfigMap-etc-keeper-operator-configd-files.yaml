# Template Parameters:
#
# NAME=etc-keeper-operator-configd-files
# NAMESPACE=kube-system
# COMMENT=
#
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-keeper-configd-files" (include "altinity-clickhouse-operator.fullname" .) }}
  namespace: {{ include "altinity-clickhouse-operator.namespace" . }}
  labels: {{ include "altinity-clickhouse-operator.labels" . | nindent 4 }}
  annotations: {{ include "altinity-clickhouse-operator.annotations" . | nindent 4 }}
data: {{ include "altinity-clickhouse-operator.configmap-data" (list . .Values.configs.keeperConfigdFiles) | nindent 2 }}
