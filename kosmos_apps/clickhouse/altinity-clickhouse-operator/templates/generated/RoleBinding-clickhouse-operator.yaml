{{- if (and .Values.rbac.create .Values.rbac.namespaceScoped) -}}
# Specifies either
#   ClusterRoleBinding between ClusterRole and ServiceAccount.
# or
#   RoleBinding between Role and ServiceAccount.
# ClusterRoleBinding is namespace-less and must have unique name
# RoleBinding is namespace-bound
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "altinity-clickhouse-operator.fullname" . }}
  namespace: {{ include "altinity-clickhouse-operator.namespace" . }}
  labels: {{ include "altinity-clickhouse-operator.labels" . | nindent 4 }}
  annotations: {{ include "altinity-clickhouse-operator.annotations" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "altinity-clickhouse-operator.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "altinity-clickhouse-operator.serviceAccountName" . }}
    namespace: {{ include "altinity-clickhouse-operator.namespace" . }}
{{- end }}
