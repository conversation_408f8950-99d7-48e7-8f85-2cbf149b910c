# Template Parameters:
#
# NAMESPACE=kube-system
# COMMENT=
#
# Setup ClusterIP Service to provide monitoring metrics for Prometheus
# Service would be created in kubectl-specified namespace
# In order to get access outside of k8s it should be exposed as:
# kubectl --namespace prometheus port-forward service/prometheus 9090
# and point browser to localhost:9090
kind: Service
apiVersion: v1
metadata:
  name: {{ printf "%s-metrics" (include "altinity-clickhouse-operator.fullname" .) }}
  namespace: {{ include "altinity-clickhouse-operator.namespace" . }}
  labels: {{ include "altinity-clickhouse-operator.labels" . | nindent 4 }}
  annotations: {{ include "altinity-clickhouse-operator.annotations" . | nindent 4 }}
spec:
  ports:
    - port: 8888
      name: clickhouse-metrics
    - port: 9999
      name: operator-metrics
  selector: {{ include "altinity-clickhouse-operator.selectorLabels" . | nindent 4 }}
