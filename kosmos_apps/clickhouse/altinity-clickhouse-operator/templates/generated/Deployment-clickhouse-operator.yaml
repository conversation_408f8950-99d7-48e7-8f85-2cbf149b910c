# Template Parameters:
#
# NAMESPACE=kube-system
# COMMENT=
# OPERATOR_IMAGE=altinity/clickhouse-operator:0.25.2
# OPERATOR_IMAGE_PULL_POLICY=Always
# METRICS_EXPORTER_IMAGE=altinity/metrics-exporter:0.25.2
# METRICS_EXPORTER_IMAGE_PULL_POLICY=Always
#
# Setup Deployment for clickhouse-operator
# Deployment would be created in kubectl-specified namespace
kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ include "altinity-clickhouse-operator.fullname" . }}
  namespace: {{ include "altinity-clickhouse-operator.namespace" . }}
  labels: {{ include "altinity-clickhouse-operator.labels" . | nindent 4 }}
  annotations: {{ include "altinity-clickhouse-operator.annotations" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels: {{ include "altinity-clickhouse-operator.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels: {{ include "altinity-clickhouse-operator.labels" . | nindent 8 }}{{ if .Values.podLabels }}{{ toYaml .Values.podLabels | nindent 8 }}{{ end }}
      annotations:
        {{ if .Values.podAnnotations }}{{ toYaml .Values.podAnnotations | nindent 8 }}{{ end }}
        checksum/files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-clickhouse-operator-files.yaml") . | sha256sum }}
        checksum/confd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-clickhouse-operator-confd-files.yaml") . | sha256sum }}
        checksum/configd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-clickhouse-operator-configd-files.yaml") . | sha256sum }}
        checksum/templatesd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-clickhouse-operator-templatesd-files.yaml") . | sha256sum }}
        checksum/usersd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-clickhouse-operator-usersd-files.yaml") . | sha256sum }}
        checksum/keeper-confd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-keeper-operator-confd-files.yaml") . | sha256sum }}
        checksum/keeper-configd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-keeper-operator-configd-files.yaml") . | sha256sum }}
        checksum/keeper-templatesd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-keeper-operator-templatesd-files.yaml") . | sha256sum }}
        checksum/keeper-usersd-files: {{ include (print $.Template.BasePath "/generated/ConfigMap-etc-keeper-operator-usersd-files.yaml") . | sha256sum }}
    spec:
      serviceAccountName: {{ include "altinity-clickhouse-operator.serviceAccountName" . }}
      volumes:
        - name: etc-clickhouse-operator-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-files
        - name: etc-clickhouse-operator-confd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-confd-files
        - name: etc-clickhouse-operator-configd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-configd-files
        - name: etc-clickhouse-operator-templatesd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-templatesd-files
        - name: etc-clickhouse-operator-usersd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-usersd-files
        - name: etc-keeper-operator-confd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-keeper-confd-files
        - name: etc-keeper-operator-configd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-keeper-configd-files
        - name: etc-keeper-operator-templatesd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-keeper-templatesd-files
        - name: etc-keeper-operator-usersd-folder
          configMap:
            name: {{ include "altinity-clickhouse-operator.fullname" . }}-keeper-usersd-files
      containers:
        - name: {{ .Chart.Name }}
          image: {{ .Values.operator.image.repository }}:{{ include "altinity-clickhouse-operator.operator.tag" . }}
          imagePullPolicy: {{ .Values.operator.image.pullPolicy }}
          volumeMounts:
            - name: etc-clickhouse-operator-folder
              mountPath: /etc/clickhouse-operator
            - name: etc-clickhouse-operator-confd-folder
              mountPath: /etc/clickhouse-operator/chi/conf.d
            - name: etc-clickhouse-operator-configd-folder
              mountPath: /etc/clickhouse-operator/chi/config.d
            - name: etc-clickhouse-operator-templatesd-folder
              mountPath: /etc/clickhouse-operator/chi/templates.d
            - name: etc-clickhouse-operator-usersd-folder
              mountPath: /etc/clickhouse-operator/chi/users.d
            - name: etc-keeper-operator-confd-folder
              mountPath: /etc/clickhouse-operator/chk/conf.d
            - name: etc-keeper-operator-configd-folder
              mountPath: /etc/clickhouse-operator/chk/keeper_config.d
            - name: etc-keeper-operator-templatesd-folder
              mountPath: /etc/clickhouse-operator/chk/templates.d
            - name: etc-keeper-operator-usersd-folder
              mountPath: /etc/clickhouse-operator/chk/users.d
          env:
            # Pod-specific
            # spec.nodeName: ip-172-20-52-62.ec2.internal
            - name: OPERATOR_POD_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            # metadata.name: clickhouse-operator-6f87589dbb-ftcsf
            - name: OPERATOR_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            # metadata.namespace: kube-system
            - name: OPERATOR_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            # status.podIP: **********
            - name: OPERATOR_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            # spec.serviceAccount: clickhouse-operator
            # spec.serviceAccountName: clickhouse-operator
            - name: OPERATOR_POD_SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            # Container-specific
            - name: OPERATOR_CONTAINER_CPU_REQUEST
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: requests.cpu
            - name: OPERATOR_CONTAINER_CPU_LIMIT
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: limits.cpu
            - name: OPERATOR_CONTAINER_MEM_REQUEST
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: requests.memory
            - name: OPERATOR_CONTAINER_MEM_LIMIT
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: limits.memory
            {{ with .Values.operator.env }}{{ toYaml . | nindent 12 }}{{ end }}
          ports:
            - containerPort: 9999
              name: metrics
          resources: {{ toYaml .Values.operator.resources | nindent 12 }}
          securityContext: {{ toYaml .Values.operator.containerSecurityContext | nindent 12 }}
{{ if .Values.metrics.enabled }}
        - name: metrics-exporter
          image: {{ .Values.metrics.image.repository }}:{{ include "altinity-clickhouse-operator.metrics.tag" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy }}
          volumeMounts:
            - name: etc-clickhouse-operator-folder
              mountPath: /etc/clickhouse-operator
            - name: etc-clickhouse-operator-confd-folder
              mountPath: /etc/clickhouse-operator/chi/conf.d
            - name: etc-clickhouse-operator-configd-folder
              mountPath: /etc/clickhouse-operator/chi/config.d
            - name: etc-clickhouse-operator-templatesd-folder
              mountPath: /etc/clickhouse-operator/chi/templates.d
            - name: etc-clickhouse-operator-usersd-folder
              mountPath: /etc/clickhouse-operator/chi/users.d
            - name: etc-keeper-operator-confd-folder
              mountPath: /etc/clickhouse-operator/chk/conf.d
            - name: etc-keeper-operator-configd-folder
              mountPath: /etc/clickhouse-operator/chk/keeper_config.d
            - name: etc-keeper-operator-templatesd-folder
              mountPath: /etc/clickhouse-operator/chk/templates.d
            - name: etc-keeper-operator-usersd-folder
              mountPath: /etc/clickhouse-operator/chk/users.d
          env:
            # Pod-specific
            # spec.nodeName: ip-172-20-52-62.ec2.internal
            - name: OPERATOR_POD_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            # metadata.name: clickhouse-operator-6f87589dbb-ftcsf
            - name: OPERATOR_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            # metadata.namespace: kube-system
            - name: OPERATOR_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            # status.podIP: **********
            - name: OPERATOR_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            # spec.serviceAccount: clickhouse-operator
            # spec.serviceAccountName: clickhouse-operator
            - name: OPERATOR_POD_SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            # Container-specific
            - name: OPERATOR_CONTAINER_CPU_REQUEST
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: requests.cpu
            - name: OPERATOR_CONTAINER_CPU_LIMIT
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: limits.cpu
            - name: OPERATOR_CONTAINER_MEM_REQUEST
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: requests.memory
            - name: OPERATOR_CONTAINER_MEM_LIMIT
              valueFrom:
                resourceFieldRef:
                  containerName: {{ .Chart.Name }}
                  resource: limits.memory
            {{ with .Values.metrics.env }}{{ toYaml . | nindent 12 }}{{ end }}
          ports:
            - containerPort: 8888
              name: metrics
          resources: {{ toYaml .Values.metrics.resources | nindent 12 }}
          securityContext: {{ toYaml .Values.metrics.containerSecurityContext | nindent 12 }}
{{ end }}
      imagePullSecrets: {{ toYaml .Values.imagePullSecrets | nindent 8 }}
      nodeSelector: {{ toYaml .Values.nodeSelector | nindent 8 }}
      affinity: {{ toYaml .Values.affinity | nindent 8 }}
      tolerations: {{ toYaml .Values.tolerations | nindent 8 }}
      securityContext: {{ toYaml .Values.podSecurityContext | nindent 8 }}
      topologySpreadConstraints: {{ toYaml .Values.topologySpreadConstraints | nindent 8 }}
