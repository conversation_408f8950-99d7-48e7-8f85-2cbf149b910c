{{- if .Values.secret.create -}}
#
# Template parameters available:
#   NAMESPACE=kube-system
#   COMMENT=
#   OPERATOR_VERSION=0.25.2
#   CH_USERNAME_SECRET_PLAIN=clickhouse_operator
#   CH_PASSWORD_SECRET_PLAIN=clickhouse_operator_password
#
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "altinity-clickhouse-operator.fullname" . }}
  namespace: {{ include "altinity-clickhouse-operator.namespace" . }}
  labels: {{ include "altinity-clickhouse-operator.labels" . | nindent 4 }}
  annotations: {{ include "altinity-clickhouse-operator.annotations" . | nindent 4 }}
type: Opaque
data:
  username: {{ .Values.secret.username | b64enc }}
  password: {{ .Values.secret.password | b64enc }}
{{- end -}}
