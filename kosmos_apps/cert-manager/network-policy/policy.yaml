kind: Namespace
apiVersion: v1
metadata:
  name: kosmos-system-restricted
---
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: cert-manager-allow-egress
  namespace: kosmos-system-restricted
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/instance: cert-manager
  egress:
    - ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 443
  policyTypes:
    - Egress
---
kind: CiliumNetworkPolicy
apiVersion: cilium.io/v2
metadata:
  name: cert-manager-to-api-server-egress
spec:
  endpointSelector: {}
  egress:
    - toEntities:
        - kube-apiserver
    - toPorts:
        - ports:
            - port: "6443"
              protocol: TCP
