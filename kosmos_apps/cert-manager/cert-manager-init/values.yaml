image:
  repository: bitnami/kubectl
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

corpCA:
  # Enable it if you want to benefit from your corporation Signed CA TLS. You must provide CA cert, Certificate and its Key below
  enabled: false
  # Your corporation CA certificate file, in plain format
  caCrt: |

  # Your corporation CA signing certificate file, in plain format
  tlsCrt: |

  # Your corporation CA signing certificate key file, in plain format
  tlsKey: |

letsEncrypt:
  # Enable it of you want TLS certificates generation using cert-manager with LetsEncrypt provider
  # NOTE: LetsEncrypt provider must have been configurated on cert-manager chart
  enabled: false
  # Cloudflare API plain Token
  cloudflareApiToken: ""
  # Email that will receive LetsEncrypt notifications
  letsEncryptEmail: <EMAIL>
  # Email account which created token
  CloudflareEmail: <EMAIL>
  dns01:
    enabled: false
  http01:
    enabled: false

podSecurityContext: {}

securityContext:
  capabilities:
      drop:
        - ALL
  seccompProfile:
    type: "RuntimeDefault"
  allowPrivilegeEscalation: false
  runAsNonRoot: true
  runAsUser: 1000

resources:
  requests:
    cpu: 100m
    memory: 64Mi
  limits:
    cpu: 100m
    memory: 64Mi
