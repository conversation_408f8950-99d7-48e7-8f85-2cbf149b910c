{{ .Release.Name }} installed !

Release name: {{ .Release.Name }}

Release info:

  $ helm -n {{ .Release.Namespace }} status {{ .Release.Name }}
  $ helm -n {{ .Release.Namespace }} get all {{ .Release.Name }}

To get kosmos-ca-issuer clusterIssuer info:

  $ kubectl describe clusterissuers.cert-manager.io kosmos-ca-issuer

{{ if .Values.corpCA.enabled }}
To get corp-ca-issuer clusterIssuer info:

  $ kubectl describe clusterissuers.cert-manager.io corp-ca-issuer
{{ end }}

{{ if .Values.letsEncrypt.enabled }}
To get letsencrypt-staging or letsencrypt-prod clusterIssuer info:

  $ kubectl describe clusterissuers.cert-manager.io letsencrypt-staging
  $ kubectl describe clusterissuers.cert-manager.io letsencrypt-prod
{{ end }}