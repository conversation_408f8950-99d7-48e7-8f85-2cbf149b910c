{{- if .Values.corpCA.enabled }}
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: {{ include "cert-manager-init.kosmosIssuer.name" . }}
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  ca:
    secretName: {{ include "cert-manager-init.kosmosIssuer.secretName" . }}
{{- else }}
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: selfsigned-issuer
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  selfSigned: {}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: kosmos-selfsigned-ca
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  isCA: true
  commonName: kosmos-selfsigned-ca
  secretName: {{ include "cert-manager-init.kosmosIssuer.secretName" . }}
  privateKey:
    algorithm: ECDSA
    size: 256
  issuerRef:
    name: selfsigned-issuer
    kind: ClusterIssuer
    group: cert-manager.io
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: {{ include "cert-manager-init.kosmosIssuer.name" . }}
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  ca:
    secretName: {{ include "cert-manager-init.kosmosIssuer.secretName" . }}
{{- end }}
{{- if (and .Values.letsEncrypt.enabled .Values.letsEncrypt.dns01.enabled) }}
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  acme:
    # The ACME server URL
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: {{ .Values.letsEncrypt.letsEncryptEmail }}
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-staging
    # Enable the DNS-01 challenge provider
    solvers:
      - dns01:
          cloudflare:
            email: {{ .Values.letsEncrypt.CloudflareEmail }}
            apiTokenSecretRef:
              name: {{ include "cert-manager-init.letsEncrypt.secretName" . }}
              key: api-token
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  acme:
    # The ACME server URL
    server: https://acme-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: {{ .Values.letsEncrypt.letsEncryptEmail }}
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-prod
    # Enable the DNS-01 challenge provider
    solvers:
      - dns01:
          cloudflare:
            email: {{ .Values.letsEncrypt.CloudflareEmail }}
            apiTokenSecretRef:
              name: {{ include "cert-manager-init.letsEncrypt.secretName" . }}
              key: api-token
{{- end }}
{{- if (and .Values.letsEncrypt.enabled .Values.letsEncrypt.http01.enabled) }}
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  acme:
    # The ACME server URL
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: {{ .Values.letsEncrypt.letsEncryptEmail }}
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-staging
    # Enable the HTTP-01 challenge provider
    solvers:
      - http01:
          ingress:
            ingressClassName: nginx
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
spec:
  acme:
    # The ACME server URL
    server: https://acme-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: {{ .Values.letsEncrypt.letsEncryptEmail }}
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-prod
    # Enable the HTTP-01 challenge provider
    solvers:
      - http01:
          ingress:
            ingressClassName: nginx
{{- end }}
