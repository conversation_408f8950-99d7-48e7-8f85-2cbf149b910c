{{- if (and .Values.letsEncrypt.enabled .Values.letsEncrypt.dns01.enabled) -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "cert-manager-init.letsEncrypt.secretName" . }}
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
data:
  api-token: {{ required "letsEncrypt.cloudflareApiToken value must be defined if letsEncrypt is enabled" .Values.letsEncrypt.cloudflareApiToken | b64enc }}
type: Opaque
{{- end }}
{{- if .Values.corpCA.enabled -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "cert-manager-init.kosmosIssuer.secretName" . }}
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
data:
  ca.crt: {{ required "corpCA.caCrt content file must be provided if corpCA is enabled" .Values.corpCA.caCrt | b64enc }}
  tls.crt: {{ required "corpCA.tlsCrt content file must be provided if corpCA is enabled" .Values.corpCA.tlsCrt | b64enc }}
  tls.key: {{ required "corpCA.tlsKey content file must be provided if corpCA is enabled" .Values.corpCA.tlsKey | b64enc }}
type: kubernetes.io/tls
{{- end }}
