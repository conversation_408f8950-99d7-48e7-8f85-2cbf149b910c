---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "cert-manager-init.roleBindingName" . }}
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-5"
subjects:
- kind: ServiceAccount
  name: {{ include "cert-manager-init.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ include "cert-manager-init.roleName" . }}
  apiGroup: rbac.authorization.k8s.io
