---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "cert-manager-init.jobName" . }}
  labels:
    {{- include "cert-manager-init.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  template:
    metadata:
      name: {{ .Chart.Name }}
      labels:
        {{- include "cert-manager-init.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      serviceAccountName: {{ include "cert-manager-init.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "cert-manager-init.jobName" . }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command:
            - "bash"
            - "-c"
            - |
              clusterissuers=''
              certificates=''
              while [ "$clusterissuers" != 'CustomResourceDefinition' ] || [ "$certificates" != 'CustomResourceDefinition' ] ; do
                  sleep 2
                  echo 'Checking if Cert Manager CustomResourceDefinitions exist...'
                  clusterissuers=$(kubectl get --ignore-not-found customresourcedefinitions.apiextensions.k8s.io clusterissuers.cert-manager.io -o jsonpath={.kind})
                  certificates=$(kubectl get --ignore-not-found customresourcedefinitions.apiextensions.k8s.io certificates.cert-manager.io -o jsonpath={.kind})
              done
              echo 'CustomResourceDefinitions exist !'

              echo 'Checking if CustomResourceDefinitions are in Established status...'
              kubectl wait --for=condition=Established customresourcedefinitions.apiextensions.k8s.io certificates.cert-manager.io --timeout=60s
              kubectl wait --for=condition=Established customresourcedefinitions.apiextensions.k8s.io clusterissuers.cert-manager.io --timeout=60s

              echo 'Cert Manager is ready !'
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
