{{/*
Expand the name of the chart.
*/}}
{{- define "cert-manager-init.name" -}}
{{- default .Chart.Name | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "cert-manager-init.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "cert-manager-init.labels" -}}
helm.sh/chart: {{ include "cert-manager-init.chart" . }}
{{ include "cert-manager-init.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "cert-manager-init.selectorLabels" -}}
app.kubernetes.io/name: {{ include "cert-manager-init.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Define secret name if using Lets Encrypt with Cloudflare
*/}}
{{- define "cert-manager-init.letsEncrypt.secretName" -}}
cloudflare-api-token-secret
{{- end }}

{{/*
Define secret name if using corp provided CA
*/}}
{{- define "cert-manager-init.kosmosIssuer.name" -}}
kosmos-ca-issuer
{{- end }}

{{/*
Define tls secret name
*/}}
{{- define "cert-manager-init.kosmosIssuer.secretName" -}}
kosmos-ca-secret
{{- end }}

{{/*
Define job name
*/}}
{{- define "cert-manager-init.jobName" -}}
{{ include "cert-manager-init.name" . }}-job
{{- end }}

{{/*
Define service account name
*/}}
{{- define "cert-manager-init.serviceAccountName" -}}
cert-manager-init-sa
{{- end }}

{{/*
Define role name
*/}}
{{- define "cert-manager-init.roleName" -}}
cert-manager-init-role
{{- end }}


{{/*
Define role-binding name
*/}}
{{- define "cert-manager-init.roleBindingName" -}}
cert-manager-init-rb
{{- end }}
