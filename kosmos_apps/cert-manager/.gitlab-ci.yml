stages:
  - helm test
  - helm build
  - helmfile update
  - deploy kosmos-dev
  - deploy kosmos-int

include:
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helm/helm.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/helmfile/helmfile.gitlab-ci.yml'
  - project: 'athea/devops/athea-toolbox'
    ref: 'main'
    file: 'templates/deployment/deployment.gitlab-ci.yml'

variables:
  VALUES_TARGET_DIR_NAME: cert-manager
  HELMFILE_LABEL: app=cert-manager


### ANCHORS
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG =~ /^cert-manager-\d+\.\d+\.\d+(-\d+)?$/
      variables:
        HELM_DIR_PATH: "cert-manager"
    - if: $CI_COMMIT_TAG =~ /^cert-manager-init-\d+\.\d+\.\d+$/
      variables:
        HELM_DIR_PATH: "cert-manager-init"


### HELM
cert-manager:
  extends: .test_helm
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - cert-manager/**/*
      variables:
        HELM_DIR_PATH: "cert-manager"

cert-manager-init:
  extends: .test_helm
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - cert-manager-init/**/*
      variables:
        HELM_DIR_PATH: "cert-manager-init"

helm-build:
  extends: .build_helm
  <<: *tag_rules


### HELMFILE
values-sync:
  extends: .helmfile_values_sync
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - values/**/*

helmfile-update:
  extends: .helmfile_update
  <<: *tag_rules


### DEPLOY
kosmos-dev-deployment:
  extends: .kosmos_dev_deployment
  rules:
    - if: $CI_COMMIT_TAG =~ /^cert-manager-\d+\.\d+\.\d+(-\d+)?$/
      when: manual
    - if: $CI_COMMIT_TAG =~ /^cert-manager-init-\d+\.\d+\.\d+$/
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - values/**/*
      when: manual

kosmos-int-deployment:
  extends: .kosmos_int_deployment
  needs:
    - job: values-sync
      optional: true
    - job: helmfile-update
      optional: true
  rules:
    - if: $CI_COMMIT_TAG =~ /^cert-manager-\d+\.\d+\.\d+(-\d+)?$/
      when: manual
    - if: $CI_COMMIT_TAG =~ /^cert-manager-init-\d+\.\d+\.\d+$/
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      changes:
        - values/**/*
