# Prerequisites

Images from ```resources_for_airgap.txt``` should be loaded in private registry and aliases configured at kube level in registry.yaml.

Full Helmfile PaaS is provided under platform [********************](https://gitlab.corp.athea/athea/kosmos/apps/********************).  
You can also use the manual mode below.

Cert-manager has three deployment mode:

- Cert-manager only: with auto-signed certificates,
- Cert-manager with your corporation signed CA,
- with letsEncrypt provider: certificates will be generated using LetsEncrypt with DNS01 Challenge

More documentation about CA signed certificates:

- [cert-manager CA documentation](https://cert-manager.io/docs/configuration/ca/)

More documentation here about letsEncrypt provider:

- [cert-manager ACME documentation](https://cert-manager.io/docs/configuration/acme/)
- [DNS01 challenge documentation](https://cert-manager.io/docs/configuration/acme/dns01/cloudflare/)

## Network Policies

If network policies are globaly hardened at cluster level use:
```bash
kubectl apply -f network-policy --namespace kosmos-system-restricted
```

## Install or upgrade cert-manager with self-signed certificates

Install cert-manager:

```bash
helm upgrade --install -f ./values.yaml --create-namespace --namespace kosmos-system-restricted cert-manager ./cert-manager
```

Create a CA cluster issuer:

```bash
helm upgrade --install --namespace kosmos-system-restricted cert-manager-init ./cert-manager-init
```

## Install or upgrade cert-manager with your corporation CA signed certificate

Install cert-manager:

```bash
helm upgrade --install -f ./values.yaml --create-namespace --namespace kosmos-system-restricted cert-manager ./cert-manager
```

Create a CA cluster issuer:

This mode required a few more parameters to be set in this value file:

- ./cert-manager-init/values.yaml

**in ./cert-manager-init/values.yaml:**

- **corpCA.enabled:** set it to true to enable this mode,
- **corpCA.tlsCrt:** provide your corporate CA certificate file here,
- **corpCA.tlsKey:** provide your corporate CA key file here.

./cert-manager-init/values.yaml file should look like this:

```yaml
corpCA:
  # Enable it if you want to benefit from your corporation Signed CA TLS. You must provide both Certificate and its Key below
  enabled: true
  # Your corporation CA certificate file, in plain format
  tlsCrt: |
    -----BEGIN CERTIFICATE-----
    ...
    ...
    ...
    -----END CERTIFICATE-----
  # Your corporation CA  key file, in plain format
  tlsKey: |
    -----BEGIN PRIVATE KEY-----
    ...
    ...
    ...
    -----END PRIVATE KEY-----
```

Now you can install cert-manager-init:

```bash
helm upgrade --install --namespace kosmos-system-restricted cert-manager-init ./cert-manager-init
```

**NOTE:** do not commit any certificate and key files here as it is sensitive information.

## Install or upgrade cert-manager with LetsEncrypt provider

This mode required a few more parameters to be set in those values files:

- ./letsencrypt-values.yaml
- ./cert-manager-init/values.yaml

**in ./letsencrypt-values.yaml:**

- **dns01-recursive-nameservers:** DNS01 challenge needs DNS resolution to work properly, if your DNS flow is blocked, you can redirect it to a proxy using this param, or ignore by commenting the line,
- **http_proxy and https_proxy:** Cert-manager needs outbound traffic on http to communicate with letsencrypt, you can redirect traffic to a proxy if needed, or ignore by commenting the line,
- **no_proxy:** if http_proxy enabled, you should use this param and include your cluster network CIDR (we already added 10.0.0.0/8, which works with rke2 cluster), or ignore by commenting the line.

**in ./cert-manager-init/values.yaml:**

- **letsEncrypt.enabled:** set it to true to enable this mode.
- **letsEncrypt.cloudflareApiToken:** you should put here the Cloudflare API token, more info about the needed permissions [here](https://cert-manager.io/docs/configuration/acme/dns01/cloudflare/#api-tokens).
- **letsEncrypt.letsEncryptEmail:** Email that will receive notifications from LetsEncrypt about your certificates.
- **letsEncrypt.CloudflareEmail:** Email linked to the account that created the API token.

Once the values have been configured, you can install cert-manager:

```bash
helm upgrade --install -f ./values.yaml -f letsencrypt-values.yaml --create-namespace --namespace kosmos-system-restricted cert-manager ./cert-manager
```

Then cert-manager-init:

```bash
helm upgrade --install --namespace kosmos-system-restricted cert-manager-init ./cert-manager-init
```

## Request a certificate for an ingress

On your application, in your ingress config, you can tell cert-manager to request a certificate by adding those annotations:

### With selfsigned mode

```yaml
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: myIngress
  annotations:
    cert-manager.io/cluster-issuer: "kosmos-ca-issuer"
    cert-manager.io/duration: "8760h"
    cert-manager.io/renew-before: "360h"
    cert-manager.io/subject-countries: "France"
    cert-manager.io/subject-localities: "Velizy"
    cert-manager.io/subject-organizations: "Athea"
    cert-manager.io/subject-provinces: "IDF"
spec:
  ...
  ...
```

### With corporate CA mode

```yaml
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: myIngress
  annotations:
    cert-manager.io/cluster-issuer: "corp-ca-issuer"
    cert-manager.io/duration: "8760h"
    cert-manager.io/renew-before: "360h"
    cert-manager.io/subject-countries: "France"
    cert-manager.io/subject-localities: "Velizy"
    cert-manager.io/subject-organizations: "Athea"
    cert-manager.io/subject-provinces: "IDF"
spec:
  ...
  ...
```

### With Lets Encrypt mode

```yaml
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: myIngress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ...
  ...
```

Official documentation [here](https://cert-manager.io/docs/usage/ingress/)

**NOTE:**  you can choose between two cluster-issuers:

- letsencrypt-staging: for testing purpose, no [rate limit](https://letsencrypt.org/fr/docs/rate-limits/) from LetsEncrypt, will generate a certificate not valid,
- letsencrypt-prod: for production, valid certificates, with rate limit.

## Delete cert-manager

```bash
helm uninstall -n kosmos-system-restricted cert-manager 
helm uninstall -n kosmos-system-restricted cert-manager-init
```
