{{- if .Values.startupapicheck.enabled }}
{{- if .Values.global.podSecurityPolicy.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "startupapicheck.fullname" . }}-psp
  labels:
    app: {{ include "startupapicheck.name" . }}
    app.kubernetes.io/name: {{ include "startupapicheck.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "startupapicheck"
    {{- include "labels" . | nindent 4 }}
  {{- with .Values.startupapicheck.rbac.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "startupapicheck.fullname" . }}-psp
subjects:
  - kind: ServiceAccount
    name: {{ template "startupapicheck.serviceAccountName" . }}
    namespace: {{ include "cert-manager.namespace" . }}
{{- end }}
{{- end }}
