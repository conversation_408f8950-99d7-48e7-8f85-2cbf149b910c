{{- if .Values.cainjector.config -}}
{{- $config := .Values.cainjector.config -}}
{{- $_ := set $config "apiVersion" (default "cainjector.config.cert-manager.io/v1alpha1" $config.apiVersion) -}}
{{- $_ := set $config "kind" (default "CAInjectorConfiguration" $config.kind) -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "cainjector.fullname" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  labels:
    app: {{ include "cainjector.name" . }}
    app.kubernetes.io/name: {{ include "cainjector.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "cainjector"
    {{- include "labels" . | nindent 4 }}
data:
  config.yaml: |
    {{- $config | toYaml | nindent 4 }}
{{- end -}}