{{- if .Values.config -}}
{{- $config := .Values.config -}}
{{- $_ := set $config "apiVersion" (default "controller.config.cert-manager.io/v1alpha1" $config.apiVersion) -}}
{{- $_ := set $config "kind" (default "ControllerConfiguration" $config.kind) -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "cert-manager.fullname" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  labels:
    app: {{ include "cert-manager.name" . }}
    app.kubernetes.io/name: {{ include "cert-manager.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "controller"
    {{- include "labels" . | nindent 4 }}
data:
  config.yaml: |
    {{- $config | toYaml | nindent 4 }}
{{- end -}}