{{- if .Values.cainjector.enabled }}
{{- if .Values.global.podSecurityPolicy.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "cainjector.fullname" . }}-psp
  labels:
    app: {{ include "cainjector.name" . }}
    app.kubernetes.io/name: {{ include "cainjector.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "cainjector"
    {{- include "labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "cainjector.fullname" . }}-psp
subjects:
  - kind: ServiceAccount
    name: {{ template "cainjector.serviceAccountName" . }}
    namespace: {{ include "cert-manager.namespace" . }}
{{- end }}
{{- end }}
