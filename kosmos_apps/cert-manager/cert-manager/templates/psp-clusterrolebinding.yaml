{{- if .Values.global.podSecurityPolicy.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "cert-manager.fullname" . }}-psp
  labels:
    app: {{ include "cert-manager.name" . }}
    app.kubernetes.io/name: {{ include "cert-manager.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "controller"
    {{- include "labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "cert-manager.fullname" . }}-psp
subjects:
  - kind: ServiceAccount
    name: {{ template "cert-manager.serviceAccountName" . }}
    namespace: {{ include "cert-manager.namespace" . }}
{{- end }}
