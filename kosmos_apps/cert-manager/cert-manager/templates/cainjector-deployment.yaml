{{- if .Values.cainjector.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "cainjector.fullname" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  labels:
    app: {{ include "cainjector.name" . }}
    app.kubernetes.io/name: {{ include "cainjector.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "cainjector"
    {{- include "labels" . | nindent 4 }}
  {{- with .Values.cainjector.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.cainjector.replicaCount }}
  {{- /* The if statement below is equivalent to {{- if $value }} but will also return true for 0. */ -}}
  {{- if not (has (quote .Values.global.revisionHistoryLimit) (list "" (quote ""))) }}
  revisionHistoryLimit: {{ .Values.global.revisionHistoryLimit }}
  {{- end }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "cainjector.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: "cainjector"
  {{- with .Values.cainjector.strategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
        app: {{ include "cainjector.name" . }}
        app.kubernetes.io/name: {{ include "cainjector.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/component: "cainjector"
        {{- include "labels" . | nindent 8 }}
        {{- with .Values.cainjector.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.cainjector.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if and .Values.prometheus.enabled (not (or .Values.prometheus.servicemonitor.enabled .Values.prometheus.podmonitor.enabled)) }}
      {{- if not .Values.cainjector.podAnnotations }}
      annotations:
      {{- end }}
        prometheus.io/path: "/metrics"
        prometheus.io/scrape: 'true'
        prometheus.io/port: '9402'
      {{- end }}
    spec:
      {{- if not .Values.cainjector.serviceAccount.create }}
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- end }}
      serviceAccountName: {{ template "cainjector.serviceAccountName" . }}
      {{- if hasKey .Values.cainjector "automountServiceAccountToken" }}
      automountServiceAccountToken: {{ .Values.cainjector.automountServiceAccountToken }}
      {{- end }}
      enableServiceLinks: {{ .Values.cainjector.enableServiceLinks }}
      {{- with .Values.global.priorityClassName }}
      priorityClassName: {{ . | quote }}
      {{- end }}
      {{- with .Values.cainjector.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}-cainjector
          image: "{{ template "image" (tuple .Values.cainjector.image $.Chart.AppVersion) }}"
          imagePullPolicy: {{ .Values.cainjector.image.pullPolicy }}
          args:
          {{- /* The if statement below is equivalent to {{- if $value }} but will also return true for 0. */ -}}
          {{- if not (has (quote .Values.global.logLevel) (list "" (quote ""))) }}
          - --v={{ .Values.global.logLevel }}
          {{- end }}
          {{- if .Values.cainjector.config }}
          - --config=/var/cert-manager/config/config.yaml
          {{- end }}
          {{- with .Values.global.leaderElection }}
          - --leader-election-namespace={{ .namespace }}
          {{- if .leaseDuration }}
          - --leader-election-lease-duration={{ .leaseDuration }}
          {{- end }}
          {{- if .renewDeadline }}
          - --leader-election-renew-deadline={{ .renewDeadline }}
          {{- end }}
          {{- if .retryPeriod }}
          - --leader-election-retry-period={{ .retryPeriod }}
          {{- end }}
          {{- end }}
          {{- with .Values.cainjector.featureGates}}
          - --feature-gates={{ . }}
          {{- end}}
          {{- with .Values.cainjector.extraArgs }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- if not .Values.prometheus.enabled }}
          - --metrics-listen-address=0
          {{- end }}
          {{- if .Values.prometheus.enabled }}
          ports:
          - containerPort: 9402
            name: http-metrics
            protocol: TCP
          {{- end }}
          env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          {{- with .Values.cainjector.extraEnv }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- with .Values.cainjector.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.cainjector.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if or .Values.cainjector.config .Values.cainjector.volumeMounts }}
          volumeMounts:
            {{- if .Values.cainjector.config }}
            - name: config
              mountPath: /var/cert-manager/config
            {{- end }}
            {{- with .Values.cainjector.volumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          {{- end }}
      {{- with .Values.cainjector.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.cainjector.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.cainjector.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with  .Values.cainjector.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if or .Values.cainjector.volumes .Values.cainjector.config }}
      volumes:
        {{- if .Values.cainjector.config }}
        - name: config
          configMap:
            name: {{ include "cainjector.fullname" . }}
        {{- end }}
        {{ with .Values.cainjector.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
{{- end }}
