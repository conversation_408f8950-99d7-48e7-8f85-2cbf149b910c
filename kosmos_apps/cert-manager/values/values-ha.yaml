# The number of replicas of the cert-manager controller to run.
#
# The default is 1, but in production set this to 2 or 3 to provide high
# availability.
#
# If `replicas > 1`, consider setting `podDisruptionBudget.enabled=true`.
#
# Note that cert-manager uses leader election to ensure that there can
# only be a single instance active at a time.
replicaCount: 2

webhook:
  # Number of replicas of the cert-manager webhook to run.
  #
  # The default is 1, but in production set this to 2 or 3 to provide high
  # availability.
  #
  # If `replicas > 1`, consider setting `webhook.podDisruptionBudget.enabled=true`.
  replicaCount: 2

cainjector:
  # The number of replicas of the cert-manager cainjector to run.
  #
  # The default is 1, but in production set this to 2 or 3 to provide high
  # availability.
  #
  # If `replicas > 1`, consider setting `cainjector.podDisruptionBudget.enabled=true`.
  #
  # Note that cert-manager uses leader election to ensure that there can
  # only be a single instance active at a time.
  replicaCount: 2
