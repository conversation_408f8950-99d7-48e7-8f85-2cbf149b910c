{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.metrics.enabled .Values.metrics.podMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ default (include "common.names.namespace" .) .Values.metrics.podMonitor.namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- if .Values.metrics.podMonitor.additionalLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.metrics.podMonitor.additionalLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  podMetricsEndpoints:
    - port: {{ .Values.metrics.podMonitor.port }}
      {{- if .Values.metrics.podMonitor.interval }}
      interval: {{ .Values.metrics.podMonitor.interval }}
      {{- end }}
      {{- if .Values.metrics.podMonitor.scrapeTimeout }}
      scrapeTimeout: {{ .Values.metrics.podMonitor.scrapeTimeout }}
      {{- end }}
      {{- if .Values.metrics.podMonitor.honorLabels }}
      honorLabels: {{ .Values.metrics.podMonitor.honorLabels }}
      {{- end }}
      {{- with concat .Values.metrics.podMonitor.relabelings .Values.metrics.podMonitor.relabellings }}
      relabelings: {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- if .Values.metrics.podMonitor.metricRelabelings }}
      metricRelabelings: {{- toYaml .Values.metrics.podMonitor.metricRelabelings | nindent 6 }}
      {{- end }}
    {{- range .Values.metrics.podMonitor.additionalEndpoints }}
    - port: {{ .port }}
      {{- if .interval }}
      interval: {{ .interval }}
      {{- end }}
      {{- if .path }}
      path: {{ .path }}
      {{- end }}
      {{- if .honorLabels }}
      honorLabels: {{ .honorLabels }}
      {{- end }}
      {{- with concat .relabelings .relabellings }}
      relabelings: {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- if .metricRelabelings }}
      metricRelabelings: {{- toYaml .metricRelabelings | nindent 6 }}
      {{- end }}
      {{- if .scrapeTimeout }}
      scrapeTimeout: {{ .scrapeTimeout }}
      {{- end }}
      {{- if .params }}
      params:
        {{- range $key, $value := .params }}
        {{ $key }}:
          {{- range $value }}
          - {{ . | quote }}
          {{- end }}
        {{- end }}
      {{- end }}
    {{- end }}
  {{- if .Values.metrics.serviceMonitor.podTargetLabels }}
  podTargetLabels: {{- toYaml .Values.metrics.podMonitor.podTargetLabels | nindent 4 }}
  {{- end }}
  {{- with .Values.metrics.podMonitor.sampleLimit -}}
  sampleLimit: {{ . }}
  {{- end }}
  {{- with .Values.metrics.podMonitor.targetLimit -}}
  targetLimit: {{ . }}
  {{- end }}
  namespaceSelector:
    matchNames:
      - {{ include "common.names.namespace" . | quote }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 6 }}
{{- end }}
