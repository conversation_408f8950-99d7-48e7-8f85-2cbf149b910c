{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.rbac.create }}
apiVersion: {{ include "common.capabilities.rbac.apiVersion" . }}
kind: Role
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
rules:
  {{- if and (include "common.capabilities.psp.supported" .) .Values.podSecurityPolicy.enabled }}
  - apiGroups:
      - '{{ template "podSecurityPolicy.apiGroup" . }}'
    resources:
      - 'podsecuritypolicies'
    verbs:
      - 'use'
    resourceNames: [{{ printf "%s-master" (include "common.names.fullname" .) }}]
  {{- end }}
  {{- if and .Values.sentinel.enabled (or .Values.sentinel.masterService.enabled .Values.sentinel.service.createMaster) }}
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["list", "patch"]
  {{- end -}}
  {{- if .Values.rbac.rules }}
  {{- include "common.tplvalues.render" ( dict "value" .Values.rbac.rules "context" $ ) | nindent 2 }}
  {{- end }}
{{- end }}
