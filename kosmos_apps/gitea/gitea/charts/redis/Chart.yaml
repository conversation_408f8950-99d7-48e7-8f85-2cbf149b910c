annotations:
  category: Database
  images: |
    - name: kubectl
      image: docker.io/bitnami/kubectl:1.32.2-debian-12-r2
    - name: os-shell
      image: docker.io/bitnami/os-shell:12-debian-12-r38
    - name: redis
      image: docker.io/bitnami/redis:7.4.2-debian-12-r4
    - name: redis-exporter
      image: docker.io/bitnami/redis-exporter:1.67.0-debian-12-r9
    - name: redis-sentinel
      image: docker.io/bitnami/redis-sentinel:7.4.2-debian-12-r4
  licenses: Apache-2.0
apiVersion: v2
appVersion: 7.4.2
dependencies:
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: Redis(R) is an open source, advanced key-value store. It is often referred
  to as a data structure server since keys can contain strings, hashes, lists, sets
  and sorted sets.
home: https://bitnami.com
icon: https://dyltqmyl993wv.cloudfront.net/assets/stacks/redis/img/redis-stack-220x234.png
keywords:
- redis
- keyvalue
- database
maintainers:
- name: Broadcom, Inc. All Rights Reserved.
  url: https://github.com/bitnami/charts
name: redis
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/redis
version: 20.8.0
