annotations:
  category: Database
  images: |
    - name: os-shell
      image: docker.io/bitnami/os-shell:12-debian-12-r39
    - name: pgpool
      image: docker.io/bitnami/pgpool:4.5.5-debian-12-r6
    - name: postgres-exporter
      image: docker.io/bitnami/postgres-exporter:0.16.0-debian-12-r11
    - name: postgresql-repmgr
      image: docker.io/bitnami/postgresql-repmgr:17.4.0-debian-12-r2
  licenses: Apache-2.0
apiVersion: v2
appVersion: 17.4.0
dependencies:
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: This PostgreSQL cluster solution includes the PostgreSQL replication
  manager, an open-source tool for managing replication and failover on PostgreSQL
  clusters.
home: https://bitnami.com
icon: https://dyltqmyl993wv.cloudfront.net/assets/stacks/postgresql/img/postgresql-stack-220x234.png
keywords:
- postgresql
- repmgr
- pgpool
- postgres
- database
- sql
- replication
- cluster
- high availability
maintainers:
- name: Broadcom, Inc. All Rights Reserved.
  url: https://github.com/bitnami/charts
name: postgresql-ha
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/postgresql-ha
version: 15.2.3
