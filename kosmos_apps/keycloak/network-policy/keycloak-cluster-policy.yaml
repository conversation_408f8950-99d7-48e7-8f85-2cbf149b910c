kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: keycloak-cluster-allow-ingress
spec:
  podSelector: {}
  ingress:
    - ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 443
  policyTypes:
    - Ingress
---
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: keycloak-cluster-allow-egress
spec:
  podSelector: {}
  egress:
    - ports:
        - protocol: TCP
          port: 5432
      to:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: kosmos-sql
  policyTypes:
    - Egress
