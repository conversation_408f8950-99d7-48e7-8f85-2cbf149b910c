kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: keycloak-operator-allow-ingress
spec:
  podSelector: {}
  ingress:
    - ports:
        - protocol: TCP
          port: 8080
  policyTypes:
    - Ingress
---
kind: CiliumNetworkPolicy
apiVersion: cilium.io/v2
metadata:
  name: keycloak-operator-to-api-server-egress
spec:
  endpointSelector: {}
  egress:
    - toEntities:
        - kube-apiserver
    - toPorts:
        - ports:
            - port: "6443"
              protocol: TCP
