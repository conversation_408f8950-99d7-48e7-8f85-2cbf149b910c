{{- $secret_name := "import-admin-keycloak" -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secret_name | quote }}
  labels:
    {{- include "keycloak-secrets.labels" . | nindent 4 }}
type: "kubernetes.io/basic-auth"
data:
  {{- $old_sec:= lookup "v1" "Secret" .Release.Namespace $secret_name }}
  {{- if or (not $old_sec) (not $old_sec.data) }}
  username: {{ .Values.username | b64enc | quote }}
  password: {{ .Values.password | default (randAlphaNum 20) | b64enc | quote }}
  {{ else }}
  username: {{ index $old_sec.data "username" }}
  password: {{ index $old_sec.data "password" }}
  {{ end }}
