# Default values for keycloakimporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

config:
  keycloak: |
    clientId: admin-cli
    username: ref+k8s://v1/Secret/kosmos-iam/keycloak-cluster-initial-admin/username
    password: ref+k8s://v1/Secret/kosmos-iam/keycloak-cluster-initial-admin/password
    grantType: password
    baseUrl: http://keycloak-cluster-service.kosmos-iam.svc.cluster.local.
    skipCertCheck: false
    createRealm: false
  import: |
    {
        "realm": "master",
        "groups": [],
        "clients": [],
        "clientScopes": [],
        "scopeMappings": [],
        "clientScopeMappings": {},
        "roles": {
            "realm": [],
            "client": {}
        },
        "users": [
            {
                "enabled": true,
                "username": "ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/username+",
                "email": "",
                "emailVerified": true,
                "groups": [],
                "firstName": "admin",
                "lastName": "keycloakimporter",
                "attributes": {},
                "requiredActions": [],
                "realmRoles": [
                    "admin"
                ],
                "clientRoles": {},
                "credentials": [
                    {
                        "type": "password",
                        "value": "ref+k8s://v1/Secret/kosmos-iam/import-admin-keycloak/password+",
                        "temporary": false
                    }
                ]
            }
        ]
    }
