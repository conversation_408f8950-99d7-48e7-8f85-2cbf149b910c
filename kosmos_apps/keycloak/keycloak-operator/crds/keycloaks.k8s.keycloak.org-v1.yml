# Generated by Fabric8 CRDGenerator, manual edits might get overwritten!
apiVersion: "apiextensions.k8s.io/v1"
kind: "CustomResourceDefinition"
metadata:
  name: "keycloaks.k8s.keycloak.org"
spec:
  group: "k8s.keycloak.org"
  names:
    kind: "Keycloak"
    plural: "keycloaks"
    shortNames:
    - "kc"
    singular: "keycloak"
  scope: "Namespaced"
  versions:
  - name: "v2alpha1"
    schema:
      openAPIV3Schema:
        properties:
          spec:
            properties:
              additionalOptions:
                description: |-
                  Configuration of the Keycloak server.
                  expressed as a keys (reference: https://www.keycloak.org/server/all-config) and values that can be either direct values or references to secrets.
                items:
                  properties:
                    name:
                      type: "string"
                    secret:
                      properties:
                        key:
                          type: "string"
                        name:
                          type: "string"
                        optional:
                          type: "boolean"
                      type: "object"
                    value:
                      type: "string"
                  type: "object"
                type: "array"
              bootstrapAdmin:
                description: "In this section you can configure Keycloak's bootstrap\
                  \ admin - will be used only for inital cluster creation."
                properties:
                  service:
                    description: "Configures the bootstrap admin service account"
                    properties:
                      secret:
                        description: "Name of the Secret that contains the client-id\
                          \ and client-secret keys"
                        type: "string"
                    type: "object"
                  user:
                    description: "Configures the bootstrap admin user"
                    properties:
                      secret:
                        description: "Name of the Secret that contains the username\
                          \ and password keys"
                        type: "string"
                    type: "object"
                type: "object"
              cache:
                description: "In this section you can configure Keycloak's cache"
                properties:
                  configMapFile:
                    properties:
                      key:
                        type: "string"
                      name:
                        type: "string"
                      optional:
                        type: "boolean"
                    type: "object"
                type: "object"
              db:
                description: "In this section you can find all properties related\
                  \ to connect to a database."
                properties:
                  database:
                    description: "Sets the database name of the default JDBC URL of\
                      \ the chosen vendor. If the `url` option is set, this option\
                      \ is ignored."
                    type: "string"
                  host:
                    description: "Sets the hostname of the default JDBC URL of the\
                      \ chosen vendor. If the `url` option is set, this option is\
                      \ ignored."
                    type: "string"
                  passwordSecret:
                    description: "The reference to a secret holding the password of\
                      \ the database user."
                    properties:
                      key:
                        type: "string"
                      name:
                        type: "string"
                      optional:
                        type: "boolean"
                    type: "object"
                  poolInitialSize:
                    description: "The initial size of the connection pool."
                    type: "integer"
                  poolMaxSize:
                    description: "The maximum size of the connection pool."
                    type: "integer"
                  poolMinSize:
                    description: "The minimal size of the connection pool."
                    type: "integer"
                  port:
                    description: "Sets the port of the default JDBC URL of the chosen\
                      \ vendor. If the `url` option is set, this option is ignored."
                    type: "integer"
                  schema:
                    description: "The database schema to be used."
                    type: "string"
                  url:
                    description: "The full database JDBC URL. If not provided, a default\
                      \ URL is set based on the selected database vendor. For instance,\
                      \ if using 'postgres', the default JDBC URL would be '************************************'. "
                    type: "string"
                  usernameSecret:
                    description: "The reference to a secret holding the username of\
                      \ the database user."
                    properties:
                      key:
                        type: "string"
                      name:
                        type: "string"
                      optional:
                        type: "boolean"
                    type: "object"
                  vendor:
                    description: "The database vendor."
                    type: "string"
                type: "object"
              features:
                description: "In this section you can configure Keycloak features,\
                  \ which should be enabled/disabled."
                properties:
                  disabled:
                    description: "Disabled Keycloak features"
                    items:
                      type: "string"
                    type: "array"
                  enabled:
                    description: "Enabled Keycloak features"
                    items:
                      type: "string"
                    type: "array"
                type: "object"
              hostname:
                description: "In this section you can configure Keycloak hostname\
                  \ and related properties."
                properties:
                  admin:
                    description: "The hostname for accessing the administration console.\
                      \ Applicable for Hostname v1 and v2."
                    type: "string"
                  adminUrl:
                    description: "DEPRECATED. Sets the base URL for accessing the\
                      \ administration console, including scheme, host, port and path.\
                      \ Applicable for Hostname v1."
                    type: "string"
                  backchannelDynamic:
                    description: "Enables dynamic resolving of backchannel URLs, including\
                      \ hostname, scheme, port and context path. Set to true if your\
                      \ application accesses Keycloak via a private network. Applicable\
                      \ for Hostname v2."
                    type: "boolean"
                  hostname:
                    description: "Hostname for the Keycloak server. Applicable for\
                      \ Hostname v1 and v2."
                    type: "string"
                  strict:
                    description: "Disables dynamically resolving the hostname from\
                      \ request headers. Applicable for Hostname v1 and v2."
                    type: "boolean"
                  strictBackchannel:
                    description: "DEPRECATED. By default backchannel URLs are dynamically\
                      \ resolved from request headers to allow internal and external\
                      \ applications. Applicable for Hostname v1."
                    type: "boolean"
                type: "object"
              http:
                description: "In this section you can configure Keycloak features\
                  \ related to HTTP and HTTPS"
                properties:
                  httpEnabled:
                    description: "Enables the HTTP listener."
                    type: "boolean"
                  httpPort:
                    description: "The used HTTP port."
                    type: "integer"
                  httpsPort:
                    description: "The used HTTPS port."
                    type: "integer"
                  tlsSecret:
                    description: "A secret containing the TLS configuration for HTTPS.\
                      \ Reference: https://kubernetes.io/docs/concepts/configuration/secret/#tls-secrets."
                    type: "string"
                type: "object"
              httpManagement:
                description: "In this section you can configure Keycloak's management\
                  \ interface setting."
                properties:
                  port:
                    description: "Port of the management interface."
                    type: "integer"
                type: "object"
              image:
                description: "Custom Keycloak image to be used."
                type: "string"
              imagePullSecrets:
                description: "Secret(s) that might be used when pulling an image from\
                  \ a private container image registry or repository."
                items:
                  properties:
                    name:
                      type: "string"
                  type: "object"
                type: "array"
              ingress:
                description: |-
                  The deployment is, by default, exposed through a basic ingress.
                  You can change this behaviour by setting the enabled property to false.
                properties:
                  annotations:
                    additionalProperties:
                      type: "string"
                    description: "Additional annotations to be appended to the Ingress\
                      \ object"
                    type: "object"
                  className:
                    type: "string"
                  enabled:
                    type: "boolean"
                type: "object"
              instances:
                description: "Number of Keycloak instances. Default is 1."
                type: "integer"
              networkPolicy:
                description: "Controls the ingress traffic flow into Keycloak pods."
                properties:
                  enabled:
                    default: true
                    description: "Enables or disables the ingress traffic control."
                    type: "boolean"
                  http:
                    description: "A list of sources which should be able to access\
                      \ this endpoint. Items in this list are combined using a logical\
                      \ OR operation. If this field is empty or missing, this rule\
                      \ matches all sources (traffic not restricted by source). If\
                      \ this field is present and contains at least one item, this\
                      \ rule allows traffic only if the traffic matches at least one\
                      \ item in the from list."
                    items:
                      properties:
                        ipBlock:
                          properties:
                            cidr:
                              type: "string"
                            except:
                              items:
                                type: "string"
                              type: "array"
                          type: "object"
                        namespaceSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: "string"
                                  operator:
                                    type: "string"
                                  values:
                                    items:
                                      type: "string"
                                    type: "array"
                                type: "object"
                              type: "array"
                            matchLabels:
                              additionalProperties:
                                type: "string"
                              type: "object"
                          type: "object"
                        podSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: "string"
                                  operator:
                                    type: "string"
                                  values:
                                    items:
                                      type: "string"
                                    type: "array"
                                type: "object"
                              type: "array"
                            matchLabels:
                              additionalProperties:
                                type: "string"
                              type: "object"
                          type: "object"
                      type: "object"
                    type: "array"
                  https:
                    description: "A list of sources which should be able to access\
                      \ this endpoint. Items in this list are combined using a logical\
                      \ OR operation. If this field is empty or missing, this rule\
                      \ matches all sources (traffic not restricted by source). If\
                      \ this field is present and contains at least one item, this\
                      \ rule allows traffic only if the traffic matches at least one\
                      \ item in the from list."
                    items:
                      properties:
                        ipBlock:
                          properties:
                            cidr:
                              type: "string"
                            except:
                              items:
                                type: "string"
                              type: "array"
                          type: "object"
                        namespaceSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: "string"
                                  operator:
                                    type: "string"
                                  values:
                                    items:
                                      type: "string"
                                    type: "array"
                                type: "object"
                              type: "array"
                            matchLabels:
                              additionalProperties:
                                type: "string"
                              type: "object"
                          type: "object"
                        podSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: "string"
                                  operator:
                                    type: "string"
                                  values:
                                    items:
                                      type: "string"
                                    type: "array"
                                type: "object"
                              type: "array"
                            matchLabels:
                              additionalProperties:
                                type: "string"
                              type: "object"
                          type: "object"
                      type: "object"
                    type: "array"
                  management:
                    description: "A list of sources which should be able to access\
                      \ this endpoint. Items in this list are combined using a logical\
                      \ OR operation. If this field is empty or missing, this rule\
                      \ matches all sources (traffic not restricted by source). If\
                      \ this field is present and contains at least one item, this\
                      \ rule allows traffic only if the traffic matches at least one\
                      \ item in the from list."
                    items:
                      properties:
                        ipBlock:
                          properties:
                            cidr:
                              type: "string"
                            except:
                              items:
                                type: "string"
                              type: "array"
                          type: "object"
                        namespaceSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: "string"
                                  operator:
                                    type: "string"
                                  values:
                                    items:
                                      type: "string"
                                    type: "array"
                                type: "object"
                              type: "array"
                            matchLabels:
                              additionalProperties:
                                type: "string"
                              type: "object"
                          type: "object"
                        podSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: "string"
                                  operator:
                                    type: "string"
                                  values:
                                    items:
                                      type: "string"
                                    type: "array"
                                type: "object"
                              type: "array"
                            matchLabels:
                              additionalProperties:
                                type: "string"
                              type: "object"
                          type: "object"
                      type: "object"
                    type: "array"
                type: "object"
              proxy:
                description: "In this section you can configure Keycloak's reverse\
                  \ proxy setting"
                properties:
                  headers:
                    description: "The proxy headers that should be accepted by the\
                      \ server. Misconfiguration might leave the server exposed to\
                      \ security vulnerabilities."
                    type: "string"
                type: "object"
              resources:
                description: "Compute Resources required by Keycloak container"
                properties:
                  claims:
                    items:
                      properties:
                        name:
                          type: "string"
                        request:
                          type: "string"
                      type: "object"
                    type: "array"
                  limits:
                    additionalProperties:
                      anyOf:
                      - type: "integer"
                      - type: "string"
                      x-kubernetes-int-or-string: true
                    type: "object"
                  requests:
                    additionalProperties:
                      anyOf:
                      - type: "integer"
                      - type: "string"
                      x-kubernetes-int-or-string: true
                    type: "object"
                type: "object"
              scheduling:
                description: "In this section you can configure Keycloak's scheduling"
                properties:
                  affinity:
                    properties:
                      nodeAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                preference:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                    matchFields:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                  type: "object"
                                weight:
                                  type: "integer"
                              type: "object"
                            type: "array"
                          requiredDuringSchedulingIgnoredDuringExecution:
                            properties:
                              nodeSelectorTerms:
                                items:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                    matchFields:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                  type: "object"
                                type: "array"
                            type: "object"
                        type: "object"
                      podAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                podAffinityTerm:
                                  properties:
                                    labelSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: "string"
                                              operator:
                                                type: "string"
                                              values:
                                                items:
                                                  type: "string"
                                                type: "array"
                                            type: "object"
                                          type: "array"
                                        matchLabels:
                                          additionalProperties:
                                            type: "string"
                                          type: "object"
                                      type: "object"
                                    matchLabelKeys:
                                      items:
                                        type: "string"
                                      type: "array"
                                    mismatchLabelKeys:
                                      items:
                                        type: "string"
                                      type: "array"
                                    namespaceSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: "string"
                                              operator:
                                                type: "string"
                                              values:
                                                items:
                                                  type: "string"
                                                type: "array"
                                            type: "object"
                                          type: "array"
                                        matchLabels:
                                          additionalProperties:
                                            type: "string"
                                          type: "object"
                                      type: "object"
                                    namespaces:
                                      items:
                                        type: "string"
                                      type: "array"
                                    topologyKey:
                                      type: "string"
                                  type: "object"
                                weight:
                                  type: "integer"
                              type: "object"
                            type: "array"
                          requiredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                    matchLabels:
                                      additionalProperties:
                                        type: "string"
                                      type: "object"
                                  type: "object"
                                matchLabelKeys:
                                  items:
                                    type: "string"
                                  type: "array"
                                mismatchLabelKeys:
                                  items:
                                    type: "string"
                                  type: "array"
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                    matchLabels:
                                      additionalProperties:
                                        type: "string"
                                      type: "object"
                                  type: "object"
                                namespaces:
                                  items:
                                    type: "string"
                                  type: "array"
                                topologyKey:
                                  type: "string"
                              type: "object"
                            type: "array"
                        type: "object"
                      podAntiAffinity:
                        properties:
                          preferredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                podAffinityTerm:
                                  properties:
                                    labelSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: "string"
                                              operator:
                                                type: "string"
                                              values:
                                                items:
                                                  type: "string"
                                                type: "array"
                                            type: "object"
                                          type: "array"
                                        matchLabels:
                                          additionalProperties:
                                            type: "string"
                                          type: "object"
                                      type: "object"
                                    matchLabelKeys:
                                      items:
                                        type: "string"
                                      type: "array"
                                    mismatchLabelKeys:
                                      items:
                                        type: "string"
                                      type: "array"
                                    namespaceSelector:
                                      properties:
                                        matchExpressions:
                                          items:
                                            properties:
                                              key:
                                                type: "string"
                                              operator:
                                                type: "string"
                                              values:
                                                items:
                                                  type: "string"
                                                type: "array"
                                            type: "object"
                                          type: "array"
                                        matchLabels:
                                          additionalProperties:
                                            type: "string"
                                          type: "object"
                                      type: "object"
                                    namespaces:
                                      items:
                                        type: "string"
                                      type: "array"
                                    topologyKey:
                                      type: "string"
                                  type: "object"
                                weight:
                                  type: "integer"
                              type: "object"
                            type: "array"
                          requiredDuringSchedulingIgnoredDuringExecution:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                    matchLabels:
                                      additionalProperties:
                                        type: "string"
                                      type: "object"
                                  type: "object"
                                matchLabelKeys:
                                  items:
                                    type: "string"
                                  type: "array"
                                mismatchLabelKeys:
                                  items:
                                    type: "string"
                                  type: "array"
                                namespaceSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                    matchLabels:
                                      additionalProperties:
                                        type: "string"
                                      type: "object"
                                  type: "object"
                                namespaces:
                                  items:
                                    type: "string"
                                  type: "array"
                                topologyKey:
                                  type: "string"
                              type: "object"
                            type: "array"
                        type: "object"
                    type: "object"
                  priorityClassName:
                    type: "string"
                  tolerations:
                    items:
                      properties:
                        effect:
                          type: "string"
                        key:
                          type: "string"
                        operator:
                          type: "string"
                        tolerationSeconds:
                          type: "integer"
                        value:
                          type: "string"
                      type: "object"
                    type: "array"
                  topologySpreadConstraints:
                    items:
                      properties:
                        labelSelector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: "string"
                                  operator:
                                    type: "string"
                                  values:
                                    items:
                                      type: "string"
                                    type: "array"
                                type: "object"
                              type: "array"
                            matchLabels:
                              additionalProperties:
                                type: "string"
                              type: "object"
                          type: "object"
                        matchLabelKeys:
                          items:
                            type: "string"
                          type: "array"
                        maxSkew:
                          type: "integer"
                        minDomains:
                          type: "integer"
                        nodeAffinityPolicy:
                          type: "string"
                        nodeTaintsPolicy:
                          type: "string"
                        topologyKey:
                          type: "string"
                        whenUnsatisfiable:
                          type: "string"
                      type: "object"
                    type: "array"
                type: "object"
              startOptimized:
                description: "Set to force the behavior of the --optimized flag for\
                  \ the start command. If left unspecified the operator will assume\
                  \ custom images have already been augmented."
                type: "boolean"
              tracing:
                description: "In this section you can configure OpenTelemetry Tracing\
                  \ for Keycloak."
                properties:
                  compression:
                    description: "OpenTelemetry compression method used to compress\
                      \ payloads. If unset, compression is disabled. Possible values\
                      \ are: gzip, none."
                    type: "string"
                  enabled:
                    description: "Enables the OpenTelemetry tracing."
                    type: "boolean"
                  endpoint:
                    description: "OpenTelemetry endpoint to connect to."
                    type: "string"
                  protocol:
                    description: "OpenTelemetry protocol used for the telemetry data\
                      \ (default 'grpc'). For more information, check the Tracing\
                      \ guide."
                    type: "string"
                  resourceAttributes:
                    additionalProperties:
                      type: "string"
                    description: "OpenTelemetry resource attributes present in the\
                      \ exported trace to characterize the telemetry producer."
                    type: "object"
                  samplerRatio:
                    description: "OpenTelemetry sampler ratio. Probability that a\
                      \ span will be sampled. Expected double value in interval [0,1]."
                    type: "number"
                  samplerType:
                    description: "OpenTelemetry sampler to use for tracing (default\
                      \ 'traceidratio'). For more information, check the Tracing guide."
                    type: "string"
                  serviceName:
                    description: "OpenTelemetry service name. Takes precedence over\
                      \ 'service.name' defined in the 'resourceAttributes' map."
                    type: "string"
                type: "object"
              transaction:
                description: "In this section you can find all properties related\
                  \ to the settings of transaction behavior."
                properties:
                  xaEnabled:
                    description: "Determine whether Keycloak should use a non-XA datasource\
                      \ in case the database does not support XA transactions."
                    type: "boolean"
                type: "object"
              truststores:
                additionalProperties:
                  properties:
                    name:
                      description: "Not used. To be removed in later versions."
                      type: "string"
                    secret:
                      properties:
                        name:
                          type: "string"
                        optional:
                          type: "boolean"
                      required:
                      - "name"
                      type: "object"
                  required:
                  - "secret"
                  type: "object"
                description: "In this section you can configure Keycloak truststores."
                type: "object"
              unsupported:
                description: |-
                  In this section you can configure podTemplate advanced features, not production-ready, and not supported settings.
                  Use at your own risk and open an issue with your use-case if you don't find an alternative way.
                properties:
                  podTemplate:
                    description: |-
                      You can configure that will be merged with the one configured by default by the operator.
                      Use at your own risk, we reserve the possibility to remove/change the way any field gets merged in future releases without notice.
                      Reference: https://kubernetes.io/docs/concepts/workloads/pods/#pod-templates
                    properties:
                      metadata:
                        properties:
                          annotations:
                            additionalProperties:
                              type: "string"
                            type: "object"
                          creationTimestamp:
                            type: "string"
                          deletionGracePeriodSeconds:
                            type: "integer"
                          deletionTimestamp:
                            type: "string"
                          finalizers:
                            items:
                              type: "string"
                            type: "array"
                          generateName:
                            type: "string"
                          generation:
                            type: "integer"
                          labels:
                            additionalProperties:
                              type: "string"
                            type: "object"
                          managedFields:
                            items:
                              properties:
                                apiVersion:
                                  type: "string"
                                fieldsType:
                                  type: "string"
                                fieldsV1:
                                  type: "object"
                                manager:
                                  type: "string"
                                operation:
                                  type: "string"
                                subresource:
                                  type: "string"
                                time:
                                  type: "string"
                              type: "object"
                            type: "array"
                          name:
                            type: "string"
                          namespace:
                            type: "string"
                          ownerReferences:
                            items:
                              properties:
                                apiVersion:
                                  type: "string"
                                blockOwnerDeletion:
                                  type: "boolean"
                                controller:
                                  type: "boolean"
                                kind:
                                  type: "string"
                                name:
                                  type: "string"
                                uid:
                                  type: "string"
                              type: "object"
                            type: "array"
                          resourceVersion:
                            type: "string"
                          selfLink:
                            type: "string"
                          uid:
                            type: "string"
                        type: "object"
                      spec:
                        properties:
                          activeDeadlineSeconds:
                            type: "integer"
                          affinity:
                            properties:
                              nodeAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        preference:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                          type: "object"
                                        weight:
                                          type: "integer"
                                      type: "object"
                                    type: "array"
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    properties:
                                      nodeSelectorTerms:
                                        items:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                          type: "object"
                                        type: "array"
                                    type: "object"
                                type: "object"
                              podAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: "string"
                                                      operator:
                                                        type: "string"
                                                      values:
                                                        items:
                                                          type: "string"
                                                        type: "array"
                                                    type: "object"
                                                  type: "array"
                                                matchLabels:
                                                  additionalProperties:
                                                    type: "string"
                                                  type: "object"
                                              type: "object"
                                            matchLabelKeys:
                                              items:
                                                type: "string"
                                              type: "array"
                                            mismatchLabelKeys:
                                              items:
                                                type: "string"
                                              type: "array"
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: "string"
                                                      operator:
                                                        type: "string"
                                                      values:
                                                        items:
                                                          type: "string"
                                                        type: "array"
                                                    type: "object"
                                                  type: "array"
                                                matchLabels:
                                                  additionalProperties:
                                                    type: "string"
                                                  type: "object"
                                              type: "object"
                                            namespaces:
                                              items:
                                                type: "string"
                                              type: "array"
                                            topologyKey:
                                              type: "string"
                                          type: "object"
                                        weight:
                                          type: "integer"
                                      type: "object"
                                    type: "array"
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                            matchLabels:
                                              additionalProperties:
                                                type: "string"
                                              type: "object"
                                          type: "object"
                                        matchLabelKeys:
                                          items:
                                            type: "string"
                                          type: "array"
                                        mismatchLabelKeys:
                                          items:
                                            type: "string"
                                          type: "array"
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                            matchLabels:
                                              additionalProperties:
                                                type: "string"
                                              type: "object"
                                          type: "object"
                                        namespaces:
                                          items:
                                            type: "string"
                                          type: "array"
                                        topologyKey:
                                          type: "string"
                                      type: "object"
                                    type: "array"
                                type: "object"
                              podAntiAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: "string"
                                                      operator:
                                                        type: "string"
                                                      values:
                                                        items:
                                                          type: "string"
                                                        type: "array"
                                                    type: "object"
                                                  type: "array"
                                                matchLabels:
                                                  additionalProperties:
                                                    type: "string"
                                                  type: "object"
                                              type: "object"
                                            matchLabelKeys:
                                              items:
                                                type: "string"
                                              type: "array"
                                            mismatchLabelKeys:
                                              items:
                                                type: "string"
                                              type: "array"
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: "string"
                                                      operator:
                                                        type: "string"
                                                      values:
                                                        items:
                                                          type: "string"
                                                        type: "array"
                                                    type: "object"
                                                  type: "array"
                                                matchLabels:
                                                  additionalProperties:
                                                    type: "string"
                                                  type: "object"
                                              type: "object"
                                            namespaces:
                                              items:
                                                type: "string"
                                              type: "array"
                                            topologyKey:
                                              type: "string"
                                          type: "object"
                                        weight:
                                          type: "integer"
                                      type: "object"
                                    type: "array"
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                            matchLabels:
                                              additionalProperties:
                                                type: "string"
                                              type: "object"
                                          type: "object"
                                        matchLabelKeys:
                                          items:
                                            type: "string"
                                          type: "array"
                                        mismatchLabelKeys:
                                          items:
                                            type: "string"
                                          type: "array"
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: "string"
                                                  operator:
                                                    type: "string"
                                                  values:
                                                    items:
                                                      type: "string"
                                                    type: "array"
                                                type: "object"
                                              type: "array"
                                            matchLabels:
                                              additionalProperties:
                                                type: "string"
                                              type: "object"
                                          type: "object"
                                        namespaces:
                                          items:
                                            type: "string"
                                          type: "array"
                                        topologyKey:
                                          type: "string"
                                      type: "object"
                                    type: "array"
                                type: "object"
                            type: "object"
                          automountServiceAccountToken:
                            type: "boolean"
                          containers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: "string"
                                  type: "array"
                                command:
                                  items:
                                    type: "string"
                                  type: "array"
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: "string"
                                      value:
                                        type: "string"
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: "string"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: "string"
                                              fieldPath:
                                                type: "string"
                                            type: "object"
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: "string"
                                              divisor:
                                                anyOf:
                                                - type: "integer"
                                                - type: "string"
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: "string"
                                            type: "object"
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: "string"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                        type: "object"
                                    type: "object"
                                  type: "array"
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: "string"
                                          optional:
                                            type: "boolean"
                                        type: "object"
                                      prefix:
                                        type: "string"
                                      secretRef:
                                        properties:
                                          name:
                                            type: "string"
                                          optional:
                                            type: "boolean"
                                        type: "object"
                                    type: "object"
                                  type: "array"
                                image:
                                  type: "string"
                                imagePullPolicy:
                                  type: "string"
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: "string"
                                              type: "array"
                                          type: "object"
                                        httpGet:
                                          properties:
                                            host:
                                              type: "string"
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: "string"
                                                  value:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            path:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: "string"
                                          type: "object"
                                        sleep:
                                          properties:
                                            seconds:
                                              type: "integer"
                                          type: "object"
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                          type: "object"
                                      type: "object"
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: "string"
                                              type: "array"
                                          type: "object"
                                        httpGet:
                                          properties:
                                            host:
                                              type: "string"
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: "string"
                                                  value:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            path:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: "string"
                                          type: "object"
                                        sleep:
                                          properties:
                                            seconds:
                                              type: "integer"
                                          type: "object"
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                          type: "object"
                                      type: "object"
                                  type: "object"
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                name:
                                  type: "string"
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: "integer"
                                      hostIP:
                                        type: "string"
                                      hostPort:
                                        type: "integer"
                                      name:
                                        type: "string"
                                      protocol:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: "string"
                                      restartPolicy:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: "string"
                                          request:
                                            type: "string"
                                        type: "object"
                                      type: "array"
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: "integer"
                                        - type: "string"
                                        x-kubernetes-int-or-string: true
                                      type: "object"
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: "integer"
                                        - type: "string"
                                        x-kubernetes-int-or-string: true
                                      type: "object"
                                  type: "object"
                                restartPolicy:
                                  type: "string"
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: "boolean"
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: "string"
                                        type:
                                          type: "string"
                                      type: "object"
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: "string"
                                          type: "array"
                                        drop:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    privileged:
                                      type: "boolean"
                                    procMount:
                                      type: "string"
                                    readOnlyRootFilesystem:
                                      type: "boolean"
                                    runAsGroup:
                                      type: "integer"
                                    runAsNonRoot:
                                      type: "boolean"
                                    runAsUser:
                                      type: "integer"
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: "string"
                                        role:
                                          type: "string"
                                        type:
                                          type: "string"
                                        user:
                                          type: "string"
                                      type: "object"
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: "string"
                                        type:
                                          type: "string"
                                      type: "object"
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: "string"
                                        gmsaCredentialSpecName:
                                          type: "string"
                                        hostProcess:
                                          type: "boolean"
                                        runAsUserName:
                                          type: "string"
                                      type: "object"
                                  type: "object"
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                stdin:
                                  type: "boolean"
                                stdinOnce:
                                  type: "boolean"
                                terminationMessagePath:
                                  type: "string"
                                terminationMessagePolicy:
                                  type: "string"
                                tty:
                                  type: "boolean"
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: "string"
                                      name:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: "string"
                                      mountPropagation:
                                        type: "string"
                                      name:
                                        type: "string"
                                      readOnly:
                                        type: "boolean"
                                      recursiveReadOnly:
                                        type: "string"
                                      subPath:
                                        type: "string"
                                      subPathExpr:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                workingDir:
                                  type: "string"
                              type: "object"
                            type: "array"
                          dnsConfig:
                            properties:
                              nameservers:
                                items:
                                  type: "string"
                                type: "array"
                              options:
                                items:
                                  properties:
                                    name:
                                      type: "string"
                                    value:
                                      type: "string"
                                  type: "object"
                                type: "array"
                              searches:
                                items:
                                  type: "string"
                                type: "array"
                            type: "object"
                          dnsPolicy:
                            type: "string"
                          enableServiceLinks:
                            type: "boolean"
                          ephemeralContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: "string"
                                  type: "array"
                                command:
                                  items:
                                    type: "string"
                                  type: "array"
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: "string"
                                      value:
                                        type: "string"
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: "string"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: "string"
                                              fieldPath:
                                                type: "string"
                                            type: "object"
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: "string"
                                              divisor:
                                                anyOf:
                                                - type: "integer"
                                                - type: "string"
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: "string"
                                            type: "object"
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: "string"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                        type: "object"
                                    type: "object"
                                  type: "array"
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: "string"
                                          optional:
                                            type: "boolean"
                                        type: "object"
                                      prefix:
                                        type: "string"
                                      secretRef:
                                        properties:
                                          name:
                                            type: "string"
                                          optional:
                                            type: "boolean"
                                        type: "object"
                                    type: "object"
                                  type: "array"
                                image:
                                  type: "string"
                                imagePullPolicy:
                                  type: "string"
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: "string"
                                              type: "array"
                                          type: "object"
                                        httpGet:
                                          properties:
                                            host:
                                              type: "string"
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: "string"
                                                  value:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            path:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: "string"
                                          type: "object"
                                        sleep:
                                          properties:
                                            seconds:
                                              type: "integer"
                                          type: "object"
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                          type: "object"
                                      type: "object"
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: "string"
                                              type: "array"
                                          type: "object"
                                        httpGet:
                                          properties:
                                            host:
                                              type: "string"
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: "string"
                                                  value:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            path:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: "string"
                                          type: "object"
                                        sleep:
                                          properties:
                                            seconds:
                                              type: "integer"
                                          type: "object"
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                          type: "object"
                                      type: "object"
                                  type: "object"
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                name:
                                  type: "string"
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: "integer"
                                      hostIP:
                                        type: "string"
                                      hostPort:
                                        type: "integer"
                                      name:
                                        type: "string"
                                      protocol:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: "string"
                                      restartPolicy:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: "string"
                                          request:
                                            type: "string"
                                        type: "object"
                                      type: "array"
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: "integer"
                                        - type: "string"
                                        x-kubernetes-int-or-string: true
                                      type: "object"
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: "integer"
                                        - type: "string"
                                        x-kubernetes-int-or-string: true
                                      type: "object"
                                  type: "object"
                                restartPolicy:
                                  type: "string"
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: "boolean"
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: "string"
                                        type:
                                          type: "string"
                                      type: "object"
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: "string"
                                          type: "array"
                                        drop:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    privileged:
                                      type: "boolean"
                                    procMount:
                                      type: "string"
                                    readOnlyRootFilesystem:
                                      type: "boolean"
                                    runAsGroup:
                                      type: "integer"
                                    runAsNonRoot:
                                      type: "boolean"
                                    runAsUser:
                                      type: "integer"
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: "string"
                                        role:
                                          type: "string"
                                        type:
                                          type: "string"
                                        user:
                                          type: "string"
                                      type: "object"
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: "string"
                                        type:
                                          type: "string"
                                      type: "object"
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: "string"
                                        gmsaCredentialSpecName:
                                          type: "string"
                                        hostProcess:
                                          type: "boolean"
                                        runAsUserName:
                                          type: "string"
                                      type: "object"
                                  type: "object"
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                stdin:
                                  type: "boolean"
                                stdinOnce:
                                  type: "boolean"
                                targetContainerName:
                                  type: "string"
                                terminationMessagePath:
                                  type: "string"
                                terminationMessagePolicy:
                                  type: "string"
                                tty:
                                  type: "boolean"
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: "string"
                                      name:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: "string"
                                      mountPropagation:
                                        type: "string"
                                      name:
                                        type: "string"
                                      readOnly:
                                        type: "boolean"
                                      recursiveReadOnly:
                                        type: "string"
                                      subPath:
                                        type: "string"
                                      subPathExpr:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                workingDir:
                                  type: "string"
                              type: "object"
                            type: "array"
                          hostAliases:
                            items:
                              properties:
                                hostnames:
                                  items:
                                    type: "string"
                                  type: "array"
                                ip:
                                  type: "string"
                              type: "object"
                            type: "array"
                          hostIPC:
                            type: "boolean"
                          hostNetwork:
                            type: "boolean"
                          hostPID:
                            type: "boolean"
                          hostUsers:
                            type: "boolean"
                          hostname:
                            type: "string"
                          imagePullSecrets:
                            items:
                              properties:
                                name:
                                  type: "string"
                              type: "object"
                            type: "array"
                          initContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: "string"
                                  type: "array"
                                command:
                                  items:
                                    type: "string"
                                  type: "array"
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: "string"
                                      value:
                                        type: "string"
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: "string"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: "string"
                                              fieldPath:
                                                type: "string"
                                            type: "object"
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: "string"
                                              divisor:
                                                anyOf:
                                                - type: "integer"
                                                - type: "string"
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: "string"
                                            type: "object"
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: "string"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                        type: "object"
                                    type: "object"
                                  type: "array"
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: "string"
                                          optional:
                                            type: "boolean"
                                        type: "object"
                                      prefix:
                                        type: "string"
                                      secretRef:
                                        properties:
                                          name:
                                            type: "string"
                                          optional:
                                            type: "boolean"
                                        type: "object"
                                    type: "object"
                                  type: "array"
                                image:
                                  type: "string"
                                imagePullPolicy:
                                  type: "string"
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: "string"
                                              type: "array"
                                          type: "object"
                                        httpGet:
                                          properties:
                                            host:
                                              type: "string"
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: "string"
                                                  value:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            path:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: "string"
                                          type: "object"
                                        sleep:
                                          properties:
                                            seconds:
                                              type: "integer"
                                          type: "object"
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                          type: "object"
                                      type: "object"
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: "string"
                                              type: "array"
                                          type: "object"
                                        httpGet:
                                          properties:
                                            host:
                                              type: "string"
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: "string"
                                                  value:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            path:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: "string"
                                          type: "object"
                                        sleep:
                                          properties:
                                            seconds:
                                              type: "integer"
                                          type: "object"
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: "string"
                                            port:
                                              anyOf:
                                              - type: "integer"
                                              - type: "string"
                                              x-kubernetes-int-or-string: true
                                          type: "object"
                                      type: "object"
                                  type: "object"
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                name:
                                  type: "string"
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        type: "integer"
                                      hostIP:
                                        type: "string"
                                      hostPort:
                                        type: "integer"
                                      name:
                                        type: "string"
                                      protocol:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: "string"
                                      restartPolicy:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: "string"
                                          request:
                                            type: "string"
                                        type: "object"
                                      type: "array"
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: "integer"
                                        - type: "string"
                                        x-kubernetes-int-or-string: true
                                      type: "object"
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: "integer"
                                        - type: "string"
                                        x-kubernetes-int-or-string: true
                                      type: "object"
                                  type: "object"
                                restartPolicy:
                                  type: "string"
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: "boolean"
                                    appArmorProfile:
                                      properties:
                                        localhostProfile:
                                          type: "string"
                                        type:
                                          type: "string"
                                      type: "object"
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: "string"
                                          type: "array"
                                        drop:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    privileged:
                                      type: "boolean"
                                    procMount:
                                      type: "string"
                                    readOnlyRootFilesystem:
                                      type: "boolean"
                                    runAsGroup:
                                      type: "integer"
                                    runAsNonRoot:
                                      type: "boolean"
                                    runAsUser:
                                      type: "integer"
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: "string"
                                        role:
                                          type: "string"
                                        type:
                                          type: "string"
                                        user:
                                          type: "string"
                                      type: "object"
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: "string"
                                        type:
                                          type: "string"
                                      type: "object"
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: "string"
                                        gmsaCredentialSpecName:
                                          type: "string"
                                        hostProcess:
                                          type: "boolean"
                                        runAsUserName:
                                          type: "string"
                                      type: "object"
                                  type: "object"
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: "string"
                                          type: "array"
                                      type: "object"
                                    failureThreshold:
                                      type: "integer"
                                    grpc:
                                      properties:
                                        port:
                                          type: "integer"
                                        service:
                                          type: "string"
                                      type: "object"
                                    httpGet:
                                      properties:
                                        host:
                                          type: "string"
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: "string"
                                              value:
                                                type: "string"
                                            type: "object"
                                          type: "array"
                                        path:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: "string"
                                      type: "object"
                                    initialDelaySeconds:
                                      type: "integer"
                                    periodSeconds:
                                      type: "integer"
                                    successThreshold:
                                      type: "integer"
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: "string"
                                        port:
                                          anyOf:
                                          - type: "integer"
                                          - type: "string"
                                          x-kubernetes-int-or-string: true
                                      type: "object"
                                    terminationGracePeriodSeconds:
                                      type: "integer"
                                    timeoutSeconds:
                                      type: "integer"
                                  type: "object"
                                stdin:
                                  type: "boolean"
                                stdinOnce:
                                  type: "boolean"
                                terminationMessagePath:
                                  type: "string"
                                terminationMessagePolicy:
                                  type: "string"
                                tty:
                                  type: "boolean"
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: "string"
                                      name:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: "string"
                                      mountPropagation:
                                        type: "string"
                                      name:
                                        type: "string"
                                      readOnly:
                                        type: "boolean"
                                      recursiveReadOnly:
                                        type: "string"
                                      subPath:
                                        type: "string"
                                      subPathExpr:
                                        type: "string"
                                    type: "object"
                                  type: "array"
                                workingDir:
                                  type: "string"
                              type: "object"
                            type: "array"
                          nodeName:
                            type: "string"
                          nodeSelector:
                            additionalProperties:
                              type: "string"
                            type: "object"
                          os:
                            properties:
                              name:
                                type: "string"
                            type: "object"
                          overhead:
                            additionalProperties:
                              anyOf:
                              - type: "integer"
                              - type: "string"
                              x-kubernetes-int-or-string: true
                            type: "object"
                          preemptionPolicy:
                            type: "string"
                          priority:
                            type: "integer"
                          priorityClassName:
                            type: "string"
                          readinessGates:
                            items:
                              properties:
                                conditionType:
                                  type: "string"
                              type: "object"
                            type: "array"
                          resourceClaims:
                            items:
                              properties:
                                name:
                                  type: "string"
                                resourceClaimName:
                                  type: "string"
                                resourceClaimTemplateName:
                                  type: "string"
                              type: "object"
                            type: "array"
                          resources:
                            properties:
                              claims:
                                items:
                                  properties:
                                    name:
                                      type: "string"
                                    request:
                                      type: "string"
                                  type: "object"
                                type: "array"
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: "integer"
                                  - type: "string"
                                  x-kubernetes-int-or-string: true
                                type: "object"
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: "integer"
                                  - type: "string"
                                  x-kubernetes-int-or-string: true
                                type: "object"
                            type: "object"
                          restartPolicy:
                            type: "string"
                          runtimeClassName:
                            type: "string"
                          schedulerName:
                            type: "string"
                          schedulingGates:
                            items:
                              properties:
                                name:
                                  type: "string"
                              type: "object"
                            type: "array"
                          securityContext:
                            properties:
                              appArmorProfile:
                                properties:
                                  localhostProfile:
                                    type: "string"
                                  type:
                                    type: "string"
                                type: "object"
                              fsGroup:
                                type: "integer"
                              fsGroupChangePolicy:
                                type: "string"
                              runAsGroup:
                                type: "integer"
                              runAsNonRoot:
                                type: "boolean"
                              runAsUser:
                                type: "integer"
                              seLinuxChangePolicy:
                                type: "string"
                              seLinuxOptions:
                                properties:
                                  level:
                                    type: "string"
                                  role:
                                    type: "string"
                                  type:
                                    type: "string"
                                  user:
                                    type: "string"
                                type: "object"
                              seccompProfile:
                                properties:
                                  localhostProfile:
                                    type: "string"
                                  type:
                                    type: "string"
                                type: "object"
                              supplementalGroups:
                                items:
                                  type: "integer"
                                type: "array"
                              supplementalGroupsPolicy:
                                type: "string"
                              sysctls:
                                items:
                                  properties:
                                    name:
                                      type: "string"
                                    value:
                                      type: "string"
                                  type: "object"
                                type: "array"
                              windowsOptions:
                                properties:
                                  gmsaCredentialSpec:
                                    type: "string"
                                  gmsaCredentialSpecName:
                                    type: "string"
                                  hostProcess:
                                    type: "boolean"
                                  runAsUserName:
                                    type: "string"
                                type: "object"
                            type: "object"
                          serviceAccount:
                            type: "string"
                          serviceAccountName:
                            type: "string"
                          setHostnameAsFQDN:
                            type: "boolean"
                          shareProcessNamespace:
                            type: "boolean"
                          subdomain:
                            type: "string"
                          terminationGracePeriodSeconds:
                            type: "integer"
                          tolerations:
                            items:
                              properties:
                                effect:
                                  type: "string"
                                key:
                                  type: "string"
                                operator:
                                  type: "string"
                                tolerationSeconds:
                                  type: "integer"
                                value:
                                  type: "string"
                              type: "object"
                            type: "array"
                          topologySpreadConstraints:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          operator:
                                            type: "string"
                                          values:
                                            items:
                                              type: "string"
                                            type: "array"
                                        type: "object"
                                      type: "array"
                                    matchLabels:
                                      additionalProperties:
                                        type: "string"
                                      type: "object"
                                  type: "object"
                                matchLabelKeys:
                                  items:
                                    type: "string"
                                  type: "array"
                                maxSkew:
                                  type: "integer"
                                minDomains:
                                  type: "integer"
                                nodeAffinityPolicy:
                                  type: "string"
                                nodeTaintsPolicy:
                                  type: "string"
                                topologyKey:
                                  type: "string"
                                whenUnsatisfiable:
                                  type: "string"
                              type: "object"
                            type: "array"
                          volumes:
                            items:
                              properties:
                                awsElasticBlockStore:
                                  properties:
                                    fsType:
                                      type: "string"
                                    partition:
                                      type: "integer"
                                    readOnly:
                                      type: "boolean"
                                    volumeID:
                                      type: "string"
                                  type: "object"
                                azureDisk:
                                  properties:
                                    cachingMode:
                                      type: "string"
                                    diskName:
                                      type: "string"
                                    diskURI:
                                      type: "string"
                                    fsType:
                                      type: "string"
                                    kind:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                  type: "object"
                                azureFile:
                                  properties:
                                    readOnly:
                                      type: "boolean"
                                    secretName:
                                      type: "string"
                                    shareName:
                                      type: "string"
                                  type: "object"
                                cephfs:
                                  properties:
                                    monitors:
                                      items:
                                        type: "string"
                                      type: "array"
                                    path:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    secretFile:
                                      type: "string"
                                    secretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                    user:
                                      type: "string"
                                  type: "object"
                                cinder:
                                  properties:
                                    fsType:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    secretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                    volumeID:
                                      type: "string"
                                  type: "object"
                                configMap:
                                  properties:
                                    defaultMode:
                                      type: "integer"
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          mode:
                                            type: "integer"
                                          path:
                                            type: "string"
                                        type: "object"
                                      type: "array"
                                    name:
                                      type: "string"
                                    optional:
                                      type: "boolean"
                                  type: "object"
                                csi:
                                  properties:
                                    driver:
                                      type: "string"
                                    fsType:
                                      type: "string"
                                    nodePublishSecretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                    readOnly:
                                      type: "boolean"
                                    volumeAttributes:
                                      additionalProperties:
                                        type: "string"
                                      type: "object"
                                  type: "object"
                                downwardAPI:
                                  properties:
                                    defaultMode:
                                      type: "integer"
                                    items:
                                      items:
                                        properties:
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: "string"
                                              fieldPath:
                                                type: "string"
                                            type: "object"
                                          mode:
                                            type: "integer"
                                          path:
                                            type: "string"
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: "string"
                                              divisor:
                                                anyOf:
                                                - type: "integer"
                                                - type: "string"
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: "string"
                                            type: "object"
                                        type: "object"
                                      type: "array"
                                  type: "object"
                                emptyDir:
                                  properties:
                                    medium:
                                      type: "string"
                                    sizeLimit:
                                      anyOf:
                                      - type: "integer"
                                      - type: "string"
                                      x-kubernetes-int-or-string: true
                                  type: "object"
                                ephemeral:
                                  properties:
                                    volumeClaimTemplate:
                                      properties:
                                        metadata:
                                          properties:
                                            annotations:
                                              additionalProperties:
                                                type: "string"
                                              type: "object"
                                            creationTimestamp:
                                              type: "string"
                                            deletionGracePeriodSeconds:
                                              type: "integer"
                                            deletionTimestamp:
                                              type: "string"
                                            finalizers:
                                              items:
                                                type: "string"
                                              type: "array"
                                            generateName:
                                              type: "string"
                                            generation:
                                              type: "integer"
                                            labels:
                                              additionalProperties:
                                                type: "string"
                                              type: "object"
                                            managedFields:
                                              items:
                                                properties:
                                                  apiVersion:
                                                    type: "string"
                                                  fieldsType:
                                                    type: "string"
                                                  fieldsV1:
                                                    type: "object"
                                                  manager:
                                                    type: "string"
                                                  operation:
                                                    type: "string"
                                                  subresource:
                                                    type: "string"
                                                  time:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            name:
                                              type: "string"
                                            namespace:
                                              type: "string"
                                            ownerReferences:
                                              items:
                                                properties:
                                                  apiVersion:
                                                    type: "string"
                                                  blockOwnerDeletion:
                                                    type: "boolean"
                                                  controller:
                                                    type: "boolean"
                                                  kind:
                                                    type: "string"
                                                  name:
                                                    type: "string"
                                                  uid:
                                                    type: "string"
                                                type: "object"
                                              type: "array"
                                            resourceVersion:
                                              type: "string"
                                            selfLink:
                                              type: "string"
                                            uid:
                                              type: "string"
                                          type: "object"
                                        spec:
                                          properties:
                                            accessModes:
                                              items:
                                                type: "string"
                                              type: "array"
                                            dataSource:
                                              properties:
                                                apiGroup:
                                                  type: "string"
                                                kind:
                                                  type: "string"
                                                name:
                                                  type: "string"
                                              type: "object"
                                            dataSourceRef:
                                              properties:
                                                apiGroup:
                                                  type: "string"
                                                kind:
                                                  type: "string"
                                                name:
                                                  type: "string"
                                                namespace:
                                                  type: "string"
                                              type: "object"
                                            resources:
                                              properties:
                                                limits:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: "integer"
                                                    - type: "string"
                                                    x-kubernetes-int-or-string: true
                                                  type: "object"
                                                requests:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: "integer"
                                                    - type: "string"
                                                    x-kubernetes-int-or-string: true
                                                  type: "object"
                                              type: "object"
                                            selector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: "string"
                                                      operator:
                                                        type: "string"
                                                      values:
                                                        items:
                                                          type: "string"
                                                        type: "array"
                                                    type: "object"
                                                  type: "array"
                                                matchLabels:
                                                  additionalProperties:
                                                    type: "string"
                                                  type: "object"
                                              type: "object"
                                            storageClassName:
                                              type: "string"
                                            volumeAttributesClassName:
                                              type: "string"
                                            volumeMode:
                                              type: "string"
                                            volumeName:
                                              type: "string"
                                          type: "object"
                                      type: "object"
                                  type: "object"
                                fc:
                                  properties:
                                    fsType:
                                      type: "string"
                                    lun:
                                      type: "integer"
                                    readOnly:
                                      type: "boolean"
                                    targetWWNs:
                                      items:
                                        type: "string"
                                      type: "array"
                                    wwids:
                                      items:
                                        type: "string"
                                      type: "array"
                                  type: "object"
                                flexVolume:
                                  properties:
                                    driver:
                                      type: "string"
                                    fsType:
                                      type: "string"
                                    options:
                                      additionalProperties:
                                        type: "string"
                                      type: "object"
                                    readOnly:
                                      type: "boolean"
                                    secretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                  type: "object"
                                flocker:
                                  properties:
                                    datasetName:
                                      type: "string"
                                    datasetUUID:
                                      type: "string"
                                  type: "object"
                                gcePersistentDisk:
                                  properties:
                                    fsType:
                                      type: "string"
                                    partition:
                                      type: "integer"
                                    pdName:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                  type: "object"
                                gitRepo:
                                  properties:
                                    directory:
                                      type: "string"
                                    repository:
                                      type: "string"
                                    revision:
                                      type: "string"
                                  type: "object"
                                glusterfs:
                                  properties:
                                    endpoints:
                                      type: "string"
                                    path:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                  type: "object"
                                hostPath:
                                  properties:
                                    path:
                                      type: "string"
                                    type:
                                      type: "string"
                                  type: "object"
                                image:
                                  properties:
                                    pullPolicy:
                                      type: "string"
                                    reference:
                                      type: "string"
                                  type: "object"
                                iscsi:
                                  properties:
                                    chapAuthDiscovery:
                                      type: "boolean"
                                    chapAuthSession:
                                      type: "boolean"
                                    fsType:
                                      type: "string"
                                    initiatorName:
                                      type: "string"
                                    iqn:
                                      type: "string"
                                    iscsiInterface:
                                      type: "string"
                                    lun:
                                      type: "integer"
                                    portals:
                                      items:
                                        type: "string"
                                      type: "array"
                                    readOnly:
                                      type: "boolean"
                                    secretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                    targetPortal:
                                      type: "string"
                                  type: "object"
                                name:
                                  type: "string"
                                nfs:
                                  properties:
                                    path:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    server:
                                      type: "string"
                                  type: "object"
                                persistentVolumeClaim:
                                  properties:
                                    claimName:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                  type: "object"
                                photonPersistentDisk:
                                  properties:
                                    fsType:
                                      type: "string"
                                    pdID:
                                      type: "string"
                                  type: "object"
                                portworxVolume:
                                  properties:
                                    fsType:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    volumeID:
                                      type: "string"
                                  type: "object"
                                projected:
                                  properties:
                                    defaultMode:
                                      type: "integer"
                                    sources:
                                      items:
                                        properties:
                                          clusterTrustBundle:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: "string"
                                                        operator:
                                                          type: "string"
                                                        values:
                                                          items:
                                                            type: "string"
                                                          type: "array"
                                                      type: "object"
                                                    type: "array"
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: "string"
                                                    type: "object"
                                                type: "object"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                              path:
                                                type: "string"
                                              signerName:
                                                type: "string"
                                            type: "object"
                                          configMap:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: "string"
                                                    mode:
                                                      type: "integer"
                                                    path:
                                                      type: "string"
                                                  type: "object"
                                                type: "array"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                          downwardAPI:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    fieldRef:
                                                      properties:
                                                        apiVersion:
                                                          type: "string"
                                                        fieldPath:
                                                          type: "string"
                                                      type: "object"
                                                    mode:
                                                      type: "integer"
                                                    path:
                                                      type: "string"
                                                    resourceFieldRef:
                                                      properties:
                                                        containerName:
                                                          type: "string"
                                                        divisor:
                                                          anyOf:
                                                          - type: "integer"
                                                          - type: "string"
                                                          x-kubernetes-int-or-string: true
                                                        resource:
                                                          type: "string"
                                                      type: "object"
                                                  type: "object"
                                                type: "array"
                                            type: "object"
                                          secret:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: "string"
                                                    mode:
                                                      type: "integer"
                                                    path:
                                                      type: "string"
                                                  type: "object"
                                                type: "array"
                                              name:
                                                type: "string"
                                              optional:
                                                type: "boolean"
                                            type: "object"
                                          serviceAccountToken:
                                            properties:
                                              audience:
                                                type: "string"
                                              expirationSeconds:
                                                type: "integer"
                                              path:
                                                type: "string"
                                            type: "object"
                                        type: "object"
                                      type: "array"
                                  type: "object"
                                quobyte:
                                  properties:
                                    group:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    registry:
                                      type: "string"
                                    tenant:
                                      type: "string"
                                    user:
                                      type: "string"
                                    volume:
                                      type: "string"
                                  type: "object"
                                rbd:
                                  properties:
                                    fsType:
                                      type: "string"
                                    image:
                                      type: "string"
                                    keyring:
                                      type: "string"
                                    monitors:
                                      items:
                                        type: "string"
                                      type: "array"
                                    pool:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    secretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                    user:
                                      type: "string"
                                  type: "object"
                                scaleIO:
                                  properties:
                                    fsType:
                                      type: "string"
                                    gateway:
                                      type: "string"
                                    protectionDomain:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    secretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                    sslEnabled:
                                      type: "boolean"
                                    storageMode:
                                      type: "string"
                                    storagePool:
                                      type: "string"
                                    system:
                                      type: "string"
                                    volumeName:
                                      type: "string"
                                  type: "object"
                                secret:
                                  properties:
                                    defaultMode:
                                      type: "integer"
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: "string"
                                          mode:
                                            type: "integer"
                                          path:
                                            type: "string"
                                        type: "object"
                                      type: "array"
                                    optional:
                                      type: "boolean"
                                    secretName:
                                      type: "string"
                                  type: "object"
                                storageos:
                                  properties:
                                    fsType:
                                      type: "string"
                                    readOnly:
                                      type: "boolean"
                                    secretRef:
                                      properties:
                                        name:
                                          type: "string"
                                      type: "object"
                                    volumeName:
                                      type: "string"
                                    volumeNamespace:
                                      type: "string"
                                  type: "object"
                                vsphereVolume:
                                  properties:
                                    fsType:
                                      type: "string"
                                    storagePolicyID:
                                      type: "string"
                                    storagePolicyName:
                                      type: "string"
                                    volumePath:
                                      type: "string"
                                  type: "object"
                              type: "object"
                            type: "array"
                        type: "object"
                    type: "object"
                type: "object"
              update:
                description: "Configuration related to Keycloak deployment updates."
                properties:
                  revision:
                    description: "When use the Explicit strategy, the revision signals\
                      \ if a rolling update can be used or not."
                    type: "string"
                  strategy:
                    default: "RecreateOnImageChange"
                    description: "Sets the update strategy to use."
                    enum:
                    - "Auto"
                    - "Explicit"
                    - "RecreateOnImageChange"
                    type: "string"
                type: "object"
                x-kubernetes-validations:
                - message: "The 'revision' field is required when 'Explicit' strategy\
                    \ is used"
                  rule: "self.strategy != 'Explicit' || has(self.revision)"
            type: "object"
          status:
            properties:
              conditions:
                items:
                  properties:
                    lastTransitionTime:
                      type: "string"
                    message:
                      type: "string"
                    observedGeneration:
                      type: "integer"
                    status:
                      type: "string"
                    type:
                      type: "string"
                  type: "object"
                type: "array"
              instances:
                type: "integer"
              observedGeneration:
                type: "integer"
              selector:
                type: "string"
            type: "object"
        type: "object"
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: ".status.selector"
        specReplicasPath: ".spec.instances"
        statusReplicasPath: ".status.instances"
      status: {}