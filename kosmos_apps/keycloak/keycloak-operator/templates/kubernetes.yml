---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    app.quarkus.io/quarkus-version: 3.20.0
    app.quarkus.io/vcs-uri: https://github.com/keycloak/keycloak.git
    app.quarkus.io/build-timestamp: 2025-05-08 - 09:14:51 +0000
  labels:
    app.kubernetes.io/name: keycloak-operator
    app.kubernetes.io/version: 26.2.4
    app.kubernetes.io/managed-by: quarkus
  name: {{ include "keycloak-operator.fullname" . }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "keycloak-operator.fullname" . }}-clusterrole
rules:
  - apiGroups:
      - config.openshift.io
    resources:
      - ingresses
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    app.kubernetes.io/version: 26.2.4
  name: keycloakrealmimportcontroller-cluster-role
rules:
  - apiGroups:
      - k8s.keycloak.org
    resources:
      - keycloakrealmimports
      - keycloakrealmimports/status
      - keycloakrealmimports/finalizers
    verbs:
      - get
      - list
      - watch
      - patch
      - update
      - create
      - delete
  - apiGroups:
      - batch
    resources:
      - jobs
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - watch
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    app.kubernetes.io/version: 26.2.4
  name: keycloakcontroller-cluster-role
rules:
  - apiGroups:
      - k8s.keycloak.org
    resources:
      - keycloaks
      - keycloaks/status
      - keycloaks/finalizers
    verbs:
      - get
      - list
      - watch
      - patch
      - update
      - create
      - delete
  - apiGroups:
      - ""
    resources:
      - services
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - apps
    resources:
      - statefulsets
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - networking.k8s.io
    resources:
      - ingresses
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - create
      - delete
      - get
      - list
      - watch
  - apiGroups:
      - networking.k8s.io
    resources:
      - networkpolicies
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
  name: {{ include "keycloak-operator.fullname" . }}-clusterrole-binding
roleRef:
  kind: ClusterRole
  apiGroup: rbac.authorization.k8s.io
  name: {{ include "keycloak-operator.fullname" . }}-clusterrole
subjects:
  - kind: ServiceAccount
    name: keycloak-operator
    namespace: {{.Release.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "keycloak-operator.fullname" . }}-role
rules:
  - apiGroups:
      - apps
    resources:
      - statefulsets
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - secrets
      - services
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - list
  - apiGroups:
      - batch
    resources:
      - jobs
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - networking.k8s.io
    resources:
      - ingresses
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - patch
      - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
  name: {{ include "keycloak-operator.fullname" . }}-role-binding
roleRef:
  kind: Role
  apiGroup: rbac.authorization.k8s.io
  name: {{ include "keycloak-operator.fullname" . }}-role
subjects:
  - kind: ServiceAccount
    name: {{ include "keycloak-operator.fullname" . }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    app.kubernetes.io/version: 26.2.4
  name: keycloakrealmimportcontroller-role-binding
roleRef:
  kind: ClusterRole
  apiGroup: rbac.authorization.k8s.io
  name: keycloakrealmimportcontroller-cluster-role
subjects:
  - kind: ServiceAccount
    name: {{ include "keycloak-operator.fullname" . }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    app.kubernetes.io/version: 26.2.4
  name: keycloakcontroller-role-binding
roleRef:
  kind: ClusterRole
  apiGroup: rbac.authorization.k8s.io
  name: keycloakcontroller-cluster-role
subjects:
  - kind: ServiceAccount
    name: {{ include "keycloak-operator.fullname" . }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    app.kubernetes.io/version: 26.2.4
  name: {{ include "keycloak-operator.fullname" . }}-view
roleRef:
  kind: ClusterRole
  apiGroup: rbac.authorization.k8s.io
  name: view
subjects:
  - kind: ServiceAccount
    name: {{ include "keycloak-operator.fullname" . }}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    app.quarkus.io/quarkus-version: 3.20.0
    app.quarkus.io/vcs-uri: https://github.com/keycloak/keycloak.git
    app.quarkus.io/build-timestamp: 2025-05-08 - 09:14:51 +0000
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    app.kubernetes.io/version: 26.2.4
    app.kubernetes.io/managed-by: quarkus
  name: {{ include "keycloak-operator.fullname" . }}
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8080
  selector:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    app.quarkus.io/quarkus-version: 3.20.0
    app.quarkus.io/vcs-uri: https://github.com/keycloak/keycloak.git
    app.quarkus.io/build-timestamp: 2025-05-08 - 09:14:51 +0000
  labels:
    app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    app.kubernetes.io/version: 26.2.4
    app.kubernetes.io/managed-by: quarkus
  name: {{ include "keycloak-operator.fullname" . }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
  template:
    metadata:
      annotations:
        app.quarkus.io/quarkus-version: 3.20.0
        app.quarkus.io/vcs-uri: https://github.com/keycloak/keycloak.git
        app.quarkus.io/build-timestamp: 2025-05-08 - 09:14:51 +0000
      labels:
        app.kubernetes.io/managed-by: quarkus
        app.kubernetes.io/version: 26.2.4
        app.kubernetes.io/name: {{ include "keycloak-operator.fullname" . }}
    spec:
      containers:
        - env:
            - name: KUBERNETES_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: RELATED_IMAGE_KEYCLOAK
              value: quay.io/keycloak/keycloak:26.2.4
          image: quay.io/keycloak/keycloak-operator:26.2.4
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /q/health/live
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          name: keycloak-operator
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /q/health/ready
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          resources:
            limits:
              cpu: 700m
              memory: 450Mi
            requests:
              cpu: 300m
              memory: 450Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
          startupProbe:
            failureThreshold: 3
            httpGet:
              path: /q/health/started
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
      serviceAccountName: {{ include "keycloak-operator.fullname" . }}