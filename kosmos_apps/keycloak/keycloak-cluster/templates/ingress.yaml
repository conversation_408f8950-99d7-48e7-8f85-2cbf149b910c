---
kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: "{{ .Values.clustername }}-ingress"
  labels:
    app: keycloak
    app.kubernetes.io/instance: "{{ .Values.clustername }}"
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    {{- /* the tpl allow templating in the annotations using .Values */ -}}
    {{- with .Values.ingress.annotations -}}
    {{- range $key, $value := .  }}
    {{ $key }}: {{ tpl $value $ | quote  }}
    {{- end }}
    {{- end }}
spec:
  defaultBackend:
    service:
      name: "{{ .Values.clustername }}-service"
      port:
        number: 443
  tls:
    - hosts:
        - "{{ .Values.hostname }}.{{ .Values.domain }}"
      secretName: "{{ .Values.clustername }}-tls-secret"
  rules:
    - host: "{{ .Values.hostname }}.{{ .Values.domain }}"
      http:
        paths:
          - pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ .Values.clustername }}-service"
                port:
                  number: 443
