1. Get the application URL by running following command:

  kubectl -n {{ .Release.Namespace }} get ingress

    NOTE: It may take a few seconds for the keycloak portal to be available.

2. Get keycloak initial admin (named "temp-admin" as of Keycloak 26) password by running following command :

  kubectl -n {{ .Release.Namespace }} get secret {{ .Values.clustername }}-initial-admin -o jsonpath='{.data.password}' | base64 --decode ; echo
