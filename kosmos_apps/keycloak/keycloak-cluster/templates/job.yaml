apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "keycloak-cluster.name" . }}-readiness-job
  labels:
    {{- include "keycloak-cluster.labels" . | nindent 4 }}
spec:
  template:
    metadata:
      name: {{ include "keycloak-cluster.name" . }}-readiness-job
      {{- with .Values.clusterReadinessJob.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "keycloak-cluster.labels" . | nindent 8 }}
          {{- with .Values.clusterReadinessJob.podLabels }}
          {{- toYaml . | nindent 8 }}
          {{- end }}
    spec:
      {{- with .Values.clusterReadinessJob.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      #serviceAccountName: {{ include "keycloak-cluster.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.clusterReadinessJob.podSecurityContext | nindent 8 }}
      restartPolicy: {{ .Values.clusterReadinessJob.restartPolicy }}
      containers:
        - name: {{ include "keycloak-cluster.name" . }}-readiness-job
          image: "{{ .Values.clusterReadinessJob.image.registry | trimSuffix "/"}}/{{ .Values.clusterReadinessJob.image.name }}:{{ .Values.clusterReadinessJob.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.clusterReadinessJob.image.pullPolicy }}
          command:
            - "/bin/sh"
            - "-c"
            - |
              KC_HOST="{{ .Values.clustername }}-service"
              echo "Waiting for the cluster '{{ .Values.clustername }}' to be up and running"
              until wget -T 3 http://$KC_HOST.kosmos-iam.svc.cluster.local/admin/master/console -O /tmp/index.html
              do
                echo
                echo "The cluster '{{ .Values.clustername }}' is not yet available"
                sleep 5
              done
              echo
              echo "The cluster is up and running"
              echo
              exit 0
          resources:
            {{- toYaml .Values.clusterReadinessJob.resources | nindent 12 }}
          securityContext:
            {{- toYaml .Values.clusterReadinessJob.securityContext | nindent 12 }}
      {{- with .Values.clusterReadinessJob.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.clusterReadinessJob.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.clusterReadinessJob.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
