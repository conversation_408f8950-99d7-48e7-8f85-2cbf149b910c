apiVersion: k8s.keycloak.org/v2alpha1
kind: Keycloak
metadata:
  name: {{ .Values.clustername }}
spec:
  instances: {{ .Values.instances }}
  unsupported:
    podTemplate:
      spec:
        affinity:
          {{- toYaml .Values.affinity | nindent 10 }}
        containers:
        - securityContext:
            capabilities:
              drop:
                - ALL
            runAsNonRoot: true
            allowPrivilegeEscalation: false
            seccompProfile:
              type: RuntimeDefault
  resources:
    {{- toYaml .Values.resources | nindent 4 }}
  hostname:
    hostname: "{{ .Values.hostname }}.{{ .Values.domain }}"
  http:
    tlsSecret: {{ .Values.clustername }}-tls-secret
    httpEnabled: true
    httpPort: 80
    httpsPort: 443
  ingress:
    enabled: false
  db:
    vendor: {{ .Values.db.vendor }}
    host: {{ .Values.db.host }}
    database: {{ .Values.db.name }}
    poolMinSize: {{ .Values.db.poolMinSize }}
    poolInitialSize: {{ .Values.db.poolInitialSize }}
    poolMaxSize: {{ .Values.db.poolMaxSize }}
    usernameSecret:
      name: {{ .Values.db.secret }}
      key: username
    passwordSecret:
      name: {{ .Values.db.secret }}
      key: password
  additionalOptions:
    - name: PROXY_ADDRESS_FORWARDING
      value: "true"
    - name: HOSTNAME_STRICT_HTTPS
      value: "false"
    - name: PROXY
      value: none
    - name: TLS_HOSTNAME_VERIFIER
      value: ANY
    - name: log_level
      value: org.keycloak.events:DEBUG
    - name: log_console_output
      value: json
{{- range $key, $value := .Values.extraOptions }}
    - name: "{{ $key }}"
      value: "{{ $value }}"
{{- end }}

{{- if .Values.truststores }}
  truststores:
    {{- range $key, $value := .Values.truststores }}
    {{ $key }}:
      secret:
        name: {{ $key }}-kc-truststore
    {{- end }}
{{- end }}
