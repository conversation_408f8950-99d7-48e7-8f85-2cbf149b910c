instances: 1
hostname: auth
domain: kosmos.athea
clustername: keycloak-cluster
resources:
  requests:
    cpu: 250m
    memory: 512Mi
  limits:
    cpu: 2000m
    memory: 1536Mi
affinity: {}
db:
  vendor: postgres
  host: pgcluster-rw.kosmos-sql
  name: iam
  username: athea
  password: mysuperpass
  secret: keycloak-db-secret
  poolMinSize: 30
  poolInitialSize: 30
  poolMaxSize: 100
ingress:
  annotations:
    cert-manager.io/cluster-issuer: kosmos-ca-issuer
clusterReadinessJob:
  image:
    registry: docker.io
    name: busybox
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: "1.37.0-glibc"
  imagePullSecrets: []
  restartPolicy: OnFailure
  serviceAccount:
    create: false
    automount: true
    annotations: {}
    name: ""
  podAnnotations: {}
  podLabels: {}
  podSecurityContext:
    runAsUser: 1000
    fsGroup: 2000
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 128Mi
  nodeSelector: {}
  tolerations: []
  affinity: {}
truststores: {}
extraOptions: {}
