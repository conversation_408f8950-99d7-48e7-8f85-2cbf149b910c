{"private": true, "name": "kosmos-studio", "version": "1.0.0", "packageManager": "pnpm@10.13.1", "workspaces": ["apps/front"], "devDependencies": {"husky": "^9.1.7", "lint-staged": "^15.5.1"}, "scripts": {"build:front": "pnpm -F front build", "compose:down": "docker compose -f docker-compose.dev.yml down", "compose:restart": "docker compose -f docker-compose.dev.yml down -v && docker compose -f docker-compose.dev.yml up", "compose:up": "docker compose -f docker-compose.dev.yml up", "dev:back": ". apps/backend/.env;cd apps/backend;go fmt ./...;go vet ./...;go run -mod vendor main.go", "dev:front": "pnpm -F front dev", "docker:run": "docker run --rm -p 3000:8080 -v ~/Documents/kubeconfig/scaleway/prs.yaml:/kubeconfig -e KUBECONFIG=/kubeconfig registry.localhost:5000/kosmos-studio:dev", "lint:front": "pnpm -F front lint", "prepare": "husky", "storybook": "pnpm -F front storybook", "typecheck:front": "pnpm -F front typecheck"}, "lint-staged": {"apps/front/src/**/*.{js,ts,tsx}": ["pnpm -F front lint:fix", "pnpm -F front vitest related --run"], "apps/front/src/**/*.{ts,tsx}": ["pnpm -F front typecheck"]}, "pnpm": {"overrides": {"prismjs": "^1.30.0"}}}