ARG DOCKERHUB_MIRROR="docker.io" # use private mirror only in CI
ARG GOOGLEHUB_MIRROR="gcr.io"
ARG GO_VERSION="1.24.0"
ARG RELEASE="dev"
ARG SHORT_COMMIT_TAG=""

FROM ${DOCKERHUB_MIRROR}/alpine AS truststore
ARG ATHEA_CA_STORE=""
RUN [[ ! -z ${ATHEA_CA_STORE+z} ]] && wget --no-check-certificate ${ATHEA_CA_STORE}/ca_si_root.crt && mv ca_si_root.crt /tmp/ca.crt || touch /tmp/ca.crt
RUN [[ ! -z ${ATHEA_CA_STORE+z} ]] && wget --no-check-certificate ${ATHEA_CA_STORE}/ca_si_signing.crt && cat ca_si_signing.crt >> /tmp/ca.crt || touch /tmp/ca.crt

FROM ${DOCKERHUB_MIRROR}/golang:${GO_VERSION} AS build-backend
ARG GO_VERSION
ARG RELEASE
ARG SHORT_COMMIT_TAG

WORKDIR /app

COPY apps/backend/vendor/ vendor/
COPY apps/backend/internal/ internal/
COPY apps/backend/pkg/ pkg/
COPY apps/backend/main.go main.go
COPY apps/backend/go.mod go.mod
COPY apps/backend/go.sum go.sum

RUN CGO_ENABLED=0\
    GOOS=linux\
    go build\
    -mod=vendor\
    -ldflags="-X 'main.ShortCommitTag=${SHORT_COMMIT_TAG}' -X 'main.Version=${RELEASE}' -X 'main.Date=$(date)' -X 'main.GoVersion=${GO_VERSION}' -X 'main.BuildTimestamp=$(date +%s)'"\
    -buildvcs=true\
    -tags=true\
    -o=kstudio\
    .

FROM ${DOCKERHUB_MIRROR}/alpine:3.22 AS build-binaries

ARG HELM_VERSION="3.18.3"
ARG PRIVATE_MIRROR=""
RUN wget --no-check-certificate "https://${PRIVATE_MIRROR}get.helm.sh/helm-v${HELM_VERSION}-linux-amd64.tar.gz" &&\
    tar xzvf helm-v${HELM_VERSION}-linux-amd64.tar.gz &&\
    mkdir -p /usr/local/helm/bin &&\
    mv linux-amd64/helm /usr/local/helm/bin/helm &&\
    rm -rf "helm-v${HELM_VERSION}-linux-amd64.tar.gz"

FROM ${DOCKERHUB_MIRROR}/node:22 AS build-ui

ENV NODE_EXTRA_CA_CERTS="/usr/local/share/ca-certificates/ca.crt"
COPY --from=truststore /tmp/ca.crt /usr/local/share/ca-certificates/ca.crt
RUN update-ca-certificates

WORKDIR /app
ENV PATH="/app/node_modules/.bin:${PATH}"

ARG YARN_REGISTRY=""
ARG PNPM_VERSION="10.13.1"
RUN YARN_REGISTRY=${YARN_REGISTRY} NPM_CONFIG_REGISTRY=${YARN_REGISTRY}\
        yarn add global pnpm@${PNPM_VERSION}
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/front ./apps/front
RUN YARN_REGISTRY=${YARN_REGISTRY} NPM_CONFIG_REGISTRY=${YARN_REGISTRY}\
      pnpm install --frozen-lockfile -F front
# TODO: lint should be a step in CI
# RUN pnpm -F front lint
# RUN pnpm -F front typecheck:front
RUN pnpm -F front build

FROM ${GOOGLEHUB_MIRROR}/distroless/base-debian12:nonroot

WORKDIR /app

COPY --from=build-backend /app/kstudio kstudio
COPY --from=build-binaries /usr/local/helm/bin/ /usr/local/helm/bin/
COPY --from=build-ui /app/apps/front/dist/ public/
COPY ./apps/docs/index.html public/docs.html
COPY ./apps/docs/logo.svg public/public/
COPY ./apps/docs/redoc.standalone.js public/public/
COPY ./apps/backend/apps.yaml /app/apps.yaml

ENV PATH="/usr/local/helm/bin:${PATH}"

USER nonroot:nonroot

EXPOSE 8080

ENTRYPOINT ["/app/kstudio"]
